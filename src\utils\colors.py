# -*- coding: utf-8 -*-
"""
نظام الألوان الموحد للتطبيق
Unified Color System
"""

class AppColors:
    """نظام الألوان الموحد للتطبيق"""
    
    # الألوان الأساسية - نظام محسن ومتطور
    PRIMARY = '#2563eb'        # أزرق ملكي محسن
    SECONDARY = '#059669'      # أخضر طبيعي
    SUCCESS = '#16a34a'        # أخضر نجاح محسن
    WARNING = '#ea580c'        # برتقالي تحذير محسن
    DANGER = '#dc2626'         # أحمر خطر محسن
    INFO = '#0ea5e9'          # أزرق معلومات محسن
    ACCENT = '#8b5cf6'        # بنفسجي مميز
    PURPLE = '#7c3aed'        # بنفسجي داكن
    
    # الألوان المحايدة المحسنة
    WHITE = '#ffffff'          # أبيض نقي
    LIGHT_GRAY = '#f8fafc'     # رمادي فاتح جداً
    GRAY = '#e2e8f0'          # رمادي فاتح
    MEDIUM_GRAY = '#94a3b8'    # رمادي متوسط
    DARK_GRAY = '#64748b'      # رمادي داكن
    DARKER_GRAY = '#334155'    # رمادي أكثر قتامة
    BLACK = '#1e293b'         # أسود مزرق
    
    # ألوان الخلفية المحسنة
    BACKGROUND = WHITE         # خلفية رئيسية
    CARD_BACKGROUND = '#f8fafc'  # خلفية البطاقات محسنة
    SIDEBAR_BACKGROUND = '#f1f5f9'  # خلفية الشريط الجانبي محسنة
    SURFACE = '#fefefe'        # سطح مرتفع
    OVERLAY = 'rgba(0,0,0,0.1)'  # طبقة شفافة
    
    # ألوان النصوص
    TEXT_PRIMARY = DARKER_GRAY    # نص أساسي
    TEXT_SECONDARY = DARK_GRAY    # نص ثانوي
    TEXT_LIGHT = '#94a3b8'       # نص فاتح
    TEXT_WHITE = WHITE           # نص أبيض
    
    # ألوان الحدود
    BORDER_LIGHT = GRAY          # حدود فاتحة
    BORDER_DARK = DARK_GRAY      # حدود داكنة
    BORDER_INFO = INFO           # حدود معلومات
    BORDER_DANGER = DANGER       # حدود خطر
    
    # ألوان التفاعل المحسنة
    HOVER_PRIMARY = '#1d4ed8'    # تمرير أزرق
    HOVER_SUCCESS = '#15803d'    # تمرير أخضر محسن
    HOVER_DANGER = '#b91c1c'     # تمرير أحمر محسن
    HOVER_WARNING = '#c2410c'    # تمرير برتقالي
    HOVER_INFO = '#0284c7'       # تمرير أزرق معلومات
    HOVER_ACCENT = '#7c3aed'     # تمرير بنفسجي
    
    # ألوان الحالة المحسنة
    ACTIVE = SUCCESS             # نشط
    INACTIVE = MEDIUM_GRAY       # غير نشط
    DISABLED = GRAY              # معطل
    SELECTED = ACCENT            # محدد
    FOCUS = INFO                 # تركيز
    
    @classmethod
    def get_color_scheme(cls):
        """الحصول على مخطط الألوان الكامل المحسن"""
        return {
            # الألوان الأساسية
            'primary': cls.PRIMARY,
            'secondary': cls.SECONDARY,
            'success': cls.SUCCESS,
            'warning': cls.WARNING,
            'danger': cls.DANGER,
            'info': cls.INFO,
            'accent': cls.ACCENT,
            'purple': cls.PURPLE,
            
            # الألوان المحايدة
            'white': cls.WHITE,
            'light': cls.LIGHT_GRAY,
            'gray': cls.GRAY,
            'medium_gray': cls.MEDIUM_GRAY,
            'dark': cls.DARK_GRAY,
            'darker': cls.DARKER_GRAY,
            'black': cls.BLACK,
            
            # ألوان الخلفية
            'background': cls.BACKGROUND,
            'card': cls.CARD_BACKGROUND,
            'sidebar': cls.SIDEBAR_BACKGROUND,
            'surface': cls.SURFACE,
            'overlay': cls.OVERLAY,
            
            # ألوان النصوص
            'text': cls.TEXT_PRIMARY,
            'text_secondary': cls.TEXT_SECONDARY,
            'text_light': cls.TEXT_LIGHT,
            'text_white': cls.TEXT_WHITE,
            
            # ألوان الحدود
            'border': cls.BORDER_LIGHT,
            'border_dark': cls.BORDER_DARK,
            'border_info': cls.BORDER_INFO,
            'border_danger': cls.BORDER_DANGER,
            
            # ألوان التفاعل
            'hover_primary': cls.HOVER_PRIMARY,
            'hover_success': cls.HOVER_SUCCESS,
            'hover_danger': cls.HOVER_DANGER,
            'hover_warning': cls.HOVER_WARNING,
            'hover_info': cls.HOVER_INFO,
            'hover_accent': cls.HOVER_ACCENT,
            
            # ألوان الحالة
            'active': cls.ACTIVE,
            'inactive': cls.INACTIVE,
            'disabled': cls.DISABLED,
            'selected': cls.SELECTED,
            'focus': cls.FOCUS
        }
    
    @classmethod
    def get_button_colors(cls, button_type='primary'):
        """الحصول على ألوان الأزرار حسب النوع المحسن"""
        colors = {
            'primary': {
                'bg': cls.PRIMARY,
                'fg': cls.TEXT_WHITE,
                'hover_bg': cls.HOVER_PRIMARY,
                'hover_fg': cls.TEXT_WHITE,
                'border': cls.PRIMARY,
                'active_bg': '#1e40af'
            },
            'success': {
                'bg': cls.SUCCESS,
                'fg': cls.TEXT_WHITE,
                'hover_bg': cls.HOVER_SUCCESS,
                'hover_fg': cls.TEXT_WHITE,
                'border': cls.SUCCESS,
                'active_bg': '#166534'
            },
            'danger': {
                'bg': cls.DANGER,
                'fg': cls.TEXT_WHITE,
                'hover_bg': cls.HOVER_DANGER,
                'hover_fg': cls.TEXT_WHITE,
                'border': cls.DANGER,
                'active_bg': '#991b1b'
            },
            'warning': {
                'bg': cls.WARNING,
                'fg': cls.TEXT_WHITE,
                'hover_bg': cls.HOVER_WARNING,
                'hover_fg': cls.TEXT_WHITE,
                'border': cls.WARNING,
                'active_bg': '#9a3412'
            },
            'info': {
                'bg': cls.INFO,
                'fg': cls.TEXT_WHITE,
                'hover_bg': cls.HOVER_INFO,
                'hover_fg': cls.TEXT_WHITE,
                'border': cls.INFO,
                'active_bg': '#0369a1'
            },
            'accent': {
                'bg': cls.ACCENT,
                'fg': cls.TEXT_WHITE,
                'hover_bg': cls.HOVER_ACCENT,
                'hover_fg': cls.TEXT_WHITE,
                'border': cls.ACCENT,
                'active_bg': '#6d28d9'
            },
            'secondary': {
                'bg': cls.SECONDARY,
                'fg': cls.TEXT_PRIMARY,
                'hover_bg': cls.GRAY,
                'hover_fg': cls.TEXT_PRIMARY,
                'border': cls.BORDER_LIGHT,
                'active_bg': cls.MEDIUM_GRAY
            },
            'outline': {
                'bg': 'transparent',
                'fg': cls.PRIMARY,
                'hover_bg': cls.PRIMARY,
                'hover_fg': cls.TEXT_WHITE,
                'border': cls.PRIMARY,
                'active_bg': cls.HOVER_PRIMARY
            },
            'ghost': {
                'bg': 'transparent',
                'fg': cls.TEXT_PRIMARY,
                'hover_bg': cls.LIGHT_GRAY,
                'hover_fg': cls.TEXT_PRIMARY,
                'border': 'transparent',
                'active_bg': cls.GRAY
            }
        }
        return colors.get(button_type, colors['primary'])
    
    @classmethod
    def get_status_color(cls, status):
        """الحصول على لون الحالة"""
        status_colors = {
            'active': cls.SUCCESS,
            'inactive': cls.DARK_GRAY,
            'pending': cls.WARNING,
            'error': cls.DANGER,
            'success': cls.SUCCESS,
            'warning': cls.WARNING,
            'info': cls.INFO,
            'low_stock': cls.WARNING,
            'out_of_stock': cls.DANGER,
            'in_stock': cls.SUCCESS
        }
        
        return status_colors.get(status, cls.TEXT_PRIMARY)

    # أنظمة الألوان المتعددة
    COLOR_THEMES = {
        'light': {
            'primary': PRIMARY,
            'secondary': SECONDARY,
            'success': SUCCESS,
            'warning': WARNING,
            'danger': DANGER,
            'info': INFO,
            'background': BACKGROUND,
            'card': CARD_BACKGROUND,
            'sidebar': SIDEBAR_BACKGROUND,
            'text': TEXT_PRIMARY,
            'text_secondary': TEXT_SECONDARY,
            'border': BORDER_LIGHT,
            'hover_primary': HOVER_PRIMARY
        },
        'dark': {
            'primary': '#3b82f6',      # أزرق فاتح
            'secondary': '#10b981',    # أخضر فاتح
            'success': '#22c55e',      # أخضر نجاح فاتح
            'warning': '#f59e0b',      # برتقالي
            'danger': '#ef4444',       # أحمر
            'info': '#06b6d4',         # سماوي
            'background': '#0f172a',   # خلفية داكنة
            'card': '#1e293b',         # بطاقات داكنة
            'sidebar': '#334155',      # شريط جانبي داكن
            'text': '#f8fafc',         # نص فاتح
            'text_secondary': '#cbd5e1', # نص ثانوي فاتح
            'border': '#475569',       # حدود داكنة
            'hover_primary': '#2563eb' # تمرير أزرق داكن
        },
        'blue': {
            'primary': '#1e40af',      # أزرق داكن
            'secondary': '#1565c0',    # أزرق متوسط
            'success': '#16a34a',      # أخضر
            'warning': '#ea580c',      # برتقالي
            'danger': '#dc2626',       # أحمر
            'info': '#0284c7',         # أزرق فاتح
            'background': '#f0f9ff',   # خلفية زرقاء فاتحة
            'card': '#ffffff',         # بطاقات بيضاء
            'sidebar': '#e0f2fe',      # شريط جانبي أزرق فاتح
            'text': '#0c4a6e',         # نص أزرق داكن
            'text_secondary': '#0369a1', # نص ثانوي أزرق
            'border': '#bae6fd',       # حدود زرقاء فاتحة
            'hover_primary': '#1d4ed8' # تمرير أزرق
        }
    }

    @classmethod
    def get_theme_colors(cls, theme='light'):
        """الحصول على ألوان موضوع معين"""
        return cls.COLOR_THEMES.get(theme, cls.COLOR_THEMES['light'])

    @classmethod
    def get_available_themes(cls):
        """الحصول على قائمة المواضيع المتاحة"""
        return list(cls.COLOR_THEMES.keys())

    @classmethod
    def apply_theme(cls, theme='light'):
        """تطبيق موضوع ألوان معين"""
        theme_colors = cls.get_theme_colors(theme)

        # تحديث الألوان الحالية
        cls.PRIMARY = theme_colors['primary']
        cls.SECONDARY = theme_colors['secondary']
        cls.SUCCESS = theme_colors['success']
        cls.WARNING = theme_colors['warning']
        cls.DANGER = theme_colors['danger']
        cls.INFO = theme_colors['info']
        cls.BACKGROUND = theme_colors['background']
        cls.CARD_BACKGROUND = theme_colors['card']
        cls.SIDEBAR_BACKGROUND = theme_colors['sidebar']
        cls.TEXT_PRIMARY = theme_colors['text']
        cls.TEXT_SECONDARY = theme_colors['text_secondary']
        cls.BORDER_LIGHT = theme_colors['border']
        cls.HOVER_PRIMARY = theme_colors['hover_primary']

        return theme_colors
