# -*- coding: utf-8 -*-
"""
نافذة إدارة النسخ الاحتياطية
Backup Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import threading
from typing import Optional

from ..utils.colors import AppColors
from ..utils.backup_manager import BackupManager

class BackupWindow:
    """نافذة إدارة النسخ الاحتياطية"""
    
    def __init__(self, parent, db_manager, config, current_user):
        """تهيئة نافذة النسخ الاحتياطية"""
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.current_user = current_user
        self.backup_manager = BackupManager(db_manager, config)
        
        # التحقق من الصلاحيات
        if not self.current_user.has_permission("manage_backups"):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة النسخ الاحتياطية")
            return
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.load_backups()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("💾 إدارة النسخ الاحتياطية")
        self.window.geometry("1000x600")
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم أقل من الحد الأدنى
        self.window.minsize(800, 500)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # شريط الأدوات
        self.create_toolbar(main_container)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_container, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # قائمة النسخ الاحتياطية
        self.create_backup_list(content_frame)
        
        # لوحة المعلومات
        self.create_info_panel(content_frame)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان (على اليمين للعربية)
        title_label = tk.Label(header_frame,
                              text="💾 إدارة النسخ الاحتياطية",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.RIGHT)
        
        # زر الإغلاق (على اليسار للعربية)
        close_btn = tk.Button(header_frame,
                             text="✕ إغلاق",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['danger'],
                             relief='flat',
                             padx=20,
                             pady=8,
                             command=self.window.destroy)
        close_btn.pack(side=tk.LEFT)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        toolbar_frame.pack(fill=tk.X, pady=(0, 20))
        
        # الجانب الأيمن - معلومات (للعربية)
        info_frame = tk.Frame(toolbar_frame, bg=self.colors['card'])
        info_frame.pack(side=tk.RIGHT, padx=15, pady=15)
        
        # عدد النسخ الاحتياطية
        self.backup_count_label = tk.Label(info_frame,
                                          text="📊 عدد النسخ: 0",
                                          font=('Segoe UI', 11),
                                          fg=self.colors['text'],
                                          bg=self.colors['card'])
        self.backup_count_label.pack(side=tk.RIGHT)
        
        # الجانب الأيسر - الأزرار (للعربية)
        buttons_frame = tk.Frame(toolbar_frame, bg=self.colors['card'])
        buttons_frame.pack(side=tk.LEFT, padx=15, pady=15)
        
        # أزرار العمليات
        buttons = [
            ("💾 نسخة احتياطية جديدة", self.create_backup, self.colors['primary']),
            ("📂 استعادة نسخة", self.restore_backup, self.colors['success']),
            ("🗑️ حذف نسخة", self.delete_backup, self.colors['danger']),
            ("🔄 تحديث", self.load_backups, self.colors['secondary']),
            ("🧹 تنظيف النسخ القديمة", self.cleanup_old_backups, self.colors['warning'])
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(buttons_frame,
                           text=text,
                           font=('Segoe UI', 10),
                           fg='white',
                           bg=color,
                           relief='flat',
                           padx=15,
                           pady=8,
                           command=command)
            btn.pack(side=tk.LEFT, padx=(0, 5))
    
    def create_backup_list(self, parent):
        """إنشاء قائمة النسخ الاحتياطية"""
        # إطار القائمة (على اليسار)
        list_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عنوان القائمة
        title_frame = tk.Frame(list_frame, bg=self.colors['primary'])
        title_frame.pack(fill=tk.X)
        
        title_label = tk.Label(title_frame,
                              text="📋 قائمة النسخ الاحتياطية",
                              font=('Segoe UI', 12, 'bold'),
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(side=tk.RIGHT, padx=15, pady=8)
        
        # جدول النسخ الاحتياطية
        table_frame = tk.Frame(list_frame, bg=self.colors['card'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # إعداد الجدول
        columns = ("اسم النسخة", "النوع", "الحجم", "التاريخ", "الحالة")
        self.backups_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.backups_tree.heading(col, text=col, anchor='center')
            self.backups_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.backups_tree.yview)
        self.backups_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.backups_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث التحديد
        self.backups_tree.bind('<<TreeviewSelect>>', self.on_backup_select)
    
    def create_info_panel(self, parent):
        """إنشاء لوحة المعلومات"""
        # إطار المعلومات (على اليمين)
        info_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        info_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        info_frame.configure(width=300)
        
        # عنوان اللوحة
        title_frame = tk.Frame(info_frame, bg=self.colors['success'])
        title_frame.pack(fill=tk.X)
        
        title_label = tk.Label(title_frame,
                              text="📊 معلومات النسخة",
                              font=('Segoe UI', 12, 'bold'),
                              fg='white',
                              bg=self.colors['success'])
        title_label.pack(side=tk.RIGHT, padx=15, pady=8)
        
        # محتوى المعلومات
        content_frame = tk.Frame(info_frame, bg=self.colors['card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # معلومات النسخة المحددة
        self.info_text = tk.Text(content_frame,
                                font=('Segoe UI', 10),
                                bg=self.colors['light'],
                                fg=self.colors['text'],
                                relief='flat',
                                wrap=tk.WORD,
                                state=tk.DISABLED)
        self.info_text.pack(fill=tk.BOTH, expand=True)
        
        # إعدادات النسخ الاحتياطي التلقائي
        settings_frame = tk.LabelFrame(content_frame,
                                      text="⚙️ إعدادات النسخ التلقائي",
                                      font=('Segoe UI', 10, 'bold'),
                                      fg=self.colors['text'],
                                      bg=self.colors['card'])
        settings_frame.pack(fill=tk.X, pady=(10, 0))
        
        # تفعيل النسخ التلقائي
        self.auto_backup_var = tk.BooleanVar()
        auto_check = tk.Checkbutton(settings_frame,
                                   text="تفعيل النسخ التلقائي",
                                   variable=self.auto_backup_var,
                                   font=('Segoe UI', 9),
                                   fg=self.colors['text'],
                                   bg=self.colors['card'],
                                   command=self.toggle_auto_backup)
        auto_check.pack(anchor='e', padx=10, pady=5)
        
        # تكرار النسخ
        freq_frame = tk.Frame(settings_frame, bg=self.colors['card'])
        freq_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(freq_frame,
                text="التكرار:",
                font=('Segoe UI', 9),
                fg=self.colors['text'],
                bg=self.colors['card']).pack(side=tk.RIGHT)
        
        self.frequency_var = tk.StringVar()
        freq_combo = ttk.Combobox(freq_frame,
                                 textvariable=self.frequency_var,
                                 values=["daily", "weekly", "monthly"],
                                 state="readonly",
                                 width=15)
        freq_combo.pack(side=tk.RIGHT, padx=(0, 10))
        
        # مدة الاحتفاظ
        retention_frame = tk.Frame(settings_frame, bg=self.colors['card'])
        retention_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(retention_frame,
                text="الاحتفاظ (يوم):",
                font=('Segoe UI', 9),
                fg=self.colors['text'],
                bg=self.colors['card']).pack(side=tk.RIGHT)
        
        self.retention_var = tk.StringVar()
        retention_entry = tk.Entry(retention_frame,
                                  textvariable=self.retention_var,
                                  font=('Segoe UI', 9),
                                  width=10)
        retention_entry.pack(side=tk.RIGHT, padx=(0, 10))
        
        # زر حفظ الإعدادات
        save_settings_btn = tk.Button(settings_frame,
                                     text="💾 حفظ الإعدادات",
                                     font=('Segoe UI', 9),
                                     fg='white',
                                     bg=self.colors['primary'],
                                     relief='flat',
                                     padx=10,
                                     pady=5,
                                     command=self.save_backup_settings)
        save_settings_btn.pack(pady=10)
        
        # تحميل الإعدادات الحالية
        self.load_backup_settings()
    
    def load_backups(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            # مسح الجدول
            for item in self.backups_tree.get_children():
                self.backups_tree.delete(item)
            
            # جلب النسخ الاحتياطية
            backups = self.backup_manager.get_backup_list()
            
            # إضافة النسخ للجدول
            for backup in backups:
                backup_type = "يدوي" if backup['backup_type'] == 'manual' else "تلقائي"
                
                # تحويل الحجم إلى وحدة مناسبة
                size_mb = backup['backup_size'] / (1024 * 1024) if backup['backup_size'] else 0
                size_text = f"{size_mb:.1f} MB"
                
                # تنسيق التاريخ
                date_obj = datetime.fromisoformat(backup['created_at'].replace('Z', '+00:00'))
                date_text = date_obj.strftime("%Y-%m-%d %H:%M")
                
                # حالة النسخة
                status_map = {
                    'completed': 'مكتملة',
                    'in_progress': 'قيد التنفيذ',
                    'failed': 'فشلت'
                }
                status_text = status_map.get(backup['status'], backup['status'])
                
                self.backups_tree.insert('', 'end', values=(
                    backup['backup_name'],
                    backup_type,
                    size_text,
                    date_text,
                    status_text
                ), tags=(backup['id'],))
            
            # تحديث عدد النسخ
            self.backup_count_label.config(text=f"📊 عدد النسخ: {len(backups)}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل النسخ الاحتياطية: {str(e)}")
    
    def on_backup_select(self, event):
        """عند تحديد نسخة احتياطية"""
        selection = self.backups_tree.selection()
        if selection:
            item = self.backups_tree.item(selection[0])
            backup_id = item['tags'][0]
            
            # البحث عن النسخة الاحتياطية
            backups = self.backup_manager.get_backup_list()
            for backup in backups:
                if backup['id'] == backup_id:
                    self.show_backup_info(backup)
                    break
    
    def show_backup_info(self, backup):
        """عرض معلومات النسخة الاحتياطية"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        
        # تنسيق المعلومات
        info = f"""اسم النسخة: {backup['backup_name']}

النوع: {'يدوي' if backup['backup_type'] == 'manual' else 'تلقائي'}

الحجم: {backup['backup_size'] / (1024 * 1024):.1f} MB

التاريخ: {backup['created_at']}

الحالة: {backup['status']}

المسار: {backup['backup_path']}

المستخدم: {backup.get('username', 'غير محدد')}
"""
        
        self.info_text.insert(1.0, info)
        self.info_text.config(state=tk.DISABLED)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية جديدة"""
        def backup_thread():
            try:
                result = self.backup_manager.create_backup("manual", self.current_user.id)
                
                # تحديث الواجهة في الخيط الرئيسي
                self.window.after(0, lambda: self.on_backup_complete(result))
                
            except Exception as e:
                self.window.after(0, lambda: messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}"))
        
        # تشغيل النسخ الاحتياطي في خيط منفصل
        threading.Thread(target=backup_thread, daemon=True).start()
        
        # عرض رسالة انتظار
        messagebox.showinfo("معلومات", "جاري إنشاء النسخة الاحتياطية...")
    
    def on_backup_complete(self, result):
        """عند اكتمال النسخ الاحتياطي"""
        if result["success"]:
            messagebox.showinfo("نجح", result["message"])
            self.load_backups()
        else:
            messagebox.showerror("خطأ", result["message"])
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        # اختيار ملف النسخة الاحتياطية
        file_path = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("ملفات النسخ الاحتياطية", "*.zip"), ("جميع الملفات", "*.*")]
        )
        
        if file_path:
            if messagebox.askyesno("تأكيد الاستعادة", 
                                  "هل تريد استعادة هذه النسخة الاحتياطية؟\n"
                                  "سيتم إنشاء نسخة احتياطية من الحالة الحالية أولاً."):
                
                def restore_thread():
                    try:
                        result = self.backup_manager.restore_backup(file_path, self.current_user.id)
                        self.window.after(0, lambda: self.on_restore_complete(result))
                    except Exception as e:
                        self.window.after(0, lambda: messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}"))
                
                threading.Thread(target=restore_thread, daemon=True).start()
                messagebox.showinfo("معلومات", "جاري استعادة النسخة الاحتياطية...")
    
    def on_restore_complete(self, result):
        """عند اكتمال الاستعادة"""
        if result["success"]:
            messagebox.showinfo("نجح", result["message"])
            self.load_backups()
        else:
            messagebox.showerror("خطأ", result["message"])
    
    def delete_backup(self):
        """حذف نسخة احتياطية"""
        selection = self.backups_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للحذف")
            return
        
        item = self.backups_tree.item(selection[0])
        backup_id = item['tags'][0]
        backup_name = item['values'][0]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف النسخة الاحتياطية:\n{backup_name}؟"):
            result = self.backup_manager.delete_backup(backup_id, self.current_user.id)
            
            if result["success"]:
                messagebox.showinfo("نجح", result["message"])
                self.load_backups()
            else:
                messagebox.showerror("خطأ", result["message"])
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        if messagebox.askyesno("تأكيد التنظيف", "هل تريد حذف النسخ الاحتياطية القديمة؟"):
            result = self.backup_manager.cleanup_old_backups()
            
            if result["success"]:
                messagebox.showinfo("نجح", result["message"])
                self.load_backups()
            else:
                messagebox.showerror("خطأ", result["message"])
    
    def load_backup_settings(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        self.auto_backup_var.set(self.db.get_setting("auto_backup_enabled", True))
        self.frequency_var.set(self.db.get_setting("backup_frequency", "daily"))
        self.retention_var.set(str(self.db.get_setting("backup_retention_days", 30)))
    
    def save_backup_settings(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            self.db.set_setting("auto_backup_enabled", self.auto_backup_var.get(), "boolean")
            self.db.set_setting("backup_frequency", self.frequency_var.get(), "string")
            self.db.set_setting("backup_retention_days", int(self.retention_var.get()), "number")
            
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def toggle_auto_backup(self):
        """تبديل النسخ الاحتياطي التلقائي"""
        if self.auto_backup_var.get():
            self.backup_manager.start_automatic_backup()
        else:
            self.backup_manager.stop_automatic_backup()
