# -*- coding: utf-8 -*-
"""
نافذة تصدير البيانات
Data Export Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import threading
from typing import Optional

from ..utils.colors import AppColors
from ..utils.export_manager import ExportManager

class ExportWindow:
    """نافذة تصدير البيانات"""
    
    def __init__(self, parent, db_manager, config, current_user):
        """تهيئة نافذة التصدير"""
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.current_user = current_user
        self.export_manager = ExportManager(db_manager)
        
        # التحقق من الصلاحيات
        if not self.current_user.has_permission("export_reports"):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لتصدير البيانات")
            return
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📤 تصدير البيانات")
        self.window.geometry("800x600")
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم أقل من الحد الأدنى
        self.window.minsize(700, 500)
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.export_type_var = tk.StringVar(value="products")
        self.format_var = tk.StringVar(value="CSV")
        self.include_date_filter_var = tk.BooleanVar(value=False)
        self.start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        self.end_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_container, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # خيارات التصدير
        self.create_export_options(content_frame)
        
        # خيارات التاريخ
        self.create_date_options(content_frame)
        
        # أزرار العمليات
        self.create_action_buttons(content_frame)
        
        # منطقة النتائج
        self.create_results_area(content_frame)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان (على اليمين للعربية)
        title_label = tk.Label(header_frame,
                              text="📤 تصدير البيانات",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.RIGHT)
        
        # زر الإغلاق (على اليسار للعربية)
        close_btn = tk.Button(header_frame,
                             text="✕ إغلاق",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['danger'],
                             relief='flat',
                             padx=20,
                             pady=8,
                             command=self.window.destroy)
        close_btn.pack(side=tk.LEFT)
    
    def create_export_options(self, parent):
        """إنشاء خيارات التصدير"""
        options_frame = tk.LabelFrame(parent,
                                     text="⚙️ خيارات التصدير",
                                     font=('Segoe UI', 12, 'bold'),
                                     fg=self.colors['text'],
                                     bg=self.colors['background'])
        options_frame.pack(fill=tk.X, pady=(0, 20))
        
        # إطار المحتوى
        content_frame = tk.Frame(options_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # نوع البيانات
        data_type_frame = tk.Frame(content_frame, bg=self.colors['background'])
        data_type_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(data_type_frame,
                text="📊 نوع البيانات:",
                font=('Segoe UI', 11, 'bold'),
                fg=self.colors['text'],
                bg=self.colors['background']).pack(side=tk.RIGHT)
        
        # خيارات نوع البيانات
        data_types = [
            ("products", "📦 المنتجات"),
            ("customers", "👥 العملاء"),
            ("suppliers", "🏭 الموردين"),
            ("invoices", "📄 الفواتير"),
            ("all", "🗂️ جميع البيانات")
        ]
        
        for value, text in data_types:
            rb = tk.Radiobutton(data_type_frame,
                               text=text,
                               variable=self.export_type_var,
                               value=value,
                               font=('Segoe UI', 10),
                               fg=self.colors['text'],
                               bg=self.colors['background'],
                               selectcolor=self.colors['background'],
                               command=self.on_export_type_change)
            rb.pack(side=tk.RIGHT, padx=(0, 15))
        
        # نوع الملف
        format_frame = tk.Frame(content_frame, bg=self.colors['background'])
        format_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(format_frame,
                text="📁 نوع الملف:",
                font=('Segoe UI', 11, 'bold'),
                fg=self.colors['text'],
                bg=self.colors['background']).pack(side=tk.RIGHT)
        
        # خيارات نوع الملف
        formats = [("CSV", "CSV"), ("JSON", "JSON"), ("EXCEL", "Excel")]
        
        for value, text in formats:
            rb = tk.Radiobutton(format_frame,
                               text=text,
                               variable=self.format_var,
                               value=value,
                               font=('Segoe UI', 10),
                               fg=self.colors['text'],
                               bg=self.colors['background'],
                               selectcolor=self.colors['background'])
            rb.pack(side=tk.RIGHT, padx=(0, 15))
    
    def create_date_options(self, parent):
        """إنشاء خيارات التاريخ"""
        self.date_frame = tk.LabelFrame(parent,
                                       text="📅 فلترة التاريخ (للفواتير فقط)",
                                       font=('Segoe UI', 12, 'bold'),
                                       fg=self.colors['text'],
                                       bg=self.colors['background'])
        self.date_frame.pack(fill=tk.X, pady=(0, 20))
        
        # إطار المحتوى
        content_frame = tk.Frame(self.date_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # تفعيل فلترة التاريخ
        filter_check = tk.Checkbutton(content_frame,
                                     text="تفعيل فلترة التاريخ",
                                     variable=self.include_date_filter_var,
                                     font=('Segoe UI', 10),
                                     fg=self.colors['text'],
                                     bg=self.colors['background'],
                                     selectcolor=self.colors['background'],
                                     command=self.toggle_date_filter)
        filter_check.pack(anchor='e', pady=(0, 10))
        
        # إطار التواريخ
        dates_frame = tk.Frame(content_frame, bg=self.colors['background'])
        dates_frame.pack(fill=tk.X)
        
        # تاريخ البداية
        start_frame = tk.Frame(dates_frame, bg=self.colors['background'])
        start_frame.pack(side=tk.RIGHT, padx=(0, 20))
        
        tk.Label(start_frame,
                text="من تاريخ:",
                font=('Segoe UI', 10),
                fg=self.colors['text'],
                bg=self.colors['background']).pack(side=tk.RIGHT)
        
        self.start_date_entry = tk.Entry(start_frame,
                                        textvariable=self.start_date_var,
                                        font=('Segoe UI', 10),
                                        width=12,
                                        state=tk.DISABLED)
        self.start_date_entry.pack(side=tk.RIGHT, padx=(0, 10))
        
        # تاريخ النهاية
        end_frame = tk.Frame(dates_frame, bg=self.colors['background'])
        end_frame.pack(side=tk.RIGHT)
        
        tk.Label(end_frame,
                text="إلى تاريخ:",
                font=('Segoe UI', 10),
                fg=self.colors['text'],
                bg=self.colors['background']).pack(side=tk.RIGHT)
        
        self.end_date_entry = tk.Entry(end_frame,
                                      textvariable=self.end_date_var,
                                      font=('Segoe UI', 10),
                                      width=12,
                                      state=tk.DISABLED)
        self.end_date_entry.pack(side=tk.RIGHT, padx=(0, 10))
        
        # إخفاء خيارات التاريخ في البداية
        self.date_frame.pack_forget()
    
    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = tk.Frame(parent, bg=self.colors['background'])
        buttons_frame.pack(fill=tk.X, pady=(0, 20))
        
        # زر التصدير
        export_btn = tk.Button(buttons_frame,
                              text="📤 بدء التصدير",
                              font=('Segoe UI', 12, 'bold'),
                              fg='white',
                              bg=self.colors['primary'],
                              relief='flat',
                              padx=30,
                              pady=10,
                              command=self.start_export)
        export_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        # زر المعاينة
        preview_btn = tk.Button(buttons_frame,
                               text="👁️ معاينة البيانات",
                               font=('Segoe UI', 11),
                               fg='white',
                               bg=self.colors['secondary'],
                               relief='flat',
                               padx=20,
                               pady=10,
                               command=self.preview_data)
        preview_btn.pack(side=tk.RIGHT, padx=(0, 10))
    
    def create_results_area(self, parent):
        """إنشاء منطقة النتائج"""
        results_frame = tk.LabelFrame(parent,
                                     text="📋 نتائج التصدير",
                                     font=('Segoe UI', 12, 'bold'),
                                     fg=self.colors['text'],
                                     bg=self.colors['background'])
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # منطقة النص
        text_frame = tk.Frame(results_frame, bg=self.colors['background'])
        text_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        self.results_text = tk.Text(text_frame,
                                   font=('Segoe UI', 10),
                                   bg=self.colors['card'],
                                   fg=self.colors['text'],
                                   relief='flat',
                                   wrap=tk.WORD,
                                   state=tk.DISABLED)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط النص والشريط
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # رسالة ترحيبية
        self.add_result_message("مرحباً بك في نافذة تصدير البيانات\nاختر نوع البيانات ونوع الملف ثم اضغط 'بدء التصدير'")
    
    def on_export_type_change(self):
        """عند تغيير نوع التصدير"""
        export_type = self.export_type_var.get()
        
        if export_type == "invoices":
            self.date_frame.pack(fill=tk.X, pady=(0, 20))
        else:
            self.date_frame.pack_forget()
    
    def toggle_date_filter(self):
        """تبديل فلترة التاريخ"""
        if self.include_date_filter_var.get():
            self.start_date_entry.config(state=tk.NORMAL)
            self.end_date_entry.config(state=tk.NORMAL)
        else:
            self.start_date_entry.config(state=tk.DISABLED)
            self.end_date_entry.config(state=tk.DISABLED)
    
    def preview_data(self):
        """معاينة البيانات"""
        export_type = self.export_type_var.get()
        
        try:
            if export_type == "products":
                data = self.db.fetch_all("SELECT COUNT(*) as count FROM products")
                count = data[0]['count'] if data else 0
                self.add_result_message(f"📦 عدد المنتجات: {count}")
                
            elif export_type == "customers":
                data = self.db.fetch_all("SELECT COUNT(*) as count FROM customers")
                count = data[0]['count'] if data else 0
                self.add_result_message(f"👥 عدد العملاء: {count}")
                
            elif export_type == "suppliers":
                data = self.db.fetch_all("SELECT COUNT(*) as count FROM suppliers")
                count = data[0]['count'] if data else 0
                self.add_result_message(f"🏭 عدد الموردين: {count}")
                
            elif export_type == "invoices":
                query = "SELECT COUNT(*) as count FROM invoices"
                params = []
                
                if self.include_date_filter_var.get():
                    query += " WHERE invoice_date BETWEEN ? AND ?"
                    params = [self.start_date_var.get(), self.end_date_var.get()]
                
                data = self.db.fetch_all(query, params)
                count = data[0]['count'] if data else 0
                self.add_result_message(f"📄 عدد الفواتير: {count}")
                
            elif export_type == "all":
                stats = self.db.get_database_stats()
                message = "📊 إحصائيات قاعدة البيانات:\n"
                message += f"المنتجات: {stats.get('products_count', 0)}\n"
                message += f"العملاء: {stats.get('customers_count', 0)}\n"
                message += f"الموردين: {stats.get('suppliers_count', 0)}\n"
                message += f"الفواتير: {stats.get('invoices_count', 0)}\n"
                self.add_result_message(message)
                
        except Exception as e:
            self.add_result_message(f"❌ خطأ في معاينة البيانات: {str(e)}")
    
    def start_export(self):
        """بدء عملية التصدير"""
        export_type = self.export_type_var.get()
        format_type = self.format_var.get()
        
        self.add_result_message(f"🚀 بدء تصدير {export_type} بصيغة {format_type}...")
        
        def export_thread():
            try:
                result = None
                
                if export_type == "products":
                    result = self.export_manager.export_products(format_type)
                elif export_type == "customers":
                    result = self.export_manager.export_customers(format_type)
                elif export_type == "suppliers":
                    result = self.export_manager.export_suppliers(format_type)
                elif export_type == "invoices":
                    start_date = self.start_date_var.get() if self.include_date_filter_var.get() else None
                    end_date = self.end_date_var.get() if self.include_date_filter_var.get() else None
                    result = self.export_manager.export_invoices(format_type, None, start_date, end_date)
                elif export_type == "all":
                    result = self.export_manager.export_all_data(format_type)
                
                # تحديث الواجهة في الخيط الرئيسي
                self.window.after(0, lambda: self.on_export_complete(result))
                
            except Exception as e:
                self.window.after(0, lambda: self.on_export_error(str(e)))
        
        # تشغيل التصدير في خيط منفصل
        threading.Thread(target=export_thread, daemon=True).start()
    
    def on_export_complete(self, result):
        """عند اكتمال التصدير"""
        if result and result["success"]:
            message = f"✅ {result['message']}\n"
            if "file_path" in result:
                message += f"📁 المسار: {result['file_path']}\n"
            if "records_count" in result:
                message += f"📊 عدد السجلات: {result['records_count']}\n"
            if "export_folder" in result:
                message += f"📂 مجلد التصدير: {result['export_folder']}\n"
            
            self.add_result_message(message)
            messagebox.showinfo("نجح", result["message"])
        else:
            error_msg = result["message"] if result else "فشل في التصدير"
            self.add_result_message(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)
    
    def on_export_error(self, error_message):
        """عند حدوث خطأ في التصدير"""
        self.add_result_message(f"❌ خطأ في التصدير: {error_message}")
        messagebox.showerror("خطأ", f"فشل في التصدير: {error_message}")
    
    def add_result_message(self, message):
        """إضافة رسالة إلى منطقة النتائج"""
        self.results_text.config(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.results_text.insert(tk.END, f"[{timestamp}] {message}\n\n")
        self.results_text.see(tk.END)
        self.results_text.config(state=tk.DISABLED)
