# -*- coding: utf-8 -*-
"""
مدير الباركود
Barcode Manager
"""

import os
import tkinter as tk
from tkinter import messagebox, filedialog
from typing import Optional, Dict, Any
import uuid

class BarcodeManager:
    """مدير الباركود"""
    
    def __init__(self, db_manager):
        """تهيئة مدير الباركود"""
        self.db = db_manager
        self.barcode_library = None
        self.qr_library = None
        
        # محاولة استيراد مكتبات الباركود
        self._import_libraries()
    
    def _import_libraries(self):
        """استيراد مكتبات الباركود"""
        try:
            # محاولة استيراد python-barcode
            import barcode
            from barcode.writer import ImageWriter
            self.barcode_library = barcode
            self.barcode_writer = ImageWriter
        except ImportError:
            self.barcode_library = None
            print("تحذير: مكتبة python-barcode غير مثبتة")
        
        try:
            # محاولة استيراد qrcode
            import qrcode
            self.qr_library = qrcode
        except ImportError:
            self.qr_library = None
            print("تحذير: مكتبة qrcode غير مثبتة")
    
    def is_available(self) -> bool:
        """التحقق من توفر مكتبات الباركود"""
        return self.barcode_library is not None or self.qr_library is not None
    
    def get_missing_libraries(self) -> list:
        """الحصول على قائمة المكتبات المفقودة"""
        missing = []
        if self.barcode_library is None:
            missing.append("python-barcode")
        if self.qr_library is None:
            missing.append("qrcode[pil]")
        return missing
    
    def generate_product_barcode(self, product_id: int, barcode_type: str = "code128") -> Dict[str, Any]:
        """إنشاء باركود للمنتج"""
        try:
            if self.barcode_library is None:
                return {
                    "success": False,
                    "message": "مكتبة python-barcode غير مثبتة. يرجى تثبيتها: pip install python-barcode[images]"
                }
            
            # جلب بيانات المنتج
            product = self.db.fetch_one("SELECT * FROM products WHERE id = ?", (product_id,))
            if not product:
                return {"success": False, "message": "المنتج غير موجود"}
            
            # إنشاء رقم الباركود إذا لم يكن موجوداً
            if not product.get('barcode'):
                barcode_number = self.generate_barcode_number(product_id)
                self.db.execute("UPDATE products SET barcode = ? WHERE id = ?", 
                               (barcode_number, product_id))
            else:
                barcode_number = product['barcode']
            
            # إنشاء الباركود
            barcode_class = self.barcode_library.get_barcode_class(barcode_type)
            barcode_instance = barcode_class(barcode_number, writer=self.barcode_writer())
            
            # حفظ الباركود
            filename = f"barcode_{product_id}_{barcode_number}"
            filepath = os.path.join("barcodes", filename)
            os.makedirs("barcodes", exist_ok=True)
            
            barcode_path = barcode_instance.save(filepath)
            
            return {
                "success": True,
                "message": "تم إنشاء الباركود بنجاح",
                "barcode_number": barcode_number,
                "file_path": barcode_path,
                "product_name": product['name']
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في إنشاء الباركود: {str(e)}"}
    
    def generate_qr_code(self, data: str, filename: str = None) -> Dict[str, Any]:
        """إنشاء رمز QR"""
        try:
            if self.qr_library is None:
                return {
                    "success": False,
                    "message": "مكتبة qrcode غير مثبتة. يرجى تثبيتها: pip install qrcode[pil]"
                }
            
            # إنشاء رمز QR
            qr = self.qr_library.QRCode(
                version=1,
                error_correction=self.qr_library.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            # إنشاء الصورة
            img = qr.make_image(fill_color="black", back_color="white")
            
            # حفظ الصورة
            if not filename:
                filename = f"qr_{uuid.uuid4().hex[:8]}.png"
            
            filepath = os.path.join("qrcodes", filename)
            os.makedirs("qrcodes", exist_ok=True)
            
            img.save(filepath)
            
            return {
                "success": True,
                "message": "تم إنشاء رمز QR بنجاح",
                "file_path": filepath,
                "data": data
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في إنشاء رمز QR: {str(e)}"}
    
    def generate_product_qr(self, product_id: int) -> Dict[str, Any]:
        """إنشاء رمز QR للمنتج"""
        try:
            # جلب بيانات المنتج
            product = self.db.fetch_one("SELECT * FROM products WHERE id = ?", (product_id,))
            if not product:
                return {"success": False, "message": "المنتج غير موجود"}
            
            # إنشاء بيانات QR
            qr_data = {
                "type": "product",
                "id": product_id,
                "name": product['name'],
                "price": product['selling_price'],
                "barcode": product.get('barcode', ''),
                "category": product.get('category_id', '')
            }
            
            # تحويل البيانات إلى نص JSON
            import json
            qr_text = json.dumps(qr_data, ensure_ascii=False)
            
            # إنشاء رمز QR
            filename = f"product_qr_{product_id}.png"
            result = self.generate_qr_code(qr_text, filename)
            
            if result["success"]:
                result["product_name"] = product['name']
                result["qr_data"] = qr_data
            
            return result
            
        except Exception as e:
            return {"success": False, "message": f"فشل في إنشاء رمز QR للمنتج: {str(e)}"}
    
    def generate_barcode_number(self, product_id: int) -> str:
        """إنشاء رقم باركود للمنتج"""
        # استخدام معرف المنتج مع بادئة
        # يمكن تخصيص هذا حسب المتطلبات
        return f"2{str(product_id).zfill(11)}"  # EAN-13 format
    
    def scan_barcode(self, image_path: str) -> Dict[str, Any]:
        """قراءة الباركود من صورة"""
        try:
            # محاولة استيراد مكتبة pyzbar
            try:
                from pyzbar import pyzbar
                from PIL import Image
            except ImportError:
                return {
                    "success": False,
                    "message": "مكتبة pyzbar غير مثبتة. يرجى تثبيتها: pip install pyzbar pillow"
                }
            
            # قراءة الصورة
            image = Image.open(image_path)
            
            # البحث عن الباركود
            barcodes = pyzbar.decode(image)
            
            if not barcodes:
                return {"success": False, "message": "لم يتم العثور على باركود في الصورة"}
            
            results = []
            for barcode in barcodes:
                barcode_data = barcode.data.decode('utf-8')
                barcode_type = barcode.type
                
                # البحث عن المنتج في قاعدة البيانات
                product = self.db.fetch_one("SELECT * FROM products WHERE barcode = ?", (barcode_data,))
                
                result = {
                    "barcode": barcode_data,
                    "type": barcode_type,
                    "product": product
                }
                results.append(result)
            
            return {
                "success": True,
                "message": f"تم العثور على {len(results)} باركود",
                "barcodes": results
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في قراءة الباركود: {str(e)}"}
    
    def print_barcode(self, barcode_path: str) -> Dict[str, Any]:
        """طباعة الباركود"""
        try:
            import subprocess
            import platform
            
            system = platform.system()
            
            if system == "Windows":
                # فتح الصورة بالبرنامج الافتراضي للطباعة
                os.startfile(barcode_path, "print")
            elif system == "Darwin":  # macOS
                subprocess.run(["open", "-a", "Preview", barcode_path])
            else:  # Linux
                subprocess.run(["xdg-open", barcode_path])
            
            return {"success": True, "message": "تم إرسال الباركود للطباعة"}
            
        except Exception as e:
            return {"success": False, "message": f"فشل في طباعة الباركود: {str(e)}"}
    
    def batch_generate_barcodes(self, product_ids: list) -> Dict[str, Any]:
        """إنشاء باركود متعدد للمنتجات"""
        try:
            results = []
            successful = 0
            failed = 0
            
            for product_id in product_ids:
                result = self.generate_product_barcode(product_id)
                results.append({
                    "product_id": product_id,
                    "result": result
                })
                
                if result["success"]:
                    successful += 1
                else:
                    failed += 1
            
            return {
                "success": True,
                "message": f"تم إنشاء {successful} باركود بنجاح، فشل {failed}",
                "results": results,
                "successful": successful,
                "failed": failed
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في الإنشاء المتعدد: {str(e)}"}
    
    def get_barcode_types(self) -> list:
        """الحصول على أنواع الباركود المدعومة"""
        if self.barcode_library is None:
            return []
        
        return [
            "code128",
            "code39",
            "ean13",
            "ean8",
            "upca",
            "isbn13",
            "isbn10"
        ]
    
    def validate_barcode(self, barcode: str, barcode_type: str = "code128") -> bool:
        """التحقق من صحة الباركود"""
        try:
            if self.barcode_library is None:
                return False
            
            barcode_class = self.barcode_library.get_barcode_class(barcode_type)
            barcode_class(barcode)
            return True
            
        except:
            return False
    
    def search_product_by_barcode(self, barcode: str) -> Dict[str, Any]:
        """البحث عن منتج بالباركود"""
        try:
            product = self.db.fetch_one("SELECT * FROM products WHERE barcode = ?", (barcode,))
            
            if product:
                return {
                    "success": True,
                    "message": "تم العثور على المنتج",
                    "product": product
                }
            else:
                return {
                    "success": False,
                    "message": "لم يتم العثور على منتج بهذا الباركود"
                }
                
        except Exception as e:
            return {"success": False, "message": f"خطأ في البحث: {str(e)}"}
    
    def update_product_barcode(self, product_id: int, barcode: str) -> Dict[str, Any]:
        """تحديث باركود المنتج"""
        try:
            # التحقق من وجود المنتج
            product = self.db.fetch_one("SELECT * FROM products WHERE id = ?", (product_id,))
            if not product:
                return {"success": False, "message": "المنتج غير موجود"}
            
            # التحقق من عدم تكرار الباركود
            existing = self.db.fetch_one("SELECT id FROM products WHERE barcode = ? AND id != ?", 
                                        (barcode, product_id))
            if existing:
                return {"success": False, "message": "هذا الباركود مستخدم بالفعل"}
            
            # تحديث الباركود
            self.db.execute("UPDATE products SET barcode = ? WHERE id = ?", (barcode, product_id))
            
            return {
                "success": True,
                "message": "تم تحديث الباركود بنجاح",
                "product_id": product_id,
                "barcode": barcode
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في تحديث الباركود: {str(e)}"}
    
    def get_products_without_barcode(self) -> list:
        """الحصول على المنتجات بدون باركود"""
        try:
            products = self.db.fetch_all(
                "SELECT id, name FROM products WHERE barcode IS NULL OR barcode = '' ORDER BY name"
            )
            return products
        except Exception as e:
            print(f"خطأ في جلب المنتجات بدون باركود: {e}")
            return []
    
    def install_required_libraries(self) -> Dict[str, Any]:
        """تثبيت المكتبات المطلوبة"""
        try:
            import subprocess
            import sys
            
            missing = self.get_missing_libraries()
            if not missing:
                return {"success": True, "message": "جميع المكتبات مثبتة"}
            
            installed = []
            failed = []
            
            for library in missing:
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", library])
                    installed.append(library)
                except subprocess.CalledProcessError:
                    failed.append(library)
            
            # إعادة محاولة الاستيراد
            self._import_libraries()
            
            if failed:
                return {
                    "success": False,
                    "message": f"فشل في تثبيت: {', '.join(failed)}",
                    "installed": installed,
                    "failed": failed
                }
            else:
                return {
                    "success": True,
                    "message": f"تم تثبيت: {', '.join(installed)}",
                    "installed": installed
                }
                
        except Exception as e:
            return {"success": False, "message": f"فشل في التثبيت: {str(e)}"}
