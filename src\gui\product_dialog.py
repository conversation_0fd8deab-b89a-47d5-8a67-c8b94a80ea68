# -*- coding: utf-8 -*-
"""
حوار إضافة وتعديل المنتجات
Product Add/Edit Dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from ..models.product import Product, ProductManager

class ProductDialog:
    """حوار إضافة وتعديل المنتجات"""
    
    def __init__(self, parent, db_manager, config, product=None):
        """
        تهيئة الحوار
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
            product: المنتج للتعديل (None للإضافة)
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.product = product
        self.product_manager = ProductManager(db_manager)
        self.result = None
        
        # ألوان أنيقة
        self.colors = {
            'primary': '#1e40af',
            'secondary': '#7c3aed',
            'success': '#059669',
            'warning': '#d97706',
            'danger': '#dc2626',
            'light': '#f8fafc',
            'dark': '#1e293b',
            'background': '#ffffff',
            'card': '#f1f5f9',
            'border': '#e2e8f0',
            'text': '#334155',
            'text_light': '#64748b'
        }
        
        # إنشاء النافذة
        self.create_dialog()
        
        # إعداد المتغيرات
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات إذا كان تعديل
        if self.product:
            self.load_product_data()
    
    def create_dialog(self):
        """إنشاء النافذة"""
        title = "✏️ تعديل منتج" if self.product else "➕ إضافة منتج جديد"
        
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(title)
        self.dialog.geometry("600x700")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم
        self.dialog.resizable(False, False)
        
        # توسيط النافذة
        self.center_dialog()
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.code_var = tk.StringVar()
        self.name_var = tk.StringVar()
        self.category_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.cost_price_var = tk.DoubleVar()
        self.selling_price_var = tk.DoubleVar()
        self.stock_quantity_var = tk.DoubleVar()
        self.min_stock_level_var = tk.DoubleVar(value=5.0)
        self.is_active_var = tk.BooleanVar(value=True)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.dialog, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # نموذج البيانات
        self.create_form(main_container)
        
        # أزرار العمليات
        self.create_buttons(main_container)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان
        title_text = "تعديل بيانات المنتج" if self.product else "إضافة منتج جديد"
        title_label = tk.Label(header_frame,
                              text=title_text,
                              font=('Segoe UI', 16, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack()
        
        # خط فاصل
        separator = tk.Frame(header_frame, height=2, bg=self.colors['border'])
        separator.pack(fill=tk.X, pady=(10, 0))
    
    def create_form(self, parent):
        """إنشاء نموذج البيانات"""
        # إطار النموذج
        form_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # حاوي الحقول
        fields_container = tk.Frame(form_frame, bg=self.colors['card'])
        fields_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # الحقول
        fields = [
            ("الكود:", self.code_var, "text", True),
            ("اسم المنتج:", self.name_var, "text", True),
            ("الفئة:", self.category_var, "text", False),
            ("الوصف:", self.description_var, "text", False),
            ("سعر التكلفة:", self.cost_price_var, "number", True),
            ("سعر البيع:", self.selling_price_var, "number", True),
            ("الكمية الحالية:", self.stock_quantity_var, "number", True),
            ("الحد الأدنى للمخزون:", self.min_stock_level_var, "number", True)
        ]
        
        for i, (label_text, var, field_type, required) in enumerate(fields):
            self.create_field(fields_container, label_text, var, field_type, required, i)
        
        # حقل الحالة
        status_frame = tk.Frame(fields_container, bg=self.colors['card'])
        status_frame.grid(row=len(fields), column=0, columnspan=2, sticky='ew', pady=10)
        
        status_check = tk.Checkbutton(status_frame,
                                     text="المنتج نشط",
                                     variable=self.is_active_var,
                                     font=('Segoe UI', 11),
                                     fg=self.colors['text'],
                                     bg=self.colors['card'],
                                     activebackground=self.colors['card'])
        status_check.pack(anchor='w')
        
        # تكوين الشبكة
        fields_container.grid_columnconfigure(1, weight=1)
    
    def create_field(self, parent, label_text, var, field_type, required, row):
        """إنشاء حقل إدخال"""
        # تسمية الحقل
        label_frame = tk.Frame(parent, bg=self.colors['card'])
        label_frame.grid(row=row, column=0, sticky='w', pady=8)
        
        label = tk.Label(label_frame,
                        text=label_text,
                        font=('Segoe UI', 11, 'bold'),
                        fg=self.colors['text'],
                        bg=self.colors['card'])
        label.pack(side=tk.LEFT)
        
        if required:
            required_label = tk.Label(label_frame,
                                     text="*",
                                     font=('Segoe UI', 11, 'bold'),
                                     fg=self.colors['danger'],
                                     bg=self.colors['card'])
            required_label.pack(side=tk.LEFT)
        
        # حقل الإدخال
        if field_type == "text":
            if label_text == "الوصف:":
                # حقل نص متعدد الأسطر للوصف
                entry = tk.Text(parent,
                               font=('Segoe UI', 11),
                               height=3,
                               width=40,
                               relief='flat',
                               bd=1,
                               bg=self.colors['background'])
                entry.grid(row=row, column=1, sticky='ew', padx=(10, 0), pady=8)
                
                # ربط النص بالمتغير
                def on_text_change(*args):
                    var.set(entry.get("1.0", tk.END).strip())
                
                entry.bind('<KeyRelease>', on_text_change)
                setattr(self, f'entry_{row}', entry)
            else:
                entry = tk.Entry(parent,
                                textvariable=var,
                                font=('Segoe UI', 11),
                                width=40,
                                relief='flat',
                                bd=1,
                                bg=self.colors['background'])
                entry.grid(row=row, column=1, sticky='ew', padx=(10, 0), pady=8)
        
        elif field_type == "number":
            entry = tk.Entry(parent,
                            textvariable=var,
                            font=('Segoe UI', 11),
                            width=40,
                            relief='flat',
                            bd=1,
                            bg=self.colors['background'])
            entry.grid(row=row, column=1, sticky='ew', padx=(10, 0), pady=8)
            
            # التحقق من الأرقام فقط
            def validate_number(char):
                return char.isdigit() or char in '.-'
            
            vcmd = (self.dialog.register(validate_number), '%S')
            entry.config(validate='key', validatecommand=vcmd)
    
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = tk.Frame(parent, bg=self.colors['background'])
        buttons_frame.pack(fill=tk.X)
        
        # زر الحفظ
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ",
                            font=('Segoe UI', 11, 'bold'),
                            fg='white',
                            bg=self.colors['success'],
                            relief='flat',
                            padx=30,
                            pady=10,
                            command=self.save_product)
        save_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              font=('Segoe UI', 11),
                              fg=self.colors['text'],
                              bg=self.colors['light'],
                              relief='flat',
                              padx=30,
                              pady=10,
                              command=self.cancel)
        cancel_btn.pack(side=tk.RIGHT)
        
        # زر المسح (للإضافة فقط)
        if not self.product:
            clear_btn = tk.Button(buttons_frame,
                                 text="🗑️ مسح",
                                 font=('Segoe UI', 11),
                                 fg=self.colors['text'],
                                 bg=self.colors['warning'],
                                 relief='flat',
                                 padx=20,
                                 pady=10,
                                 command=self.clear_form)
            clear_btn.pack(side=tk.LEFT)
    
    def load_product_data(self):
        """تحميل بيانات المنتج للتعديل"""
        if self.product:
            self.code_var.set(self.product.code)
            self.name_var.set(self.product.name)
            self.category_var.set(self.product.category or '')
            self.description_var.set(self.product.description or '')
            self.cost_price_var.set(self.product.cost_price)
            self.selling_price_var.set(self.product.selling_price)
            self.stock_quantity_var.set(self.product.stock_quantity)
            self.min_stock_level_var.set(self.product.min_stock_level)
            self.is_active_var.set(self.product.is_active)
            
            # تحديث حقل الوصف إذا كان موجوداً
            if hasattr(self, 'entry_3'):
                self.entry_3.delete("1.0", tk.END)
                self.entry_3.insert("1.0", self.product.description or '')
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        # التحقق من الحقول المطلوبة
        if not self.code_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال كود المنتج")
            return False
        
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
            return False
        
        if self.cost_price_var.get() < 0:
            messagebox.showerror("خطأ", "سعر التكلفة لا يمكن أن يكون سالباً")
            return False
        
        if self.selling_price_var.get() < 0:
            messagebox.showerror("خطأ", "سعر البيع لا يمكن أن يكون سالباً")
            return False
        
        if self.stock_quantity_var.get() < 0:
            messagebox.showerror("خطأ", "الكمية لا يمكن أن تكون سالبة")
            return False
        
        if self.min_stock_level_var.get() < 0:
            messagebox.showerror("خطأ", "الحد الأدنى للمخزون لا يمكن أن يكون سالباً")
            return False
        
        # التحقق من تفرد الكود (للإضافة أو تغيير الكود)
        if not self.product or self.product.code != self.code_var.get():
            existing_product = self.product_manager.get_product_by_code(self.code_var.get())
            if existing_product:
                messagebox.showerror("خطأ", "كود المنتج موجود مسبقاً")
                return False
        
        return True
    
    def save_product(self):
        """حفظ المنتج"""
        if not self.validate_form():
            return
        
        try:
            # إنشاء أو تحديث المنتج
            if self.product:
                # تحديث المنتج الموجود
                self.product.code = self.code_var.get().strip()
                self.product.name = self.name_var.get().strip()
                self.product.category = self.category_var.get().strip() or None
                self.product.description = self.description_var.get().strip() or None
                self.product.cost_price = self.cost_price_var.get()
                self.product.selling_price = self.selling_price_var.get()
                self.product.stock_quantity = self.stock_quantity_var.get()
                self.product.min_stock_level = self.min_stock_level_var.get()
                self.product.is_active = self.is_active_var.get()
                
                self.product_manager.update_product(self.product)
                messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح")
            else:
                # إضافة منتج جديد
                new_product = Product(
                    code=self.code_var.get().strip(),
                    name=self.name_var.get().strip(),
                    category=self.category_var.get().strip() or None,
                    description=self.description_var.get().strip() or None,
                    cost_price=self.cost_price_var.get(),
                    selling_price=self.selling_price_var.get(),
                    stock_quantity=self.stock_quantity_var.get(),
                    min_stock_level=self.min_stock_level_var.get(),
                    is_active=self.is_active_var.get()
                )
                
                self.product_manager.create_product(new_product)
                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
            
            self.result = True
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المنتج: {str(e)}")
    
    def clear_form(self):
        """مسح النموذج"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع البيانات؟"):
            self.code_var.set("")
            self.name_var.set("")
            self.category_var.set("")
            self.description_var.set("")
            self.cost_price_var.set(0.0)
            self.selling_price_var.set(0.0)
            self.stock_quantity_var.set(0.0)
            self.min_stock_level_var.set(5.0)
            self.is_active_var.set(True)
            
            # مسح حقل الوصف
            if hasattr(self, 'entry_3'):
                self.entry_3.delete("1.0", tk.END)
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.dialog.destroy()
