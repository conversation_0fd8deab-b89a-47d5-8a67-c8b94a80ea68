# -*- coding: utf-8 -*-
"""
النافذة الرئيسية الحديثة والأنيقة
Modern and Elegant Main Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
import os

class ModernMainWindow:
    """النافذة الرئيسية الحديثة للتطبيق"""
    
    def __init__(self, root, db_manager, config):
        """تهيئة النافذة الرئيسية"""
        self.root = root
        self.db = db_manager
        self.config = config
        
        # إعداد النافذة
        self.setup_window()
        
        # إعداد الألوان والأنماط
        self.setup_modern_styles()
        
        # إنشاء الواجهة
        self.create_modern_interface()
        
        # تحميل البيانات
        self.load_dashboard_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("الميزان - نظام إدارة المحال التجارية")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # ملء الشاشة
        
        # تعيين الأيقونة
        try:
            self.root.iconbitmap('assets/icon.ico')
        except:
            pass
        
        # منع تغيير الحجم أقل من الحد الأدنى
        self.root.minsize(1200, 800)
    
    def setup_modern_styles(self):
        """إعداد الأنماط الحديثة"""
        self.style = ttk.Style()
        
        # ألوان أنيقة ومتناسقة
        self.colors = {
            'primary': '#1e40af',      # أزرق ملكي
            'secondary': '#7c3aed',    # بنفسجي أنيق
            'success': '#059669',      # أخضر طبيعي
            'warning': '#d97706',      # برتقالي دافئ
            'danger': '#dc2626',       # أحمر واضح
            'light': '#f8fafc',        # أبيض مزرق
            'dark': '#1e293b',         # رمادي داكن
            'accent': '#06b6d4',       # سماوي لامع
            'background': '#ffffff',   # خلفية نقية
            'card': '#f1f5f9',        # خلفية البطاقات
            'border': '#e2e8f0',      # حدود رقيقة
            'text': '#334155',        # نص داكن
            'text_light': '#64748b',  # نص فاتح
            'hover': '#3b82f6'        # لون التمرير
        }
        
        # تعيين الثيم
        try:
            self.style.theme_use('clam')
        except:
            pass
        
        # تخصيص الأنماط
        self.configure_modern_styles()
        
        # تعيين خلفية النافذة
        self.root.configure(bg=self.colors['background'])
    
    def configure_modern_styles(self):
        """تكوين الأنماط الحديثة"""
        # أنماط الأزرار
        self.style.configure('Modern.TButton',
                           font=('Segoe UI', 11),
                           padding=(20, 10),
                           relief='flat',
                           borderwidth=0)
        
        self.style.configure('Primary.TButton',
                           font=('Segoe UI', 11, 'bold'),
                           foreground='white',
                           background=self.colors['primary'],
                           padding=(25, 12))
        
        self.style.map('Primary.TButton',
                      background=[('active', self.colors['hover'])])
        
        self.style.configure('Card.TButton',
                           font=('Segoe UI', 10),
                           foreground=self.colors['text'],
                           background=self.colors['card'],
                           padding=(15, 8),
                           relief='flat')
        
        # أنماط الإطارات
        self.style.configure('Card.TFrame',
                           background=self.colors['card'],
                           relief='flat',
                           borderwidth=1)
        
        self.style.configure('Sidebar.TFrame',
                           background=self.colors['light'],
                           relief='flat')
        
        # أنماط التسميات
        self.style.configure('Title.TLabel',
                           font=('Segoe UI', 24, 'bold'),
                           foreground=self.colors['primary'],
                           background=self.colors['background'])
        
        self.style.configure('Heading.TLabel',
                           font=('Segoe UI', 14, 'bold'),
                           foreground=self.colors['text'],
                           background=self.colors['background'])
        
        self.style.configure('Card.TLabel',
                           font=('Segoe UI', 12),
                           foreground=self.colors['text'],
                           background=self.colors['card'])
        
        self.style.configure('Stat.TLabel',
                           font=('Segoe UI', 20, 'bold'),
                           foreground=self.colors['primary'],
                           background=self.colors['card'])
    
    def create_modern_interface(self):
        """إنشاء الواجهة الحديثة"""
        # إنشاء الحاوي الرئيسي
        main_container = tk.Frame(self.root, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الشريط العلوي
        self.create_top_bar(main_container)
        
        # إنشاء المحتوى الرئيسي
        content_container = tk.Frame(main_container, bg=self.colors['background'])
        content_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء الشريط الجانبي
        self.create_sidebar(content_container)
        
        # إنشاء منطقة المحتوى
        self.create_content_area(content_container)
    
    def create_top_bar(self, parent):
        """إنشاء الشريط العلوي"""
        top_bar = tk.Frame(parent, bg=self.colors['primary'], height=80)
        top_bar.pack(fill=tk.X)
        top_bar.pack_propagate(False)
        
        # عنوان البرنامج
        title_frame = tk.Frame(top_bar, bg=self.colors['primary'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)
        
        title_label = tk.Label(title_frame, 
                              text="🏪 الميزان",
                              font=('Segoe UI', 20, 'bold'),
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20)
        
        subtitle_label = tk.Label(title_frame,
                                 text="نظام إدارة المحال التجارية",
                                 font=('Segoe UI', 12),
                                 fg='white',
                                 bg=self.colors['primary'])
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0), pady=20)
        
        # معلومات المستخدم والتاريخ
        info_frame = tk.Frame(top_bar, bg=self.colors['primary'])
        info_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)
        
        date_label = tk.Label(info_frame,
                             text=f"📅 {date.today().strftime('%Y-%m-%d')}",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['primary'])
        date_label.pack(side=tk.RIGHT, pady=(15, 5))
        
        user_label = tk.Label(info_frame,
                             text="👤 مدير النظام",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['primary'])
        user_label.pack(side=tk.RIGHT, pady=(5, 15))
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        self.sidebar = tk.Frame(parent, bg=self.colors['light'], width=280)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 20))
        self.sidebar.pack_propagate(False)
        
        # عنوان القائمة
        menu_title = tk.Label(self.sidebar,
                             text="📋 القائمة الرئيسية",
                             font=('Segoe UI', 14, 'bold'),
                             fg=self.colors['text'],
                             bg=self.colors['light'])
        menu_title.pack(pady=(20, 10), padx=20)
        
        # أزرار القائمة
        menu_items = [
            ("🏠 لوحة التحكم", self.show_dashboard, self.colors['primary']),
            ("💰 فاتورة بيع", self.new_sale_invoice, self.colors['success']),
            ("🛒 فاتورة شراء", self.new_purchase_invoice, self.colors['warning']),
            ("📦 المنتجات", self.show_products, self.colors['accent']),
            ("👥 العملاء", self.show_customers, self.colors['secondary']),
            ("🏭 الموردين", self.show_suppliers, self.colors['dark']),
            ("📈 التقارير", self.show_reports, self.colors['danger']),
            ("⚙️ الإعدادات", self.show_settings, self.colors['text'])
        ]
        
        for text, command, color in menu_items:
            self.create_menu_button(self.sidebar, text, command, color)
    
    def create_menu_button(self, parent, text, command, color):
        """إنشاء زر قائمة أنيق"""
        btn_frame = tk.Frame(parent, bg=self.colors['light'])
        btn_frame.pack(fill=tk.X, padx=15, pady=2)
        
        btn = tk.Button(btn_frame,
                       text=text,
                       font=('Segoe UI', 11),
                       fg=self.colors['text'],
                       bg=self.colors['background'],
                       activebackground=color,
                       activeforeground='white',
                       relief='flat',
                       borderwidth=0,
                       padx=20,
                       pady=12,
                       anchor='w',
                       command=command)
        btn.pack(fill=tk.X)
        
        # تأثير التمرير
        def on_enter(e):
            btn.configure(bg=color, fg='white')
        
        def on_leave(e):
            btn.configure(bg=self.colors['background'], fg=self.colors['text'])
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
    
    def create_content_area(self, parent):
        """إنشاء منطقة المحتوى"""
        self.content_area = tk.Frame(parent, bg=self.colors['background'])
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        # مسح المحتوى السابق
        for widget in self.content_area.winfo_children():
            widget.destroy()
        
        # إنشاء لوحة التحكم
        self.create_dashboard()
    
    def create_dashboard(self):
        """إنشاء لوحة التحكم الأنيقة"""
        # عنوان لوحة التحكم
        title_frame = tk.Frame(self.content_area, bg=self.colors['background'])
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = tk.Label(title_frame,
                              text="📊 لوحة التحكم",
                              font=('Segoe UI', 18, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.LEFT)
        
        # بطاقات الإحصائيات
        self.create_stats_cards()
        
        # الرسوم البيانية والجداول
        self.create_charts_section()
    
    def create_stats_cards(self):
        """إنشاء بطاقات الإحصائيات"""
        stats_frame = tk.Frame(self.content_area, bg=self.colors['background'])
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        # بيانات الإحصائيات من قاعدة البيانات
        stats_data = getattr(self, 'dashboard_stats', {
            'sales': 0.0,
            'customers': 0,
            'products': 0,
            'alerts': 0
        })

        stats = [
            ("💰 مبيعات اليوم", f"{stats_data['sales']:.2f} ج.م", self.colors['success']),
            ("👥 العملاء", str(stats_data['customers']), self.colors['primary']),
            ("📦 المنتجات", str(stats_data['products']), self.colors['accent']),
            ("⚠️ تنبيهات", str(stats_data['alerts']), self.colors['warning'])
        ]

        for i, (title, value, color) in enumerate(stats):
            self.create_stat_card(stats_frame, title, value, color, i)
    
    def create_stat_card(self, parent, title, value, color, index):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        card_frame.grid(row=0, column=index, padx=10, pady=10, sticky='ew')
        
        # تكوين الشبكة
        parent.grid_columnconfigure(index, weight=1)
        
        # أيقونة ولون
        icon_frame = tk.Frame(card_frame, bg=color, width=60, height=60)
        icon_frame.pack(side=tk.LEFT, padx=15, pady=15)
        icon_frame.pack_propagate(False)
        
        # محتوى البطاقة
        content_frame = tk.Frame(card_frame, bg=self.colors['card'])
        content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        title_label = tk.Label(content_frame,
                              text=title,
                              font=('Segoe UI', 10),
                              fg=self.colors['text_light'],
                              bg=self.colors['card'])
        title_label.pack(anchor='w')
        
        value_label = tk.Label(content_frame,
                              text=value,
                              font=('Segoe UI', 16, 'bold'),
                              fg=self.colors['text'],
                              bg=self.colors['card'])
        value_label.pack(anchor='w')
    
    def create_charts_section(self):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = tk.Frame(self.content_area, bg=self.colors['background'])
        charts_frame.pack(fill=tk.BOTH, expand=True)
        
        # قسم الأنشطة الحديثة
        activities_frame = tk.Frame(charts_frame, bg=self.colors['card'])
        activities_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        activities_title = tk.Label(activities_frame,
                                   text="📋 الأنشطة الحديثة",
                                   font=('Segoe UI', 14, 'bold'),
                                   fg=self.colors['text'],
                                   bg=self.colors['card'])
        activities_title.pack(pady=15)
        
        # قائمة الأنشطة
        activities_list = tk.Listbox(activities_frame,
                                    font=('Segoe UI', 10),
                                    bg=self.colors['background'],
                                    fg=self.colors['text'],
                                    selectbackground=self.colors['primary'],
                                    relief='flat',
                                    borderwidth=0)
        activities_list.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # إضافة بعض الأنشطة التجريبية
        sample_activities = [
            "تم إنشاء فاتورة بيع جديدة",
            "تم إضافة منتج جديد",
            "تم تحديث بيانات عميل",
            "تم إنشاء تقرير مبيعات"
        ]
        
        for activity in sample_activities:
            activities_list.insert(tk.END, f"• {activity}")
        
        # قسم الإحصائيات السريعة
        quick_stats_frame = tk.Frame(charts_frame, bg=self.colors['card'])
        quick_stats_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        stats_title = tk.Label(quick_stats_frame,
                              text="📈 إحصائيات سريعة",
                              font=('Segoe UI', 14, 'bold'),
                              fg=self.colors['text'],
                              bg=self.colors['card'])
        stats_title.pack(pady=15)
        
        # إضافة بعض الإحصائيات
        quick_stats = [
            "مبيعات هذا الشهر: 0.00 ج.م",
            "عدد الفواتير: 0",
            "متوسط قيمة الفاتورة: 0.00 ج.م",
            "أفضل منتج: غير محدد"
        ]
        
        for stat in quick_stats:
            stat_label = tk.Label(quick_stats_frame,
                                 text=f"• {stat}",
                                 font=('Segoe UI', 11),
                                 fg=self.colors['text'],
                                 bg=self.colors['card'],
                                 anchor='w')
            stat_label.pack(fill=tk.X, padx=15, pady=5)
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            # تحميل إحصائيات المبيعات اليومية
            from ..models.invoice import InvoiceManager
            from ..models.product import ProductManager
            from ..models.customer import CustomerManager

            invoice_manager = InvoiceManager(self.db)
            product_manager = ProductManager(self.db)
            customer_manager = CustomerManager(self.db)

            # حساب مبيعات اليوم
            today_sales = invoice_manager.get_daily_sales_total(date.today())

            # عدد العملاء
            customers_count = len(customer_manager.get_all_customers())

            # عدد المنتجات
            products_count = len(product_manager.get_all_products())

            # عدد التنبيهات (منتجات منخفضة المخزون)
            low_stock_count = len(product_manager.get_low_stock_products())

            # تحديث البيانات في الواجهة
            self.update_dashboard_stats(today_sales, customers_count, products_count, low_stock_count)

        except Exception as e:
            print(f"خطأ في تحميل بيانات لوحة التحكم: {e}")

    def update_dashboard_stats(self, sales, customers, products, alerts):
        """تحديث إحصائيات لوحة التحكم"""
        # سيتم تنفيذ هذا عند إنشاء لوحة التحكم
        self.dashboard_stats = {
            'sales': sales,
            'customers': customers,
            'products': products,
            'alerts': alerts
        }
    
    # وظائف القائمة
    def new_sale_invoice(self):
        """فاتورة بيع جديدة"""
        try:
            from .invoice_window import InvoiceWindow
            InvoiceWindow(self.root, self.db, self.config, "sale")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة الفاتورة: {str(e)}")
    
    def new_purchase_invoice(self):
        """فاتورة شراء جديدة"""
        try:
            from .invoice_window import InvoiceWindow
            InvoiceWindow(self.root, self.db, self.config, "purchase")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة الفاتورة: {str(e)}")
    
    def show_products(self):
        """عرض المنتجات"""
        try:
            from .modern_products_window import ModernProductsWindow
            ModernProductsWindow(self.root, self.db, self.config)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة المنتجات: {str(e)}")
    
    def show_customers(self):
        """عرض العملاء"""
        try:
            from .customers_window import CustomersWindow
            CustomersWindow(self.root, self.db, self.config)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة العملاء: {str(e)}")
    
    def show_suppliers(self):
        """عرض الموردين"""
        messagebox.showinfo("معلومات", "سيتم إضافة نافذة الموردين قريباً")
    
    def show_reports(self):
        """عرض التقارير"""
        try:
            from .reports_window import ReportsWindow
            ReportsWindow(self.root, self.db, self.config)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التقارير: {str(e)}")
    
    def show_settings(self):
        """عرض الإعدادات"""
        messagebox.showinfo("معلومات", "سيتم إضافة نافذة الإعدادات قريباً")
