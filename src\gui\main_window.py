# -*- coding: utf-8 -*-
"""
النافذة الرئيسية
Main Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
import os

class MainWindow:
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, root, db_manager, config):
        """
        تهيئة النافذة الرئيسية
        
        Args:
            root: النافذة الجذر
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
        """
        self.root = root
        self.db = db_manager
        self.config = config
        
        # متغيرات الحالة
        self.current_user = None
        self.current_theme = "light"
        
        # إعداد الواجهة
        self.setup_styles()
        self.create_menu()
        self.create_toolbar()
        self.create_main_content()
        self.create_statusbar()
        
        # تحميل لوحة التحكم
        self.show_dashboard()
    
    def setup_styles(self):
        """إعداد أنماط الواجهة"""
        self.style = ttk.Style()
        
        # تعيين الثيم
        try:
            self.style.theme_use('clam')
        except:
            pass
        
        # ألوان مخصصة
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'light': '#F5F5F5',
            'dark': '#2C3E50',
            'white': '#FFFFFF',
            'text': '#2C3E50'
        }
        
        # تخصيص الأنماط
        self.style.configure('Title.TLabel', 
                           font=('Arial', 16, 'bold'),
                           foreground=self.colors['primary'])
        
        self.style.configure('Heading.TLabel',
                           font=('Arial', 12, 'bold'),
                           foreground=self.colors['text'])
        
        self.style.configure('Card.TFrame',
                           relief='raised',
                           borderwidth=1)
        
        self.style.configure('Primary.TButton',
                           font=('Arial', 10, 'bold'))
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file)
        file_menu.add_command(label="فتح", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_separator()
        file_menu.add_command(label="طباعة", command=self.print_document)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_app)
        
        # قائمة المبيعات
        sales_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="المبيعات", menu=sales_menu)
        sales_menu.add_command(label="فاتورة بيع جديدة", command=self.new_sale_invoice)
        sales_menu.add_command(label="فواتير البيع", command=self.show_sales_invoices)
        sales_menu.add_command(label="مرتجعات البيع", command=self.show_sales_returns)
        
        # قائمة المشتريات
        purchase_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="المشتريات", menu=purchase_menu)
        purchase_menu.add_command(label="فاتورة شراء جديدة", command=self.new_purchase_invoice)
        purchase_menu.add_command(label="فواتير الشراء", command=self.show_purchase_invoices)
        purchase_menu.add_command(label="مرتجعات الشراء", command=self.show_purchase_returns)
        
        # قائمة المخزون
        inventory_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="المخزون", menu=inventory_menu)
        inventory_menu.add_command(label="المنتجات", command=self.show_products)
        inventory_menu.add_command(label="الفئات", command=self.show_categories)
        inventory_menu.add_command(label="حركة المخزون", command=self.show_stock_movements)
        inventory_menu.add_command(label="جرد المخزون", command=self.stock_adjustment)
        
        # قائمة العملاء والموردين
        contacts_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="العملاء والموردين", menu=contacts_menu)
        contacts_menu.add_command(label="العملاء", command=self.show_customers)
        contacts_menu.add_command(label="الموردين", command=self.show_suppliers)
        
        # قائمة التقارير
        reports_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير المبيعات", command=self.sales_report)
        reports_menu.add_command(label="تقرير المشتريات", command=self.purchase_report)
        reports_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        reports_menu.add_command(label="تقرير الأرباح والخسائر", command=self.profit_loss_report)
        reports_menu.add_command(label="تقرير العملاء", command=self.customers_report)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="إعدادات عامة", command=self.general_settings)
        settings_menu.add_command(label="إعدادات الفواتير", command=self.invoice_settings)
        settings_menu.add_command(label="المستخدمين", command=self.manage_users)
        settings_menu.add_command(label="النسخ الاحتياطي", command=self.backup_settings)
        
        # قائمة المساعدة
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # أزرار سريعة
        buttons = [
            ("فاتورة بيع", self.new_sale_invoice, self.colors['primary']),
            ("فاتورة شراء", self.new_purchase_invoice, self.colors['secondary']),
            ("المنتجات", self.show_products, self.colors['success']),
            ("العملاء", self.show_customers, self.colors['warning']),
            ("التقارير", self.sales_report, self.colors['dark'])
        ]
        
        for text, command, color in buttons:
            btn = ttk.Button(self.toolbar, text=text, command=command)
            btn.pack(side=tk.LEFT, padx=2)
        
        # فاصل
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # معلومات المستخدم
        user_frame = ttk.Frame(self.toolbar)
        user_frame.pack(side=tk.RIGHT, padx=5)
        
        self.user_label = ttk.Label(user_frame, text="المستخدم: مدير النظام")
        self.user_label.pack(side=tk.RIGHT)
        
        self.date_label = ttk.Label(user_frame, text=f"التاريخ: {date.today().strftime('%Y-%m-%d')}")
        self.date_label.pack(side=tk.RIGHT, padx=(0, 10))
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار رئيسي
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # إطار جانبي للقوائم السريعة
        self.sidebar = ttk.Frame(self.main_frame, width=200)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        self.sidebar.pack_propagate(False)
        
        # إطار المحتوى
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # إنشاء القائمة الجانبية
        self.create_sidebar()
    
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        # عنوان القائمة
        title_label = ttk.Label(self.sidebar, text="القوائم السريعة", style='Heading.TLabel')
        title_label.pack(pady=(0, 10))
        
        # أزرار القائمة
        menu_items = [
            ("🏠 لوحة التحكم", self.show_dashboard),
            ("💰 فاتورة بيع", self.new_sale_invoice),
            ("📦 المنتجات", self.show_products),
            ("👥 العملاء", self.show_customers),
            ("🏪 الموردين", self.show_suppliers),
            ("📊 التقارير", self.sales_report),
            ("⚙️ الإعدادات", self.general_settings)
        ]
        
        for text, command in menu_items:
            btn = ttk.Button(self.sidebar, text=text, command=command, width=25)
            btn.pack(fill=tk.X, pady=2)
    
    def create_statusbar(self):
        """إنشاء شريط الحالة"""
        self.statusbar = ttk.Frame(self.root)
        self.statusbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # رسالة الحالة
        self.status_label = ttk.Label(self.statusbar, text="جاهز")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # معلومات إضافية
        self.info_label = ttk.Label(self.statusbar, text="الميزان v1.0")
        self.info_label.pack(side=tk.RIGHT, padx=5)
    
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_content()
        
        # عنوان لوحة التحكم
        title = ttk.Label(self.content_frame, text="لوحة التحكم", style='Title.TLabel')
        title.pack(pady=10)
        
        # إطار البطاقات
        cards_frame = ttk.Frame(self.content_frame)
        cards_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # بطاقات الإحصائيات
        self.create_dashboard_cards(cards_frame)
        
        # الرسوم البيانية والتنبيهات
        self.create_dashboard_charts(cards_frame)
    
    def create_dashboard_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        # الحصول على البيانات
        try:
            from ..reports.report_generator import ReportGenerator
            report_gen = ReportGenerator(self.db)
            dashboard_data = report_gen.dashboard_summary()
        except:
            dashboard_data = {
                'today_sales': {'amount': 0},
                'customers_count': 0,
                'products_count': 0,
                'low_stock_count': 0
            }

        # الصف الأول من البطاقات
        row1 = ttk.Frame(parent)
        row1.pack(fill=tk.X, pady=(0, 10))

        # بطاقة المبيعات اليومية
        sales_amount = dashboard_data['today_sales']['amount']
        sales_card = self.create_stat_card(row1, "مبيعات اليوم", f"{sales_amount:.2f} ج.م", self.colors['primary'])
        sales_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # بطاقة العملاء
        customers_count = dashboard_data['customers_count']
        customers_card = self.create_stat_card(row1, "العملاء", str(customers_count), self.colors['secondary'])
        customers_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        # بطاقة المنتجات
        products_count = dashboard_data['products_count']
        products_card = self.create_stat_card(row1, "المنتجات", str(products_count), self.colors['success'])
        products_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        # بطاقة التنبيهات
        low_stock_count = dashboard_data['low_stock_count']
        alerts_card = self.create_stat_card(row1, "تنبيهات المخزون", str(low_stock_count), self.colors['warning'])
        alerts_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
    
    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ttk.Frame(parent, style='Card.TFrame')
        
        # العنوان
        title_label = ttk.Label(card, text=title, font=('Arial', 10, 'bold'))
        title_label.pack(pady=(10, 5))
        
        # القيمة
        value_label = ttk.Label(card, text=value, font=('Arial', 14, 'bold'), foreground=color)
        value_label.pack(pady=(0, 10))
        
        return card
    
    def create_dashboard_charts(self, parent):
        """إنشاء الرسوم البيانية والتنبيهات"""
        # الصف الثاني
        row2 = ttk.Frame(parent)
        row2.pack(fill=tk.BOTH, expand=True)
        
        # إطار التنبيهات
        alerts_frame = ttk.LabelFrame(row2, text="التنبيهات", padding=10)
        alerts_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # قائمة التنبيهات
        alerts_text = tk.Text(alerts_frame, height=10, wrap=tk.WORD)
        alerts_text.pack(fill=tk.BOTH, expand=True)
        alerts_text.insert(tk.END, "• لا توجد تنبيهات حالياً\n")
        alerts_text.config(state=tk.DISABLED)
        
        # إطار الأنشطة الأخيرة
        activity_frame = ttk.LabelFrame(row2, text="الأنشطة الأخيرة", padding=10)
        activity_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # قائمة الأنشطة
        activity_text = tk.Text(activity_frame, height=10, wrap=tk.WORD)
        activity_text.pack(fill=tk.BOTH, expand=True)
        activity_text.insert(tk.END, "• تم تشغيل البرنامج\n")
        activity_text.config(state=tk.DISABLED)
    
    # دوال القوائم (ستكون فارغة مؤقتاً)
    def new_file(self): pass
    def open_file(self): pass
    def save_file(self): pass
    def print_document(self): pass
    def exit_app(self): self.root.quit()
    
    def new_sale_invoice(self):
        self.status_label.config(text="فتح فاتورة بيع جديدة...")
        from .invoice_window import InvoiceWindow
        InvoiceWindow(self.root, self.db, self.config, "sale")
    
    def show_sales_invoices(self): pass
    def show_sales_returns(self): pass
    def new_purchase_invoice(self):
        self.status_label.config(text="فتح فاتورة شراء جديدة...")
        from .invoice_window import InvoiceWindow
        InvoiceWindow(self.root, self.db, self.config, "purchase")
    def show_purchase_invoices(self): pass
    def show_purchase_returns(self): pass
    
    def show_products(self):
        self.status_label.config(text="عرض المنتجات...")
        from .products_window import ProductsWindow
        ProductsWindow(self.root, self.db, self.config)
    
    def show_categories(self): pass
    def show_stock_movements(self): pass
    def stock_adjustment(self): pass
    
    def show_customers(self):
        self.status_label.config(text="عرض العملاء...")
        from .customers_window import CustomersWindow
        CustomersWindow(self.root, self.db, self.config)
    
    def show_suppliers(self): pass
    
    def sales_report(self):
        self.status_label.config(text="عرض التقارير...")
        from .reports_window import ReportsWindow
        ReportsWindow(self.root, self.db, self.config)
    def purchase_report(self): pass
    def inventory_report(self): pass
    def profit_loss_report(self): pass
    def customers_report(self): pass
    
    def general_settings(self): pass
    def invoice_settings(self): pass
    def manage_users(self): pass
    def backup_settings(self): pass
    
    def user_guide(self): pass
    def about(self):
        messagebox.showinfo("حول البرنامج", 
                          "برنامج الميزان\nنظام إدارة المحال التجارية\nالإصدار 1.0\n\nتم التطوير بواسطة مساعد الذكي الاصطناعي")
