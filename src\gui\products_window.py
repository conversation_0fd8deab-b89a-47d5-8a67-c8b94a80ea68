# -*- coding: utf-8 -*-
"""
نافذة إدارة المنتجات
Products Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import os

from ..models.product import Product, ProductManager

class ProductsWindow:
    """نافذة إدارة المنتجات"""
    
    def __init__(self, parent, db_manager, config):
        """
        تهيئة نافذة المنتجات
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.product_manager = ProductManager(db_manager)
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة المنتجات")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        # متغيرات
        self.products = []
        self.selected_product = None
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        # إعداد الواجهة
        self.setup_ui()
        self.load_products()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إطار البحث والأزرار
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # البحث
        search_frame = ttk.Frame(top_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT, padx=(0, 5))
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(top_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="إضافة منتج", command=self.add_product).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="تعديل", command=self.edit_product).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="حذف", command=self.delete_product).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="تحديث", command=self.load_products).pack(side=tk.LEFT, padx=2)
        
        # جدول المنتجات
        self.create_products_table(main_frame)
        
        # إطار التفاصيل
        details_frame = ttk.LabelFrame(main_frame, text="تفاصيل المنتج", padding=10)
        details_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.create_product_details(details_frame)
    
    def create_products_table(self, parent):
        """إنشاء جدول المنتجات"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # أعمدة الجدول
        columns = ('code', 'name', 'category', 'unit', 'cost_price', 'selling_price', 'stock', 'status')
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        self.tree.heading('code', text='الكود')
        self.tree.heading('name', text='اسم المنتج')
        self.tree.heading('category', text='الفئة')
        self.tree.heading('unit', text='الوحدة')
        self.tree.heading('cost_price', text='سعر التكلفة')
        self.tree.heading('selling_price', text='سعر البيع')
        self.tree.heading('stock', text='المخزون')
        self.tree.heading('status', text='الحالة')
        
        # تعيين عرض الأعمدة
        self.tree.column('code', width=80)
        self.tree.column('name', width=200)
        self.tree.column('category', width=100)
        self.tree.column('unit', width=60)
        self.tree.column('cost_price', width=80)
        self.tree.column('selling_price', width=80)
        self.tree.column('stock', width=60)
        self.tree.column('status', width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tree.bind('<<TreeviewSelect>>', self.on_product_select)
        self.tree.bind('<Double-1>', self.edit_product)
    
    def create_product_details(self, parent):
        """إنشاء قسم تفاصيل المنتج"""
        # إطار المعلومات الأساسية
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X)
        
        # الصف الأول
        row1 = ttk.Frame(info_frame)
        row1.pack(fill=tk.X, pady=2)
        
        ttk.Label(row1, text="الكود:", width=15).pack(side=tk.LEFT)
        self.code_label = ttk.Label(row1, text="-", foreground="blue")
        self.code_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row1, text="الاسم:", width=15).pack(side=tk.LEFT)
        self.name_label = ttk.Label(row1, text="-")
        self.name_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # الصف الثاني
        row2 = ttk.Frame(info_frame)
        row2.pack(fill=tk.X, pady=2)
        
        ttk.Label(row2, text="الفئة:", width=15).pack(side=tk.LEFT)
        self.category_label = ttk.Label(row2, text="-")
        self.category_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row2, text="الوحدة:", width=15).pack(side=tk.LEFT)
        self.unit_label = ttk.Label(row2, text="-")
        self.unit_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # الصف الثالث
        row3 = ttk.Frame(info_frame)
        row3.pack(fill=tk.X, pady=2)
        
        ttk.Label(row3, text="سعر التكلفة:", width=15).pack(side=tk.LEFT)
        self.cost_label = ttk.Label(row3, text="-")
        self.cost_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row3, text="سعر البيع:", width=15).pack(side=tk.LEFT)
        self.price_label = ttk.Label(row3, text="-")
        self.price_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # الصف الرابع
        row4 = ttk.Frame(info_frame)
        row4.pack(fill=tk.X, pady=2)
        
        ttk.Label(row4, text="المخزون:", width=15).pack(side=tk.LEFT)
        self.stock_label = ttk.Label(row4, text="-")
        self.stock_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row4, text="هامش الربح:", width=15).pack(side=tk.LEFT)
        self.margin_label = ttk.Label(row4, text="-")
        self.margin_label.pack(side=tk.LEFT, padx=(0, 20))
    
    def load_products(self):
        """تحميل المنتجات"""
        try:
            # مسح الجدول
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # جلب المنتجات من قاعدة البيانات
            query = """
                SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = 1
                ORDER BY p.name
            """
            
            rows = self.db.fetch_all(query)
            self.products = [Product.from_dict(dict(row)) for row in rows]
            
            # إضافة المنتجات للجدول
            for product in self.products:
                self.tree.insert('', tk.END, values=(
                    product.code,
                    product.name,
                    product.category_name or '-',
                    product.unit,
                    f"{product.cost_price:.2f}",
                    f"{product.selling_price:.2f}",
                    product.stock_quantity,
                    product.stock_status
                ))
            
            # تحديث شريط الحالة
            self.update_status(f"تم تحميل {len(self.products)} منتج")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المنتجات: {str(e)}")
    
    def on_search_change(self, *args):
        """البحث في المنتجات"""
        search_term = self.search_var.get().lower()
        
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # تصفية المنتجات
        filtered_products = []
        for product in self.products:
            if (search_term in product.name.lower() or 
                search_term in product.code.lower() or
                search_term in (product.category_name or '').lower()):
                filtered_products.append(product)
        
        # إضافة المنتجات المفلترة
        for product in filtered_products:
            self.tree.insert('', tk.END, values=(
                product.code,
                product.name,
                product.category_name or '-',
                product.unit,
                f"{product.cost_price:.2f}",
                f"{product.selling_price:.2f}",
                product.stock_quantity,
                product.stock_status
            ))
    
    def on_product_select(self, event):
        """عند اختيار منتج"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            product_code = item['values'][0]
            
            # البحث عن المنتج
            self.selected_product = None
            for product in self.products:
                if product.code == product_code:
                    self.selected_product = product
                    break
            
            # تحديث التفاصيل
            self.update_product_details()
    
    def update_product_details(self):
        """تحديث تفاصيل المنتج"""
        if self.selected_product:
            self.code_label.config(text=self.selected_product.code)
            self.name_label.config(text=self.selected_product.name)
            self.category_label.config(text=self.selected_product.category_name or '-')
            self.unit_label.config(text=self.selected_product.unit)
            self.cost_label.config(text=f"{self.selected_product.cost_price:.2f} ج.م")
            self.price_label.config(text=f"{self.selected_product.selling_price:.2f} ج.م")
            self.stock_label.config(text=str(self.selected_product.stock_quantity))
            self.margin_label.config(text=f"{self.selected_product.profit_margin:.1f}%")
        else:
            # مسح التفاصيل
            for label in [self.code_label, self.name_label, self.category_label, 
                         self.unit_label, self.cost_label, self.price_label, 
                         self.stock_label, self.margin_label]:
                label.config(text="-")
    
    def add_product(self):
        """إضافة منتج جديد"""
        dialog = ProductDialog(self.window, self.db, self.config)
        if dialog.result:
            self.load_products()
    
    def edit_product(self):
        """تعديل منتج"""
        if not self.selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return
        
        dialog = ProductDialog(self.window, self.db, self.config, self.selected_product)
        if dialog.result:
            self.load_products()
    
    def delete_product(self):
        """حذف منتج"""
        if not self.selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", 
                             f"هل تريد حذف المنتج '{self.selected_product.name}'؟"):
            try:
                # حذف المنتج (تعطيل بدلاً من الحذف الفعلي)
                query = "UPDATE products SET is_active = 0 WHERE id = ?"
                self.db.execute_query(query, (self.selected_product.id,))
                
                messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
                self.load_products()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المنتج: {str(e)}")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        # يمكن إضافة شريط حالة هنا
        pass

class ProductDialog:
    """حوار إضافة/تعديل منتج"""
    
    def __init__(self, parent, db_manager, config, product=None):
        """
        تهيئة حوار المنتج
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
            product: المنتج للتعديل (None للإضافة)
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.product = product
        self.result = None
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إضافة منتج" if product is None else "تعديل منتج")
        self.window.geometry("500x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات إذا كان تعديل
        if self.product:
            self.load_product_data()
        
        # تشغيل النافذة
        self.window.wait_window()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.code_var = tk.StringVar()
        self.name_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.category_var = tk.StringVar()
        self.unit_var = tk.StringVar(value="قطعة")
        self.cost_price_var = tk.DoubleVar()
        self.selling_price_var = tk.DoubleVar()
        self.stock_var = tk.IntVar()
        self.min_stock_var = tk.IntVar()
        self.max_stock_var = tk.IntVar(value=1000)
        self.barcode_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar(value=True)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إطار النموذج
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.BOTH, expand=True)
        
        # الحقول
        row = 0
        
        # كود المنتج
        ttk.Label(form_frame, text="كود المنتج *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.code_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # اسم المنتج
        ttk.Label(form_frame, text="اسم المنتج *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.name_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الوصف
        ttk.Label(form_frame, text="الوصف:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.description_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الوحدة
        ttk.Label(form_frame, text="الوحدة:").grid(row=row, column=0, sticky=tk.W, pady=5)
        unit_combo = ttk.Combobox(form_frame, textvariable=self.unit_var, width=27)
        unit_combo['values'] = ('قطعة', 'كيلو', 'متر', 'لتر', 'علبة', 'كرتونة')
        unit_combo.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # سعر التكلفة
        ttk.Label(form_frame, text="سعر التكلفة:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.cost_price_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # سعر البيع
        ttk.Label(form_frame, text="سعر البيع *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.selling_price_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # المخزون الحالي
        ttk.Label(form_frame, text="المخزون الحالي:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.stock_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الحد الأدنى للمخزون
        ttk.Label(form_frame, text="الحد الأدنى للمخزون:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.min_stock_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الحد الأقصى للمخزون
        ttk.Label(form_frame, text="الحد الأقصى للمخزون:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.max_stock_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الباركود
        ttk.Label(form_frame, text="الباركود:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.barcode_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # نشط
        ttk.Checkbutton(form_frame, text="منتج نشط", variable=self.is_active_var).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_product).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.RIGHT)
    
    def load_product_data(self):
        """تحميل بيانات المنتج للتعديل"""
        if self.product:
            self.code_var.set(self.product.code)
            self.name_var.set(self.product.name)
            self.description_var.set(self.product.description)
            self.unit_var.set(self.product.unit)
            self.cost_price_var.set(self.product.cost_price)
            self.selling_price_var.set(self.product.selling_price)
            self.stock_var.set(self.product.stock_quantity)
            self.min_stock_var.set(self.product.min_stock_level)
            self.max_stock_var.set(self.product.max_stock_level)
            self.barcode_var.set(self.product.barcode)
            self.is_active_var.set(self.product.is_active)
    
    def save_product(self):
        """حفظ المنتج"""
        try:
            # إنشاء كائن المنتج
            product = Product(
                id=self.product.id if self.product else None,
                code=self.code_var.get().strip(),
                name=self.name_var.get().strip(),
                description=self.description_var.get().strip(),
                unit=self.unit_var.get(),
                cost_price=self.cost_price_var.get(),
                selling_price=self.selling_price_var.get(),
                stock_quantity=self.stock_var.get(),
                min_stock_level=self.min_stock_var.get(),
                max_stock_level=self.max_stock_var.get(),
                barcode=self.barcode_var.get().strip(),
                is_active=self.is_active_var.get()
            )
            
            # التحقق من صحة البيانات
            errors = product.validate()
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return
            
            # حفظ في قاعدة البيانات
            product_manager = ProductManager(self.db)
            
            if self.product:  # تعديل
                query = """
                    UPDATE products SET 
                        code = ?, name = ?, description = ?, unit = ?,
                        cost_price = ?, selling_price = ?, stock_quantity = ?,
                        min_stock_level = ?, max_stock_level = ?, barcode = ?,
                        is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """
                self.db.execute_query(query, (
                    product.code, product.name, product.description, product.unit,
                    product.cost_price, product.selling_price, product.stock_quantity,
                    product.min_stock_level, product.max_stock_level, product.barcode,
                    product.is_active, self.product.id
                ))
                messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح")
            else:  # إضافة
                product_id = product_manager.create_product(product)
                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
            
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المنتج: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.window.destroy()
