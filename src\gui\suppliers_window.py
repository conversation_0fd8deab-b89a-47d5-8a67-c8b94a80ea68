# -*- coding: utf-8 -*-
"""
نافذة إدارة الموردين
Suppliers Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from ..models.supplier import Supplier, SupplierManager
from ..utils.colors import AppColors

class SuppliersWindow:
    """نافذة إدارة الموردين"""
    
    def __init__(self, parent, db_manager, config):
        """تهيئة نافذة الموردين"""
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.supplier_manager = SupplierManager(db_manager)
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات
        self.suppliers = []
        self.selected_supplier = None
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        # إعداد الواجهة
        self.setup_ui()
        self.load_suppliers()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🏭 إدارة الموردين")
        self.window.geometry("1300x850")
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم أقل من الحد الأدنى
        self.window.minsize(1000, 600)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # شريط البحث والأدوات
        self.create_toolbar(main_container)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_container, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # قائمة الموردين (على اليسار للعربية)
        self.create_suppliers_list(content_frame)
        
        # لوحة التفاصيل (على اليمين للعربية)
        self.create_details_panel(content_frame)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان (على اليمين للعربية)
        title_label = tk.Label(header_frame,
                              text="🏭 إدارة الموردين",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.RIGHT)
        
        # زر الإغلاق (على اليسار للعربية)
        close_btn = tk.Button(header_frame,
                             text="✕ إغلاق",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['danger'],
                             relief='flat',
                             padx=20,
                             pady=8,
                             command=self.window.destroy)
        close_btn.pack(side=tk.LEFT)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        toolbar_frame.pack(fill=tk.X, pady=(0, 20))
        
        # الجانب الأيمن - البحث (للعربية)
        search_frame = tk.Frame(toolbar_frame, bg=self.colors['card'])
        search_frame.pack(side=tk.RIGHT, padx=15, pady=15)
        
        search_label = tk.Label(search_frame,
                               text="🔍 البحث:",
                               font=('Segoe UI', 11),
                               fg=self.colors['text'],
                               bg=self.colors['card'])
        search_label.pack(side=tk.RIGHT)
        
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=('Segoe UI', 11),
                               width=30,
                               relief='flat',
                               bd=1)
        search_entry.pack(side=tk.RIGHT, padx=(0, 10))
        
        # الجانب الأيسر - الأزرار (للعربية)
        buttons_frame = tk.Frame(toolbar_frame, bg=self.colors['card'])
        buttons_frame.pack(side=tk.LEFT, padx=15, pady=15)
        
        # أزرار العمليات
        buttons = [
            ("🔄 تحديث", self.refresh_suppliers, self.colors['secondary']),
            ("🗑️ حذف", self.delete_supplier, self.colors['danger']),
            ("✏️ تعديل", self.edit_supplier, self.colors['primary']),
            ("➕ إضافة مورد", self.add_supplier, self.colors['success'])
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(buttons_frame,
                           text=text,
                           font=('Segoe UI', 10),
                           fg='white',
                           bg=color,
                           relief='flat',
                           padx=15,
                           pady=8,
                           command=command)
            btn.pack(side=tk.LEFT, padx=(0, 5))
    
    def create_suppliers_list(self, parent):
        """إنشاء قائمة الموردين"""
        # إطار القائمة
        list_frame = tk.Frame(parent, bg=self.colors['background'])
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عنوان القائمة
        list_title = tk.Label(list_frame,
                             text="📋 قائمة الموردين",
                             font=('Segoe UI', 14, 'bold'),
                             fg=self.colors['text'],
                             bg=self.colors['background'])
        list_title.pack(anchor='e', pady=(0, 10))  # محاذاة يمين للعربية
        
        # إطار الجدول
        table_frame = tk.Frame(list_frame, bg=self.colors['card'], relief='flat', bd=1)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ('code', 'name', 'contact_person', 'phone', 'city', 'balance', 'status')
        
        self.suppliers_tree = ttk.Treeview(table_frame, 
                                          columns=columns, 
                                          show='headings',
                                          height=20)
        
        # تعيين عناوين الأعمدة
        headers = {
            'code': 'الكود',
            'name': 'اسم المورد',
            'contact_person': 'الشخص المسؤول',
            'phone': 'الهاتف',
            'city': 'المدينة',
            'balance': 'الرصيد',
            'status': 'الحالة'
        }
        
        for col, header in headers.items():
            self.suppliers_tree.heading(col, text=header)
            self.suppliers_tree.column(col, width=120, anchor='center')
        
        # تعيين عرض أعمدة محددة
        self.suppliers_tree.column('name', width=200, anchor='w')
        self.suppliers_tree.column('contact_person', width=150, anchor='w')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.suppliers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # ربط الأحداث
        self.suppliers_tree.bind('<<TreeviewSelect>>', self.on_supplier_select)
        self.suppliers_tree.bind('<Double-1>', self.edit_supplier)
    
    def create_details_panel(self, parent):
        """إنشاء لوحة التفاصيل"""
        # إطار التفاصيل
        details_frame = tk.Frame(parent, bg=self.colors['card'], width=350, relief='flat', bd=1)
        details_frame.pack(side=tk.RIGHT, fill=tk.Y)
        details_frame.pack_propagate(False)
        
        # عنوان التفاصيل
        details_title = tk.Label(details_frame,
                                text="📄 تفاصيل المورد",
                                font=('Segoe UI', 14, 'bold'),
                                fg=self.colors['text'],
                                bg=self.colors['card'])
        details_title.pack(pady=(20, 15))
        
        # إطار المعلومات
        info_frame = tk.Frame(details_frame, bg=self.colors['card'])
        info_frame.pack(fill=tk.X, padx=20)
        
        # تسميات المعلومات
        self.detail_labels = {}
        details_info = [
            ('code', 'الكود:'),
            ('name', 'اسم المورد:'),
            ('contact_person', 'الشخص المسؤول:'),
            ('phone', 'الهاتف:'),
            ('email', 'البريد الإلكتروني:'),
            ('address', 'العنوان:'),
            ('city', 'المدينة:'),
            ('country', 'البلد:'),
            ('tax_number', 'الرقم الضريبي:'),
            ('payment_terms', 'شروط الدفع:'),
            ('credit_limit', 'حد الائتمان:'),
            ('current_balance', 'الرصيد الحالي:'),
            ('status', 'الحالة:')
        ]
        
        for i, (key, label) in enumerate(details_info):
            # تسمية الحقل
            field_label = tk.Label(info_frame,
                                  text=label,
                                  font=('Segoe UI', 10, 'bold'),
                                  fg=self.colors['text'],
                                  bg=self.colors['card'],
                                  anchor='e')  # محاذاة يمين للعربية
            field_label.grid(row=i, column=1, sticky='e', pady=5)
            
            # قيمة الحقل
            value_label = tk.Label(info_frame,
                                  text='-',
                                  font=('Segoe UI', 10),
                                  fg=self.colors['text_secondary'],
                                  bg=self.colors['card'],
                                  anchor='w')
            value_label.grid(row=i, column=0, sticky='w', padx=(0, 10), pady=5)
            
            self.detail_labels[key] = value_label
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(details_frame, bg=self.colors['background'], relief='flat', bd=1)
        stats_frame.pack(fill=tk.X, padx=20, pady=20)
        
        stats_title = tk.Label(stats_frame,
                              text="📊 إحصائيات سريعة",
                              font=('Segoe UI', 12, 'bold'),
                              fg=self.colors['text'],
                              bg=self.colors['background'])
        stats_title.pack(pady=(10, 5))
        
        # إحصائيات الموردين
        self.stats_labels = {}
        stats_info = [
            ('total_suppliers', 'إجمالي الموردين:'),
            ('active_suppliers', 'موردين نشطين:'),
            ('total_balance', 'إجمالي الأرصدة:'),
            ('credit_used', 'الائتمان المستخدم:')
        ]
        
        for i, (key, label) in enumerate(stats_info):
            stat_frame = tk.Frame(stats_frame, bg=self.colors['background'])
            stat_frame.pack(fill=tk.X, pady=2)
            
            stat_label = tk.Label(stat_frame,
                                 text=label,
                                 font=('Segoe UI', 9),
                                 fg=self.colors['text'],
                                 bg=self.colors['background'])
            stat_label.pack(side=tk.RIGHT)  # يمين للعربية
            
            stat_value = tk.Label(stat_frame,
                                 text='0',
                                 font=('Segoe UI', 9, 'bold'),
                                 fg=self.colors['primary'],
                                 bg=self.colors['background'])
            stat_value.pack(side=tk.LEFT)  # يسار للعربية
            
            self.stats_labels[key] = stat_value
    
    def load_suppliers(self):
        """تحميل الموردين"""
        try:
            # مسح البيانات السابقة
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)
            
            # تحميل الموردين من قاعدة البيانات
            self.suppliers = self.supplier_manager.get_all_suppliers()
            
            # إضافة الموردين للجدول
            for supplier in self.suppliers:
                status = "نشط" if supplier.is_active else "غير نشط"
                
                self.suppliers_tree.insert('', tk.END, values=(
                    supplier.code,
                    supplier.name,
                    supplier.contact_person or '-',
                    supplier.phone or '-',
                    supplier.city or '-',
                    f"{supplier.current_balance:.2f} ج.م",
                    status
                ))
            
            # تحديث الإحصائيات
            self.update_statistics()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الموردين: {str(e)}")
            self.suppliers = []
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            total_suppliers = len(self.suppliers)
            active_suppliers = sum(1 for s in self.suppliers if s.is_active)
            total_balance = sum(s.current_balance for s in self.suppliers)
            credit_used = sum(s.current_balance for s in self.suppliers if s.current_balance > 0)
            
            self.stats_labels['total_suppliers'].config(text=str(total_suppliers))
            self.stats_labels['active_suppliers'].config(text=str(active_suppliers))
            self.stats_labels['total_balance'].config(text=f"{total_balance:.2f} ج.م")
            self.stats_labels['credit_used'].config(text=f"{credit_used:.2f} ج.م")
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def on_supplier_select(self, event):
        """عند اختيار مورد"""
        selection = self.suppliers_tree.selection()
        if selection:
            item = self.suppliers_tree.item(selection[0])
            supplier_code = item['values'][0]
            
            # البحث عن المورد
            self.selected_supplier = None
            for supplier in self.suppliers:
                if supplier.code == supplier_code:
                    self.selected_supplier = supplier
                    break
            
            # تحديث التفاصيل
            self.update_details()
    
    def update_details(self):
        """تحديث تفاصيل المورد"""
        if self.selected_supplier:
            details = {
                'code': self.selected_supplier.code,
                'name': self.selected_supplier.name,
                'contact_person': self.selected_supplier.contact_person or '-',
                'phone': self.selected_supplier.phone or '-',
                'email': self.selected_supplier.email or '-',
                'address': self.selected_supplier.address or '-',
                'city': self.selected_supplier.city or '-',
                'country': self.selected_supplier.country or '-',
                'tax_number': self.selected_supplier.tax_number or '-',
                'payment_terms': self.selected_supplier.payment_terms or '-',
                'credit_limit': f"{self.selected_supplier.credit_limit:.2f} ج.م",
                'current_balance': f"{self.selected_supplier.current_balance:.2f} ج.م",
                'status': 'نشط' if self.selected_supplier.is_active else 'غير نشط'
            }
            
            for key, value in details.items():
                if key in self.detail_labels:
                    self.detail_labels[key].config(text=value)
        else:
            # مسح التفاصيل
            for label in self.detail_labels.values():
                label.config(text='-')
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        search_text = self.search_var.get().lower()
        
        # مسح الجدول
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)
        
        # تصفية وعرض الموردين
        for supplier in self.suppliers:
            if (search_text in supplier.code.lower() or 
                search_text in supplier.name.lower() or
                search_text in (supplier.contact_person or '').lower()):
                
                status = "نشط" if supplier.is_active else "غير نشط"
                
                self.suppliers_tree.insert('', tk.END, values=(
                    supplier.code,
                    supplier.name,
                    supplier.contact_person or '-',
                    supplier.phone or '-',
                    supplier.city or '-',
                    f"{supplier.current_balance:.2f} ج.م",
                    status
                ))
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        try:
            from .supplier_dialog import SupplierDialog
            dialog = SupplierDialog(self.window, self.db, self.config)
            if dialog.result:
                self.load_suppliers()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إضافة المورد: {str(e)}")
    
    def edit_supplier(self):
        """تعديل المورد المحدد"""
        if not self.selected_supplier:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للتعديل")
            return
        
        try:
            from .supplier_dialog import SupplierDialog
            dialog = SupplierDialog(self.window, self.db, self.config, self.selected_supplier)
            if dialog.result:
                self.load_suppliers()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة تعديل المورد: {str(e)}")
    
    def delete_supplier(self):
        """حذف المورد المحدد"""
        if not self.selected_supplier:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للحذف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل تريد حذف المورد '{self.selected_supplier.name}'؟"):
            try:
                self.supplier_manager.delete_supplier(self.selected_supplier.id)
                messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
                self.load_suppliers()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المورد: {str(e)}")
    
    def refresh_suppliers(self):
        """تحديث قائمة الموردين"""
        self.load_suppliers()
        messagebox.showinfo("تم", "تم تحديث قائمة الموردين")
