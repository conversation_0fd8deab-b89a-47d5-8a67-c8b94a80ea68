# -*- coding: utf-8 -*-
"""
نافذة إنشاء الفواتير
Invoice Creation Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date, timedelta
from typing import Optional

from ..models.invoice import Invoice, InvoiceItem, InvoiceManager
from ..models.product import ProductManager
from ..models.customer import CustomerManager

class InvoiceWindow:
    """نافذة إنشاء الفواتير"""
    
    def __init__(self, parent, db_manager, config, invoice_type="sale"):
        """
        تهيئة نافذة الفاتورة
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
            invoice_type: نوع الفاتورة ('sale' أو 'purchase')
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.invoice_type = invoice_type
        
        # المديرين
        self.invoice_manager = InvoiceManager(db_manager)
        self.product_manager = ProductManager(db_manager)
        self.customer_manager = CustomerManager(db_manager)
        
        # ألوان أنيقة
        self.colors = {
            'primary': '#1e40af',
            'secondary': '#7c3aed',
            'success': '#059669',
            'warning': '#d97706',
            'danger': '#dc2626',
            'light': '#f8fafc',
            'dark': '#1e293b',
            'background': '#ffffff',
            'card': '#f1f5f9',
            'border': '#e2e8f0',
            'text': '#334155',
            'text_light': '#64748b'
        }

        # إنشاء النافذة
        icon = "💰" if invoice_type == "sale" else "🛒"
        title = f"{icon} فاتورة بيع جديدة" if invoice_type == "sale" else f"{icon} فاتورة شراء جديدة"
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("1300x900")
        self.window.transient(parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # متغيرات
        self.invoice = Invoice(invoice_type=invoice_type)
        self.customers = []
        self.products = []
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.load_data()
        
        # توليد رقم الفاتورة
        self.generate_invoice_number()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.invoice_number_var = tk.StringVar()
        self.invoice_date_var = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        self.due_date_var = tk.StringVar()
        self.customer_var = tk.StringVar()
        self.payment_method_var = tk.StringVar(value="cash")
        self.discount_percentage_var = tk.DoubleVar()
        self.tax_percentage_var = tk.DoubleVar(value=14.0)
        self.notes_var = tk.StringVar()
        
        # متغيرات إضافة المنتج
        self.product_var = tk.StringVar()
        self.quantity_var = tk.DoubleVar(value=1.0)
        self.unit_price_var = tk.DoubleVar()
        self.item_discount_var = tk.DoubleVar()
        
        # متغيرات الإجماليات
        self.subtotal_var = tk.StringVar(value="0.00")
        self.discount_amount_var = tk.StringVar(value="0.00")
        self.tax_amount_var = tk.StringVar(value="0.00")
        self.total_var = tk.StringVar(value="0.00")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إطار معلومات الفاتورة
        self.create_invoice_info_frame(main_frame)
        
        # إطار إضافة المنتجات
        self.create_product_entry_frame(main_frame)
        
        # إطار عناصر الفاتورة
        self.create_items_frame(main_frame)
        
        # إطار الإجماليات
        self.create_totals_frame(main_frame)
        
        # إطار الأزرار
        self.create_buttons_frame(main_frame)
    
    def create_invoice_info_frame(self, parent):
        """إنشاء إطار معلومات الفاتورة"""
        info_frame = ttk.LabelFrame(parent, text="معلومات الفاتورة", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(info_frame)
        row1.pack(fill=tk.X, pady=2)
        
        ttk.Label(row1, text="رقم الفاتورة:", width=15).pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.invoice_number_var, width=20, state='readonly').pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row1, text="تاريخ الفاتورة:", width=15).pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.invoice_date_var, width=15).pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row1, text="تاريخ الاستحقاق:", width=15).pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.due_date_var, width=15).pack(side=tk.LEFT)
        
        # الصف الثاني
        row2 = ttk.Frame(info_frame)
        row2.pack(fill=tk.X, pady=2)
        
        customer_label = "العميل:" if self.invoice_type == "sale" else "المورد:"
        ttk.Label(row2, text=customer_label, width=15).pack(side=tk.LEFT)
        self.customer_combo = ttk.Combobox(row2, textvariable=self.customer_var, width=30)
        self.customer_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row2, text="طريقة الدفع:", width=15).pack(side=tk.LEFT)
        payment_combo = ttk.Combobox(row2, textvariable=self.payment_method_var, width=15)
        payment_combo['values'] = ('نقدي', 'آجل', 'بنكي')
        payment_combo.pack(side=tk.LEFT)
    
    def create_product_entry_frame(self, parent):
        """إنشاء إطار إضافة المنتجات"""
        entry_frame = ttk.LabelFrame(parent, text="إضافة منتج", padding=10)
        entry_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول
        row1 = ttk.Frame(entry_frame)
        row1.pack(fill=tk.X, pady=2)
        
        ttk.Label(row1, text="المنتج:", width=10).pack(side=tk.LEFT)
        self.product_combo = ttk.Combobox(row1, textvariable=self.product_var, width=30)
        self.product_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.product_combo.bind('<<ComboboxSelected>>', self.on_product_select)
        
        ttk.Label(row1, text="الكمية:", width=10).pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.quantity_var, width=10).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(row1, text="السعر:", width=10).pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.unit_price_var, width=10).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(row1, text="خصم:", width=10).pack(side=tk.LEFT)
        ttk.Entry(row1, textvariable=self.item_discount_var, width=10).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(row1, text="إضافة", command=self.add_item).pack(side=tk.LEFT, padx=(10, 0))
    
    def create_items_frame(self, parent):
        """إنشاء إطار عناصر الفاتورة"""
        items_frame = ttk.LabelFrame(parent, text="عناصر الفاتورة", padding=10)
        items_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # جدول العناصر
        columns = ('product', 'quantity', 'unit_price', 'discount', 'total')
        
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show='headings', height=10)
        
        # تعيين عناوين الأعمدة
        self.items_tree.heading('product', text='المنتج')
        self.items_tree.heading('quantity', text='الكمية')
        self.items_tree.heading('unit_price', text='السعر')
        self.items_tree.heading('discount', text='الخصم')
        self.items_tree.heading('total', text='الإجمالي')
        
        # تعيين عرض الأعمدة
        self.items_tree.column('product', width=300)
        self.items_tree.column('quantity', width=80)
        self.items_tree.column('unit_price', width=80)
        self.items_tree.column('discount', width=80)
        self.items_tree.column('total', width=100)
        
        # شريط التمرير
        items_scrollbar = ttk.Scrollbar(items_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        items_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.items_tree.bind('<Delete>', self.delete_item)
        self.items_tree.bind('<Double-1>', self.edit_item)
    
    def create_totals_frame(self, parent):
        """إنشاء إطار الإجماليات"""
        totals_frame = ttk.LabelFrame(parent, text="الإجماليات", padding=10)
        totals_frame.pack(fill=tk.X, pady=(0, 10))
        
        # إطار الخصم والضريبة
        controls_frame = ttk.Frame(totals_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(controls_frame, text="خصم %:", width=10).pack(side=tk.LEFT)
        discount_entry = ttk.Entry(controls_frame, textvariable=self.discount_percentage_var, width=10)
        discount_entry.pack(side=tk.LEFT, padx=(0, 20))
        discount_entry.bind('<KeyRelease>', self.calculate_totals)
        
        ttk.Label(controls_frame, text="ضريبة %:", width=10).pack(side=tk.LEFT)
        tax_entry = ttk.Entry(controls_frame, textvariable=self.tax_percentage_var, width=10)
        tax_entry.pack(side=tk.LEFT, padx=(0, 20))
        tax_entry.bind('<KeyRelease>', self.calculate_totals)
        
        # إطار الإجماليات
        totals_display = ttk.Frame(totals_frame)
        totals_display.pack(fill=tk.X)
        
        # المجموع الفرعي
        ttk.Label(totals_display, text="المجموع الفرعي:", width=15).grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(totals_display, textvariable=self.subtotal_var, width=15, foreground="blue").grid(row=0, column=1, sticky=tk.W, pady=2)
        
        # مبلغ الخصم
        ttk.Label(totals_display, text="مبلغ الخصم:", width=15).grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Label(totals_display, textvariable=self.discount_amount_var, width=15, foreground="red").grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # مبلغ الضريبة
        ttk.Label(totals_display, text="مبلغ الضريبة:", width=15).grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Label(totals_display, textvariable=self.tax_amount_var, width=15, foreground="green").grid(row=2, column=1, sticky=tk.W, pady=2)
        
        # المجموع النهائي
        ttk.Label(totals_display, text="المجموع النهائي:", width=15, font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Label(totals_display, textvariable=self.total_var, width=15, font=('Arial', 12, 'bold'), foreground="darkgreen").grid(row=3, column=1, sticky=tk.W, pady=2)
    
    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # ملاحظات
        ttk.Label(buttons_frame, text="ملاحظات:").pack(side=tk.LEFT)
        ttk.Entry(buttons_frame, textvariable=self.notes_var, width=40).pack(side=tk.LEFT, padx=(5, 20))
        
        # أزرار العمليات
        ttk.Button(buttons_frame, text="حفظ", command=self.save_invoice).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="طباعة", command=self.print_invoice).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.RIGHT)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            # تحميل العملاء/الموردين
            if self.invoice_type == "sale":
                self.customers = self.customer_manager.get_all_customers()
                customer_names = [f"{c.code} - {c.name}" for c in self.customers]
            else:
                # تحميل الموردين (سيتم إضافة SupplierManager لاحقاً)
                customer_names = []
            
            self.customer_combo['values'] = customer_names
            
            # تحميل المنتجات
            query = "SELECT * FROM products WHERE is_active = 1 ORDER BY name"
            rows = self.db.fetch_all(query)
            self.products = []
            product_names = []
            
            for row in rows:
                product = {
                    'id': row['id'],
                    'code': row['code'],
                    'name': row['name'],
                    'selling_price': row['selling_price'],
                    'cost_price': row['cost_price']
                }
                self.products.append(product)
                product_names.append(f"{product['code']} - {product['name']}")
            
            self.product_combo['values'] = product_names
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        try:
            invoice_number = self.invoice_manager.generate_invoice_number(self.invoice_type)
            self.invoice_number_var.set(invoice_number)
            self.invoice.invoice_number = invoice_number
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد رقم الفاتورة: {str(e)}")
    
    def on_product_select(self, event):
        """عند اختيار منتج"""
        selection = self.product_combo.get()
        if selection:
            # استخراج كود المنتج
            product_code = selection.split(' - ')[0]
            
            # البحث عن المنتج
            for product in self.products:
                if product['code'] == product_code:
                    # تعيين السعر
                    if self.invoice_type == "sale":
                        self.unit_price_var.set(product['selling_price'])
                    else:
                        self.unit_price_var.set(product['cost_price'])
                    break
    
    def add_item(self):
        """إضافة عنصر للفاتورة"""
        try:
            # التحقق من البيانات
            if not self.product_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار منتج")
                return
            
            if self.quantity_var.get() <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
                return
            
            if self.unit_price_var.get() <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال سعر صحيح")
                return
            
            # البحث عن المنتج
            product_code = self.product_var.get().split(' - ')[0]
            product = None
            for p in self.products:
                if p['code'] == product_code:
                    product = p
                    break
            
            if not product:
                messagebox.showerror("خطأ", "المنتج غير موجود")
                return
            
            # إنشاء عنصر الفاتورة
            item = InvoiceItem(
                product_id=product['id'],
                product_code=product['code'],
                product_name=product['name'],
                quantity=self.quantity_var.get(),
                unit_price=self.unit_price_var.get(),
                discount_amount=self.item_discount_var.get()
            )
            
            # إضافة العنصر للفاتورة
            self.invoice.add_item(item)
            
            # إضافة العنصر للجدول
            self.items_tree.insert('', tk.END, values=(
                f"{item.product_code} - {item.product_name}",
                f"{item.quantity:.2f}",
                f"{item.unit_price:.2f}",
                f"{item.discount_amount:.2f}",
                f"{item.total_price:.2f}"
            ))
            
            # مسح الحقول
            self.product_var.set("")
            self.quantity_var.set(1.0)
            self.unit_price_var.set(0.0)
            self.item_discount_var.set(0.0)
            
            # حساب الإجماليات
            self.calculate_totals()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة العنصر: {str(e)}")
    
    def delete_item(self, event):
        """حذف عنصر من الفاتورة"""
        selection = self.items_tree.selection()
        if selection:
            if messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذا العنصر؟"):
                # حذف من الجدول
                item_index = self.items_tree.index(selection[0])
                self.items_tree.delete(selection[0])
                
                # حذف من الفاتورة
                self.invoice.remove_item(item_index)
                
                # حساب الإجماليات
                self.calculate_totals()
    
    def edit_item(self, event):
        """تعديل عنصر في الفاتورة"""
        # يمكن إضافة حوار تعديل العنصر هنا
        pass
    
    def calculate_totals(self, event=None):
        """حساب الإجماليات"""
        try:
            # تحديث نسب الخصم والضريبة
            self.invoice.discount_percentage = self.discount_percentage_var.get()
            self.invoice.tax_percentage = self.tax_percentage_var.get()
            
            # حساب الإجماليات
            self.invoice.calculate_totals()
            
            # تحديث العرض
            self.subtotal_var.set(f"{self.invoice.subtotal:.2f} ج.م")
            self.discount_amount_var.set(f"{self.invoice.discount_amount:.2f} ج.م")
            self.tax_amount_var.set(f"{self.invoice.tax_amount:.2f} ج.م")
            self.total_var.set(f"{self.invoice.total_amount:.2f} ج.م")
            
        except Exception as e:
            print(f"خطأ في حساب الإجماليات: {e}")
    
    def save_invoice(self):
        """حفظ الفاتورة"""
        try:
            # التحقق من البيانات
            if not self.invoice.items:
                messagebox.showwarning("تحذير", "يرجى إضافة عنصر واحد على الأقل")
                return
            
            if self.invoice_type == "sale" and not self.customer_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار عميل")
                return
            
            # تحديث بيانات الفاتورة
            self.invoice.invoice_date = datetime.strptime(self.invoice_date_var.get(), '%Y-%m-%d').date()
            if self.due_date_var.get():
                self.invoice.due_date = datetime.strptime(self.due_date_var.get(), '%Y-%m-%d').date()
            
            # تحديد العميل/المورد
            if self.customer_var.get():
                customer_code = self.customer_var.get().split(' - ')[0]
                for customer in self.customers:
                    if customer.code == customer_code:
                        if self.invoice_type == "sale":
                            self.invoice.customer_id = customer.id
                            self.invoice.customer_name = customer.name
                        break
            
            self.invoice.payment_method = self.payment_method_var.get()
            self.invoice.notes = self.notes_var.get()
            
            # حفظ الفاتورة
            invoice_id = self.invoice_manager.create_invoice(self.invoice)
            
            if invoice_id:
                messagebox.showinfo("نجح", f"تم حفظ الفاتورة رقم {self.invoice.invoice_number} بنجاح")
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ الفاتورة")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الفاتورة: {str(e)}")
    
    def print_invoice(self):
        """طباعة الفاتورة"""
        messagebox.showinfo("معلومات", "سيتم إضافة وظيفة الطباعة قريباً")
    
    def cancel(self):
        """إلغاء العملية"""
        if messagebox.askyesno("تأكيد الإلغاء", "هل تريد إلغاء الفاتورة؟"):
            self.window.destroy()
