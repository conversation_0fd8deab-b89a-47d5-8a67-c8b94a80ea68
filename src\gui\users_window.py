# -*- coding: utf-8 -*-
"""
نافذة إدارة المستخدمين
Users Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from typing import Optional

from ..utils.colors import AppColors
from ..models.user import UserManager, User

class UsersWindow:
    """نافذة إدارة المستخدمين"""
    
    def __init__(self, parent, db_manager, config, current_user):
        """تهيئة نافذة إدارة المستخدمين"""
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.current_user = current_user
        self.user_manager = UserManager(db_manager)
        
        # التحقق من الصلاحيات
        if not self.current_user.has_permission("manage_users"):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة المستخدمين")
            return
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.load_users()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("👥 إدارة المستخدمين")
        self.window.geometry("1200x700")
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم أقل من الحد الأدنى
        self.window.minsize(1000, 600)
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.search_var = tk.StringVar()
        self.selected_user = None
        
        # متغيرات نموذج المستخدم
        self.username_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.full_name_var = tk.StringVar()
        self.role_var = tk.StringVar(value="user")
        self.is_active_var = tk.BooleanVar(value=True)
        self.password_var = tk.StringVar()
        self.confirm_password_var = tk.StringVar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # شريط البحث والأزرار
        self.create_toolbar(main_container)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_container, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # قائمة المستخدمين (يسار)
        self.create_users_list(content_frame)
        
        # نموذج المستخدم (يمين)
        self.create_user_form(content_frame)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان (على اليمين للعربية)
        title_label = tk.Label(header_frame,
                              text="👥 إدارة المستخدمين",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.RIGHT)
        
        # زر الإغلاق (على اليسار للعربية)
        close_btn = tk.Button(header_frame,
                             text="✕ إغلاق",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['danger'],
                             relief='flat',
                             padx=20,
                             pady=8,
                             command=self.window.destroy)
        close_btn.pack(side=tk.LEFT)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        toolbar_frame.pack(fill=tk.X, pady=(0, 20))
        
        # الجانب الأيمن - البحث (للعربية)
        search_frame = tk.Frame(toolbar_frame, bg=self.colors['card'])
        search_frame.pack(side=tk.RIGHT, padx=15, pady=15)
        
        search_label = tk.Label(search_frame,
                               text="🔍 البحث:",
                               font=('Segoe UI', 11),
                               fg=self.colors['text'],
                               bg=self.colors['card'])
        search_label.pack(side=tk.RIGHT)
        
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=('Segoe UI', 11),
                               width=25)
        search_entry.pack(side=tk.RIGHT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # الجانب الأيسر - الأزرار (للعربية)
        buttons_frame = tk.Frame(toolbar_frame, bg=self.colors['card'])
        buttons_frame.pack(side=tk.LEFT, padx=15, pady=15)
        
        # أزرار العمليات
        buttons = [
            ("👤 مستخدم جديد", self.new_user, self.colors['primary']),
            ("💾 حفظ", self.save_user, self.colors['success']),
            ("🗑️ حذف", self.delete_user, self.colors['danger']),
            ("🔄 تحديث", self.load_users, self.colors['secondary'])
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(buttons_frame,
                           text=text,
                           font=('Segoe UI', 10),
                           fg='white',
                           bg=color,
                           relief='flat',
                           padx=15,
                           pady=8,
                           command=command)
            btn.pack(side=tk.LEFT, padx=(0, 5))
    
    def create_users_list(self, parent):
        """إنشاء قائمة المستخدمين"""
        # إطار قائمة المستخدمين (على اليسار)
        list_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عنوان القائمة
        title_frame = tk.Frame(list_frame, bg=self.colors['primary'])
        title_frame.pack(fill=tk.X)
        
        title_label = tk.Label(title_frame,
                              text="📋 قائمة المستخدمين",
                              font=('Segoe UI', 12, 'bold'),
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(side=tk.RIGHT, padx=15, pady=8)
        
        # جدول المستخدمين
        table_frame = tk.Frame(list_frame, bg=self.colors['card'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # إعداد الجدول
        columns = ("اسم المستخدم", "الاسم الكامل", "الدور", "الحالة", "آخر دخول")
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.users_tree.heading(col, text=col, anchor='center')
            self.users_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث التحديد
        self.users_tree.bind('<<TreeviewSelect>>', self.on_user_select)
    
    def create_user_form(self, parent):
        """إنشاء نموذج المستخدم"""
        # إطار النموذج (على اليمين)
        form_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        form_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        form_frame.configure(width=400)
        
        # عنوان النموذج
        title_frame = tk.Frame(form_frame, bg=self.colors['success'])
        title_frame.pack(fill=tk.X)
        
        title_label = tk.Label(title_frame,
                              text="📝 بيانات المستخدم",
                              font=('Segoe UI', 12, 'bold'),
                              fg='white',
                              bg=self.colors['success'])
        title_label.pack(side=tk.RIGHT, padx=15, pady=8)
        
        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg=self.colors['card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # الحقول
        fields = [
            ("👤 اسم المستخدم:", self.username_var),
            ("📧 البريد الإلكتروني:", self.email_var),
            ("👨‍💼 الاسم الكامل:", self.full_name_var),
            ("🔒 كلمة المرور:", self.password_var),
            ("🔒 تأكيد كلمة المرور:", self.confirm_password_var)
        ]
        
        for i, (label_text, var) in enumerate(fields):
            field_frame = tk.Frame(content_frame, bg=self.colors['card'])
            field_frame.pack(fill=tk.X, pady=(0, 15))
            
            tk.Label(field_frame,
                    text=label_text,
                    font=('Segoe UI', 10),
                    fg=self.colors['text'],
                    bg=self.colors['card']).pack(anchor='e')
            
            if "كلمة المرور" in label_text:
                entry = tk.Entry(field_frame,
                               textvariable=var,
                               font=('Segoe UI', 10),
                               show='*')
            else:
                entry = tk.Entry(field_frame,
                               textvariable=var,
                               font=('Segoe UI', 10))
            entry.pack(fill=tk.X, pady=(5, 0))
        
        # حقل الدور
        role_frame = tk.Frame(content_frame, bg=self.colors['card'])
        role_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(role_frame,
                text="🎭 الدور:",
                font=('Segoe UI', 10),
                fg=self.colors['text'],
                bg=self.colors['card']).pack(anchor='e')
        
        role_combo = ttk.Combobox(role_frame,
                                 textvariable=self.role_var,
                                 values=["admin", "manager", "user", "viewer"],
                                 state="readonly")
        role_combo.pack(fill=tk.X, pady=(5, 0))
        
        # حقل الحالة
        status_frame = tk.Frame(content_frame, bg=self.colors['card'])
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        status_check = tk.Checkbutton(status_frame,
                                     text="✅ المستخدم نشط",
                                     variable=self.is_active_var,
                                     font=('Segoe UI', 10),
                                     fg=self.colors['text'],
                                     bg=self.colors['card'],
                                     selectcolor=self.colors['card'])
        status_check.pack(anchor='e')
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            # مسح الجدول
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # جلب المستخدمين
            users = self.user_manager.get_all_users()
            
            # إضافة المستخدمين للجدول
            for user in users:
                status = "نشط" if user.is_active else "غير نشط"
                last_login = user.last_login.strftime("%Y-%m-%d %H:%M") if user.last_login else "لم يسجل دخول"
                
                self.users_tree.insert('', 'end', values=(
                    user.username,
                    user.full_name,
                    user.get_role_display(),
                    status,
                    last_login
                ), tags=(user.id,))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المستخدمين: {str(e)}")
    
    def on_user_select(self, event):
        """عند تحديد مستخدم"""
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            user_id = item['tags'][0]
            
            # البحث عن المستخدم
            users = self.user_manager.get_all_users()
            for user in users:
                if user.id == user_id:
                    self.selected_user = user
                    self.load_user_data(user)
                    break
    
    def load_user_data(self, user: User):
        """تحميل بيانات المستخدم في النموذج"""
        self.username_var.set(user.username)
        self.email_var.set(user.email)
        self.full_name_var.set(user.full_name)
        self.role_var.set(user.role)
        self.is_active_var.set(user.is_active)
        self.password_var.set("")
        self.confirm_password_var.set("")
    
    def new_user(self):
        """مستخدم جديد"""
        self.selected_user = None
        self.clear_form()
    
    def clear_form(self):
        """مسح النموذج"""
        self.username_var.set("")
        self.email_var.set("")
        self.full_name_var.set("")
        self.role_var.set("user")
        self.is_active_var.set(True)
        self.password_var.set("")
        self.confirm_password_var.set("")
    
    def save_user(self):
        """حفظ المستخدم"""
        messagebox.showinfo("معلومات", "سيتم إضافة وظيفة حفظ المستخدم قريباً")
    
    def delete_user(self):
        """حذف المستخدم"""
        if not self.selected_user:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        if self.selected_user.id == self.current_user.id:
            messagebox.showerror("خطأ", "لا يمكن حذف المستخدم الحالي")
            return
        
        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المستخدم {self.selected_user.username}؟"):
            messagebox.showinfo("معلومات", "سيتم إضافة وظيفة حذف المستخدم قريباً")
    
    def on_search(self, event=None):
        """البحث في المستخدمين"""
        search_term = self.search_var.get().lower()
        
        # مسح الجدول
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # جلب المستخدمين وتطبيق الفلتر
        users = self.user_manager.get_all_users()
        
        for user in users:
            if (search_term in user.username.lower() or 
                search_term in user.full_name.lower() or
                search_term in user.email.lower()):
                
                status = "نشط" if user.is_active else "غير نشط"
                last_login = user.last_login.strftime("%Y-%m-%d %H:%M") if user.last_login else "لم يسجل دخول"
                
                self.users_tree.insert('', 'end', values=(
                    user.username,
                    user.full_name,
                    user.get_role_display(),
                    status,
                    last_login
                ), tags=(user.id,))
