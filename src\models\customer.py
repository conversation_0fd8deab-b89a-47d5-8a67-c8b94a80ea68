# -*- coding: utf-8 -*-
"""
نموذج العميل
Customer Model
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

@dataclass
class Customer:
    """نموذج بيانات العميل"""
    
    id: Optional[int] = None
    code: str = ""
    name: str = ""
    phone: str = ""
    email: str = ""
    address: str = ""
    city: str = ""
    credit_limit: float = 0.0
    current_balance: float = 0.0
    is_active: bool = True
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """معالجة ما بعد التهيئة"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    @property
    def available_credit(self) -> float:
        """الائتمان المتاح"""
        return self.credit_limit - self.current_balance
    
    @property
    def credit_status(self) -> str:
        """حالة الائتمان"""
        if self.current_balance <= 0:
            return "لا يوجد مديونية"
        elif self.current_balance >= self.credit_limit:
            return "تجاوز حد الائتمان"
        elif self.current_balance >= (self.credit_limit * 0.8):
            return "قريب من حد الائتمان"
        else:
            return "ضمن حد الائتمان"
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'city': self.city,
            'credit_limit': self.credit_limit,
            'current_balance': self.current_balance,
            'is_active': self.is_active,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Customer':
        """إنشاء من قاموس"""
        customer = cls()
        for key, value in data.items():
            if hasattr(customer, key):
                if key in ['created_at', 'updated_at'] and value:
                    if isinstance(value, str):
                        setattr(customer, key, datetime.fromisoformat(value))
                    else:
                        setattr(customer, key, value)
                else:
                    setattr(customer, key, value)
        return customer
    
    def validate(self) -> List[str]:
        """التحقق من صحة البيانات"""
        errors = []
        
        if not self.code.strip():
            errors.append("كود العميل مطلوب")
        
        if not self.name.strip():
            errors.append("اسم العميل مطلوب")
        
        if self.credit_limit < 0:
            errors.append("حد الائتمان لا يمكن أن يكون سالباً")
        
        if self.email and '@' not in self.email:
            errors.append("البريد الإلكتروني غير صحيح")
        
        return errors

@dataclass
class Supplier:
    """نموذج بيانات المورد"""
    
    id: Optional[int] = None
    code: str = ""
    name: str = ""
    phone: str = ""
    email: str = ""
    address: str = ""
    city: str = ""
    current_balance: float = 0.0
    is_active: bool = True
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """معالجة ما بعد التهيئة"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    @property
    def balance_status(self) -> str:
        """حالة الرصيد"""
        if self.current_balance > 0:
            return f"مديون بمبلغ {self.current_balance:.2f}"
        elif self.current_balance < 0:
            return f"دائن بمبلغ {abs(self.current_balance):.2f}"
        else:
            return "لا يوجد رصيد"
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'city': self.city,
            'current_balance': self.current_balance,
            'is_active': self.is_active,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Supplier':
        """إنشاء من قاموس"""
        supplier = cls()
        for key, value in data.items():
            if hasattr(supplier, key):
                if key in ['created_at', 'updated_at'] and value:
                    if isinstance(value, str):
                        setattr(supplier, key, datetime.fromisoformat(value))
                    else:
                        setattr(supplier, key, value)
                else:
                    setattr(supplier, key, value)
        return supplier
    
    def validate(self) -> List[str]:
        """التحقق من صحة البيانات"""
        errors = []
        
        if not self.code.strip():
            errors.append("كود المورد مطلوب")
        
        if not self.name.strip():
            errors.append("اسم المورد مطلوب")
        
        if self.email and '@' not in self.email:
            errors.append("البريد الإلكتروني غير صحيح")
        
        return errors

class CustomerManager:
    """مدير العملاء"""
    
    def __init__(self, db_manager):
        """
        تهيئة مدير العملاء
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def create_customer(self, customer: Customer) -> Optional[int]:
        """
        إنشاء عميل جديد
        
        Args:
            customer: بيانات العميل
            
        Returns:
            معرف العميل الجديد أو None في حالة الفشل
        """
        try:
            # التحقق من صحة البيانات
            errors = customer.validate()
            if errors:
                raise ValueError(f"أخطاء في البيانات: {', '.join(errors)}")
            
            # التحقق من عدم تكرار الكود
            existing = self.get_customer_by_code(customer.code)
            if existing:
                raise ValueError("كود العميل موجود مسبقاً")
            
            # إدراج العميل
            query = """
                INSERT INTO customers (
                    code, name, phone, email, address, city,
                    credit_limit, current_balance, is_active, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor = self.db.execute_query(query, (
                customer.code, customer.name, customer.phone, customer.email,
                customer.address, customer.city, customer.credit_limit,
                customer.current_balance, customer.is_active, customer.notes
            ))
            
            return cursor.lastrowid
            
        except Exception as e:
            print(f"خطأ في إنشاء العميل: {e}")
            raise
    
    def get_customer_by_id(self, customer_id: int) -> Optional[Customer]:
        """
        الحصول على عميل بالمعرف
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            العميل أو None
        """
        query = "SELECT * FROM customers WHERE id = ?"
        row = self.db.fetch_one(query, (customer_id,))
        if row:
            return Customer.from_dict(dict(row))
        return None
    
    def get_customer_by_code(self, code: str) -> Optional[Customer]:
        """
        الحصول على عميل بالكود
        
        Args:
            code: كود العميل
            
        Returns:
            العميل أو None
        """
        query = "SELECT * FROM customers WHERE code = ?"
        row = self.db.fetch_one(query, (code,))
        if row:
            return Customer.from_dict(dict(row))
        return None
    
    def get_all_customers(self, active_only: bool = True) -> List[Customer]:
        """
        الحصول على جميع العملاء
        
        Args:
            active_only: العملاء النشطين فقط
            
        Returns:
            قائمة العملاء
        """
        query = "SELECT * FROM customers"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY name"
        
        rows = self.db.fetch_all(query)
        return [Customer.from_dict(dict(row)) for row in rows]

class SupplierManager:
    """مدير الموردين"""
    
    def __init__(self, db_manager):
        """
        تهيئة مدير الموردين
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def create_supplier(self, supplier: Supplier) -> Optional[int]:
        """
        إنشاء مورد جديد
        
        Args:
            supplier: بيانات المورد
            
        Returns:
            معرف المورد الجديد أو None في حالة الفشل
        """
        try:
            # التحقق من صحة البيانات
            errors = supplier.validate()
            if errors:
                raise ValueError(f"أخطاء في البيانات: {', '.join(errors)}")
            
            # التحقق من عدم تكرار الكود
            existing = self.get_supplier_by_code(supplier.code)
            if existing:
                raise ValueError("كود المورد موجود مسبقاً")
            
            # إدراج المورد
            query = """
                INSERT INTO suppliers (
                    code, name, phone, email, address, city,
                    current_balance, is_active, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor = self.db.execute_query(query, (
                supplier.code, supplier.name, supplier.phone, supplier.email,
                supplier.address, supplier.city, supplier.current_balance,
                supplier.is_active, supplier.notes
            ))
            
            return cursor.lastrowid
            
        except Exception as e:
            print(f"خطأ في إنشاء المورد: {e}")
            raise
