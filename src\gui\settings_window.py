# -*- coding: utf-8 -*-
"""
نافذة الإعدادات
Settings Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime

from ..utils.colors import AppColors

class SettingsWindow:
    """نافذة الإعدادات"""
    
    def __init__(self, parent, db_manager, config):
        """تهيئة نافذة الإعدادات"""
        self.parent = parent
        self.db = db_manager
        self.config = config
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات الإعدادات
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل الإعدادات الحالية
        self.load_settings()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("⚙️ إعدادات النظام")
        self.window.geometry("900x700")
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_variables(self):
        """إعداد متغيرات الإعدادات"""
        # إعدادات الشركة
        self.company_name_var = tk.StringVar()
        self.company_address_var = tk.StringVar()
        self.company_phone_var = tk.StringVar()
        self.company_email_var = tk.StringVar()
        self.company_tax_number_var = tk.StringVar()
        self.company_logo_path_var = tk.StringVar()
        
        # إعدادات العملة
        self.currency_name_var = tk.StringVar()
        self.currency_symbol_var = tk.StringVar()
        self.currency_code_var = tk.StringVar()
        
        # إعدادات النظام
        self.auto_backup_var = tk.BooleanVar()
        self.backup_interval_var = tk.StringVar()
        self.language_var = tk.StringVar()
        self.theme_var = tk.StringVar()
        self.invoice_prefix_var = tk.StringVar()
        self.receipt_printer_var = tk.StringVar()
        
        # إعدادات الضرائب
        self.default_tax_rate_var = tk.DoubleVar()
        self.tax_included_var = tk.BooleanVar()
        
        # إعدادات الأمان
        self.session_timeout_var = tk.IntVar()
        self.password_policy_var = tk.BooleanVar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # إنشاء التبويبات
        self.create_tabs(main_container)
        
        # أزرار العمليات
        self.create_buttons(main_container)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان (على اليمين للعربية)
        title_label = tk.Label(header_frame,
                              text="⚙️ إعدادات النظام",
                              font=('Segoe UI', 18, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.RIGHT)
        
        # خط فاصل
        separator = tk.Frame(header_frame, height=2, bg=self.colors['border'])
        separator.pack(fill=tk.X, pady=(10, 0))
    
    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        tabs_frame = tk.Frame(parent, bg=self.colors['background'])
        tabs_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(tabs_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب إعدادات الشركة
        self.create_company_tab()
        
        # تبويب إعدادات العملة والضرائب
        self.create_currency_tab()
        
        # تبويب إعدادات النظام
        self.create_system_tab()
        
        # تبويب إعدادات الأمان
        self.create_security_tab()
        
        # تبويب النسخ الاحتياطي
        self.create_backup_tab()
    
    def create_company_tab(self):
        """إنشاء تبويب إعدادات الشركة"""
        company_frame = ttk.Frame(self.notebook)
        self.notebook.add(company_frame, text="🏢 بيانات الشركة")
        
        # إطار المحتوى
        content_frame = tk.Frame(company_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الحقول
        fields = [
            ("اسم الشركة:", self.company_name_var, True),
            ("العنوان:", self.company_address_var, False),
            ("الهاتف:", self.company_phone_var, False),
            ("البريد الإلكتروني:", self.company_email_var, False),
            ("الرقم الضريبي:", self.company_tax_number_var, False)
        ]
        
        for i, (label_text, var, required) in enumerate(fields):
            self.create_field(content_frame, label_text, var, "text", required, i)
        
        # حقل شعار الشركة
        logo_frame = tk.Frame(content_frame, bg=self.colors['background'])
        logo_frame.grid(row=len(fields), column=0, columnspan=2, sticky='ew', pady=10)
        
        logo_label = tk.Label(logo_frame,
                             text="شعار الشركة:",
                             font=('Segoe UI', 11, 'bold'),
                             fg=self.colors['text'],
                             bg=self.colors['background'])
        logo_label.pack(side=tk.RIGHT)
        
        logo_path_entry = tk.Entry(logo_frame,
                                  textvariable=self.company_logo_path_var,
                                  font=('Segoe UI', 11),
                                  width=40,
                                  state='readonly')
        logo_path_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        browse_btn = tk.Button(logo_frame,
                              text="تصفح",
                              font=('Segoe UI', 10),
                              command=self.browse_logo,
                              bg=self.colors['secondary'],
                              fg='white',
                              relief='flat',
                              padx=15)
        browse_btn.pack(side=tk.LEFT)
        
        # تكوين الشبكة
        content_frame.grid_columnconfigure(1, weight=1)
    
    def create_currency_tab(self):
        """إنشاء تبويب إعدادات العملة والضرائب"""
        currency_frame = ttk.Frame(self.notebook)
        self.notebook.add(currency_frame, text="💰 العملة والضرائب")
        
        # إطار المحتوى
        content_frame = tk.Frame(currency_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # قسم العملة
        currency_section = tk.LabelFrame(content_frame,
                                        text="إعدادات العملة",
                                        font=('Segoe UI', 12, 'bold'),
                                        fg=self.colors['primary'],
                                        bg=self.colors['background'])
        currency_section.pack(fill=tk.X, pady=(0, 20))
        
        currency_fields = [
            ("اسم العملة:", self.currency_name_var),
            ("رمز العملة:", self.currency_symbol_var),
            ("كود العملة:", self.currency_code_var)
        ]
        
        for i, (label_text, var) in enumerate(currency_fields):
            self.create_field(currency_section, label_text, var, "text", True, i)
        
        # قسم الضرائب
        tax_section = tk.LabelFrame(content_frame,
                                   text="إعدادات الضرائب",
                                   font=('Segoe UI', 12, 'bold'),
                                   fg=self.colors['primary'],
                                   bg=self.colors['background'])
        tax_section.pack(fill=tk.X)
        
        # معدل الضريبة الافتراضي
        self.create_field(tax_section, "معدل الضريبة الافتراضي (%):", self.default_tax_rate_var, "number", False, 0)
        
        # الضريبة مشمولة في السعر
        tax_included_frame = tk.Frame(tax_section, bg=self.colors['background'])
        tax_included_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=10)
        
        tax_included_check = tk.Checkbutton(tax_included_frame,
                                           text="الضريبة مشمولة في السعر",
                                           variable=self.tax_included_var,
                                           font=('Segoe UI', 11),
                                           fg=self.colors['text'],
                                           bg=self.colors['background'])
        tax_included_check.pack(side=tk.RIGHT)
    
    def create_system_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        system_frame = ttk.Frame(self.notebook)
        self.notebook.add(system_frame, text="🖥️ النظام")
        
        # إطار المحتوى
        content_frame = tk.Frame(system_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إعدادات الفواتير
        invoice_section = tk.LabelFrame(content_frame,
                                       text="إعدادات الفواتير",
                                       font=('Segoe UI', 12, 'bold'),
                                       fg=self.colors['primary'],
                                       bg=self.colors['background'])
        invoice_section.pack(fill=tk.X, pady=(0, 20))
        
        self.create_field(invoice_section, "بادئة رقم الفاتورة:", self.invoice_prefix_var, "text", False, 0)
        
        # إعدادات الطباعة
        printer_section = tk.LabelFrame(content_frame,
                                       text="إعدادات الطباعة",
                                       font=('Segoe UI', 12, 'bold'),
                                       fg=self.colors['primary'],
                                       bg=self.colors['background'])
        printer_section.pack(fill=tk.X, pady=(0, 20))
        
        self.create_field(printer_section, "طابعة الإيصالات:", self.receipt_printer_var, "text", False, 0)
        
        # إعدادات اللغة والمظهر
        appearance_section = tk.LabelFrame(content_frame,
                                          text="اللغة والمظهر",
                                          font=('Segoe UI', 12, 'bold'),
                                          fg=self.colors['primary'],
                                          bg=self.colors['background'])
        appearance_section.pack(fill=tk.X)
        
        # اللغة
        lang_frame = tk.Frame(appearance_section, bg=self.colors['background'])
        lang_frame.grid(row=0, column=0, columnspan=2, sticky='ew', pady=5)
        
        lang_label = tk.Label(lang_frame,
                             text="اللغة:",
                             font=('Segoe UI', 11, 'bold'),
                             fg=self.colors['text'],
                             bg=self.colors['background'])
        lang_label.pack(side=tk.RIGHT)
        
        lang_combo = ttk.Combobox(lang_frame,
                                 textvariable=self.language_var,
                                 values=["العربية", "English"],
                                 state="readonly",
                                 width=30)
        lang_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # المظهر
        theme_frame = tk.Frame(appearance_section, bg=self.colors['background'])
        theme_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=5)
        
        theme_label = tk.Label(theme_frame,
                              text="المظهر:",
                              font=('Segoe UI', 11, 'bold'),
                              fg=self.colors['text'],
                              bg=self.colors['background'])
        theme_label.pack(side=tk.RIGHT)
        
        theme_combo = ttk.Combobox(theme_frame,
                                  textvariable=self.theme_var,
                                  values=["فاتح", "داكن", "أزرق"],
                                  state="readonly",
                                  width=30)
        theme_combo.pack(side=tk.LEFT, padx=(0, 10))
        theme_combo.bind('<<ComboboxSelected>>', self.on_theme_change)
    
    def create_security_tab(self):
        """إنشاء تبويب إعدادات الأمان"""
        security_frame = ttk.Frame(self.notebook)
        self.notebook.add(security_frame, text="🔒 الأمان")
        
        # إطار المحتوى
        content_frame = tk.Frame(security_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إعدادات الجلسة
        session_section = tk.LabelFrame(content_frame,
                                       text="إعدادات الجلسة",
                                       font=('Segoe UI', 12, 'bold'),
                                       fg=self.colors['primary'],
                                       bg=self.colors['background'])
        session_section.pack(fill=tk.X, pady=(0, 20))
        
        self.create_field(session_section, "انتهاء الجلسة (دقيقة):", self.session_timeout_var, "number", False, 0)
        
        # سياسة كلمة المرور
        password_frame = tk.Frame(session_section, bg=self.colors['background'])
        password_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=10)
        
        password_check = tk.Checkbutton(password_frame,
                                       text="تطبيق سياسة كلمة مرور قوية",
                                       variable=self.password_policy_var,
                                       font=('Segoe UI', 11),
                                       fg=self.colors['text'],
                                       bg=self.colors['background'])
        password_check.pack(side=tk.RIGHT)
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_frame = ttk.Frame(self.notebook)
        self.notebook.add(backup_frame, text="💾 النسخ الاحتياطي")
        
        # إطار المحتوى
        content_frame = tk.Frame(backup_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # النسخ الاحتياطي التلقائي
        auto_backup_frame = tk.Frame(content_frame, bg=self.colors['background'])
        auto_backup_frame.pack(fill=tk.X, pady=(0, 20))
        
        auto_backup_check = tk.Checkbutton(auto_backup_frame,
                                          text="تفعيل النسخ الاحتياطي التلقائي",
                                          variable=self.auto_backup_var,
                                          font=('Segoe UI', 12, 'bold'),
                                          fg=self.colors['primary'],
                                          bg=self.colors['background'])
        auto_backup_check.pack(side=tk.RIGHT)
        
        # فترة النسخ الاحتياطي
        interval_frame = tk.Frame(content_frame, bg=self.colors['background'])
        interval_frame.pack(fill=tk.X, pady=(0, 20))
        
        interval_label = tk.Label(interval_frame,
                                 text="فترة النسخ الاحتياطي:",
                                 font=('Segoe UI', 11, 'bold'),
                                 fg=self.colors['text'],
                                 bg=self.colors['background'])
        interval_label.pack(side=tk.RIGHT)
        
        interval_combo = ttk.Combobox(interval_frame,
                                     textvariable=self.backup_interval_var,
                                     values=["يومي", "أسبوعي", "شهري"],
                                     state="readonly",
                                     width=30)
        interval_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # أزرار النسخ الاحتياطي
        backup_buttons_frame = tk.Frame(content_frame, bg=self.colors['background'])
        backup_buttons_frame.pack(fill=tk.X)
        
        create_backup_btn = tk.Button(backup_buttons_frame,
                                     text="💾 إنشاء نسخة احتياطية الآن",
                                     font=('Segoe UI', 11),
                                     bg=self.colors['success'],
                                     fg='white',
                                     relief='flat',
                                     padx=20,
                                     pady=10,
                                     command=self.create_backup)
        create_backup_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        restore_backup_btn = tk.Button(backup_buttons_frame,
                                      text="📂 استعادة نسخة احتياطية",
                                      font=('Segoe UI', 11),
                                      bg=self.colors['warning'],
                                      fg='white',
                                      relief='flat',
                                      padx=20,
                                      pady=10,
                                      command=self.restore_backup)
        restore_backup_btn.pack(side=tk.RIGHT)
    
    def create_field(self, parent, label_text, var, field_type, required, row):
        """إنشاء حقل إدخال محسن"""
        # إطار الحقل الكامل
        field_container = tk.Frame(parent, bg=self.colors['background'])
        field_container.grid(row=row, column=0, columnspan=2, sticky='ew', pady=8, padx=10)
        
        # تسمية الحقل
        label_frame = tk.Frame(field_container, bg=self.colors['background'])
        label_frame.pack(fill=tk.X, pady=(0, 5))
        
        label = tk.Label(label_frame,
                        text=label_text,
                        font=('Segoe UI', 11, 'bold'),
                        fg='#2c3e50',
                        bg=self.colors['background'])
        label.pack(side=tk.RIGHT)
        
        if required:
            required_label = tk.Label(label_frame,
                                     text=" *",
                                     font=('Segoe UI', 11, 'bold'),
                                     fg='#e74c3c',
                                     bg=self.colors['background'])
            required_label.pack(side=tk.RIGHT)
        
        # إطار الإدخال مع حدود
        entry_container = tk.Frame(field_container, bg='#ecf0f1', relief='flat', bd=1)
        entry_container.pack(fill=tk.X, ipady=2, ipadx=2)
        
        # حقل الإدخال
        if field_type == "text":
            entry = tk.Entry(entry_container,
                            textvariable=var,
                            font=('Segoe UI', 12),
                            relief='flat',
                            bd=0,
                            bg='white',
                            fg='#2c3e50',
                            insertbackground='#3498db')
            entry.pack(fill=tk.X, padx=8, pady=6)
            
            # تأثيرات التفاعل
            def on_focus_in(event):
                entry_container.config(bg='#3498db')
                entry.config(bg='#ffffff')
            
            def on_focus_out(event):
                entry_container.config(bg='#ecf0f1')
                entry.config(bg='white')
            
            entry.bind('<FocusIn>', on_focus_in)
            entry.bind('<FocusOut>', on_focus_out)
        
        elif field_type == "number":
            entry = tk.Entry(entry_container,
                            textvariable=var,
                            font=('Segoe UI', 12),
                            relief='flat',
                            bd=0,
                            bg='white',
                            fg='#2c3e50',
                            insertbackground='#3498db')
            entry.pack(fill=tk.X, padx=8, pady=6)
            
            # تأثيرات التفاعل
            def on_focus_in_num(event):
                entry_container.config(bg='#3498db')
                entry.config(bg='#ffffff')
            
            def on_focus_out_num(event):
                entry_container.config(bg='#ecf0f1')
                entry.config(bg='white')
            
            entry.bind('<FocusIn>', on_focus_in_num)
            entry.bind('<FocusOut>', on_focus_out_num)
            
            # التحقق من الأرقام فقط
            def validate_number(char):
                return char.isdigit() or char in '.-'
            
            vcmd = (parent.register(validate_number), '%S')
            entry.config(validate='key', validatecommand=vcmd)
        
        # تكوين التوسع
        field_container.grid_columnconfigure(0, weight=1)
    
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات محسنة"""
        buttons_frame = tk.Frame(parent, bg=self.colors['background'])
        buttons_frame.pack(fill=tk.X, pady=20)
        
        # زر الحفظ
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ الإعدادات",
                            font=('Segoe UI', 12, 'bold'),
                            fg='white',
                            bg='#27ae60',
                            activebackground='#2ecc71',
                            activeforeground='white',
                            relief='flat',
                            padx=30,
                            pady=12,
                            cursor='hand2',
                            command=self.save_settings)
        save_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # تأثيرات زر الحفظ
        def on_save_enter(event):
            save_btn.config(bg='#2ecc71')
        def on_save_leave(event):
            save_btn.config(bg='#27ae60')
        
        save_btn.bind('<Enter>', on_save_enter)
        save_btn.bind('<Leave>', on_save_leave)
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              font=('Segoe UI', 12),
                              fg='#2c3e50',
                              bg='#ecf0f1',
                              activebackground='#bdc3c7',
                              activeforeground='#2c3e50',
                              relief='flat',
                              padx=30,
                              pady=12,
                              cursor='hand2',
                              command=self.window.destroy)
        cancel_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        # تأثيرات زر الإلغاء
        def on_cancel_enter(event):
            cancel_btn.config(bg='#bdc3c7')
        def on_cancel_leave(event):
            cancel_btn.config(bg='#ecf0f1')
        
        cancel_btn.bind('<Enter>', on_cancel_enter)
        cancel_btn.bind('<Leave>', on_cancel_leave)
        
        # زر الافتراضي
        default_btn = tk.Button(buttons_frame,
                               text="🔄 الإعدادات الافتراضية",
                               font=('Segoe UI', 12),
                               fg='white',
                               bg='#f39c12',
                               activebackground='#e67e22',
                               activeforeground='white',
                               relief='flat',
                               padx=25,
                               pady=12,
                               cursor='hand2',
                               command=self.reset_to_defaults)
        default_btn.pack(side=tk.LEFT)
        
        # تأثيرات زر الافتراضي
        def on_default_enter(event):
            default_btn.config(bg='#e67e22')
        def on_default_leave(event):
            default_btn.config(bg='#f39c12')
        
        default_btn.bind('<Enter>', on_default_enter)
        default_btn.bind('<Leave>', on_default_leave)
    
    def load_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            # تحميل من ملف الإعدادات
            settings = self.config.get_all_settings()
            
            # إعدادات الشركة
            company = settings.get('company', {})
            self.company_name_var.set(company.get('name', ''))
            self.company_address_var.set(company.get('address', ''))
            self.company_phone_var.set(company.get('phone', ''))
            self.company_email_var.set(company.get('email', ''))
            self.company_tax_number_var.set(company.get('tax_number', ''))
            self.company_logo_path_var.set(company.get('logo_path', ''))
            
            # إعدادات العملة
            currency = settings.get('currency', {})
            self.currency_name_var.set(currency.get('name', 'جنيه مصري'))
            self.currency_symbol_var.set(currency.get('symbol', 'ج.م'))
            self.currency_code_var.set(currency.get('code', 'EGP'))
            
            # إعدادات النظام
            system = settings.get('system', {})
            self.auto_backup_var.set(system.get('auto_backup', False))
            self.backup_interval_var.set(system.get('backup_interval', 'يومي'))
            self.language_var.set(system.get('language', 'العربية'))
            self.theme_var.set(system.get('theme', 'فاتح'))
            self.invoice_prefix_var.set(system.get('invoice_prefix', 'INV'))
            self.receipt_printer_var.set(system.get('receipt_printer', ''))
            
            # إعدادات الضرائب
            tax = settings.get('tax', {})
            self.default_tax_rate_var.set(tax.get('default_rate', 14.0))
            self.tax_included_var.set(tax.get('included', False))
            
            # إعدادات الأمان
            security = settings.get('security', {})
            self.session_timeout_var.set(security.get('session_timeout', 30))
            self.password_policy_var.set(security.get('password_policy', False))
            
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # تجميع الإعدادات
            settings = {
                'company': {
                    'name': self.company_name_var.get(),
                    'address': self.company_address_var.get(),
                    'phone': self.company_phone_var.get(),
                    'email': self.company_email_var.get(),
                    'tax_number': self.company_tax_number_var.get(),
                    'logo_path': self.company_logo_path_var.get()
                },
                'currency': {
                    'name': self.currency_name_var.get(),
                    'symbol': self.currency_symbol_var.get(),
                    'code': self.currency_code_var.get()
                },
                'system': {
                    'auto_backup': self.auto_backup_var.get(),
                    'backup_interval': self.backup_interval_var.get(),
                    'language': self.language_var.get(),
                    'theme': self.theme_var.get(),
                    'invoice_prefix': self.invoice_prefix_var.get(),
                    'receipt_printer': self.receipt_printer_var.get()
                },
                'tax': {
                    'default_rate': self.default_tax_rate_var.get(),
                    'included': self.tax_included_var.get()
                },
                'security': {
                    'session_timeout': self.session_timeout_var.get(),
                    'password_policy': self.password_policy_var.get()
                }
            }
            
            # حفظ الإعدادات
            self.config.save_settings(settings)
            
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟"):
            # إعادة تعيين القيم
            self.company_name_var.set("")
            self.company_address_var.set("")
            self.company_phone_var.set("")
            self.company_email_var.set("")
            self.company_tax_number_var.set("")
            self.company_logo_path_var.set("")
            
            self.currency_name_var.set("جنيه مصري")
            self.currency_symbol_var.set("ج.م")
            self.currency_code_var.set("EGP")
            
            self.auto_backup_var.set(False)
            self.backup_interval_var.set("يومي")
            self.language_var.set("العربية")
            self.theme_var.set("فاتح")
            self.invoice_prefix_var.set("INV")
            self.receipt_printer_var.set("")
            
            self.default_tax_rate_var.set(14.0)
            self.tax_included_var.set(False)
            
            self.session_timeout_var.set(30)
            self.password_policy_var.set(False)
    
    def browse_logo(self):
        """تصفح شعار الشركة"""
        file_path = filedialog.askopenfilename(
            title="اختر شعار الشركة",
            filetypes=[("ملفات الصور", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )
        if file_path:
            self.company_logo_path_var.set(file_path)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            # اختيار مكان الحفظ
            backup_path = filedialog.asksaveasfilename(
                title="حفظ النسخة الاحتياطية",
                defaultextension=".db",
                filetypes=[("ملفات قاعدة البيانات", "*.db")]
            )
            
            if backup_path:
                # نسخ قاعدة البيانات
                import shutil
                shutil.copy2(self.db.db_path, backup_path)
                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية في:\n{backup_path}")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        if messagebox.askyesno("تحذير", "استعادة النسخة الاحتياطية ستحل محل البيانات الحالية. هل تريد المتابعة؟"):
            try:
                # اختيار النسخة الاحتياطية
                backup_path = filedialog.askopenfilename(
                    title="اختر النسخة الاحتياطية",
                    filetypes=[("ملفات قاعدة البيانات", "*.db")]
                )
                
                if backup_path:
                    # استعادة قاعدة البيانات
                    import shutil
                    shutil.copy2(backup_path, self.db.db_path)
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح\nيرجى إعادة تشغيل البرنامج")
            
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}")

    def on_theme_change(self, event=None):
        """عند تغيير المظهر"""
        theme_name = self.theme_var.get()

        # تحويل الأسماء العربية إلى الإنجليزية
        theme_mapping = {
            "فاتح": "light",
            "داكن": "dark",
            "أزرق": "blue"
        }

        theme_key = theme_mapping.get(theme_name, "light")

        try:
            # تطبيق المظهر الجديد
            from ..utils.colors import AppColors
            AppColors.apply_theme(theme_key)

            # حفظ الإعداد في قاعدة البيانات
            self.db.set_setting("appearance", "theme", theme_key)

            messagebox.showinfo("تغيير المظهر",
                               "تم تغيير المظهر بنجاح\nسيتم تطبيق التغييرات عند إعادة تشغيل البرنامج")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تغيير المظهر: {str(e)}")
