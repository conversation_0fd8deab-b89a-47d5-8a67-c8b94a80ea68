# -*- coding: utf-8 -*-
"""
نموذج المورد
Supplier Model
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

@dataclass
class Supplier:
    """نموذج المورد"""
    id: Optional[int] = None
    code: str = ""
    name: str = ""
    contact_person: str = ""
    phone: str = ""
    email: str = ""
    address: str = ""
    city: str = ""
    country: str = "مصر"
    tax_number: str = ""
    payment_terms: str = ""
    credit_limit: float = 0.0
    current_balance: float = 0.0
    notes: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """تهيئة بعد الإنشاء"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Supplier':
        """إنشاء مورد من قاموس"""
        # تحويل التواريخ من نص إلى datetime
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل المورد إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'contact_person': self.contact_person,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'city': self.city,
            'country': self.country,
            'tax_number': self.tax_number,
            'payment_terms': self.payment_terms,
            'credit_limit': self.credit_limit,
            'current_balance': self.current_balance,
            'notes': self.notes,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def validate(self) -> List[str]:
        """التحقق من صحة البيانات"""
        errors = []
        
        if not self.code.strip():
            errors.append("كود المورد مطلوب")
        
        if not self.name.strip():
            errors.append("اسم المورد مطلوب")
        
        if self.email and '@' not in self.email:
            errors.append("البريد الإلكتروني غير صحيح")
        
        if self.credit_limit < 0:
            errors.append("حد الائتمان لا يمكن أن يكون سالباً")
        
        return errors

class SupplierManager:
    """مدير الموردين"""
    
    def __init__(self, db_manager):
        """
        تهيئة مدير الموردين
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def create_supplier(self, supplier: Supplier) -> int:
        """
        إنشاء مورد جديد
        
        Args:
            supplier: بيانات المورد
            
        Returns:
            معرف المورد الجديد
        """
        # التحقق من صحة البيانات
        errors = supplier.validate()
        if errors:
            raise ValueError(f"بيانات غير صحيحة: {', '.join(errors)}")
        
        # التحقق من عدم تكرار الكود
        if self.get_supplier_by_code(supplier.code):
            raise ValueError("كود المورد موجود مسبقاً")
        
        query = """
            INSERT INTO suppliers (
                code, name, contact_person, phone, email, address, city, country,
                tax_number, payment_terms, credit_limit, current_balance, notes,
                is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        supplier.created_at = datetime.now()
        supplier.updated_at = datetime.now()
        
        result = self.db.execute_query(query, (
            supplier.code, supplier.name, supplier.contact_person, supplier.phone,
            supplier.email, supplier.address, supplier.city, supplier.country,
            supplier.tax_number, supplier.payment_terms, supplier.credit_limit,
            supplier.current_balance, supplier.notes, supplier.is_active,
            supplier.created_at, supplier.updated_at
        ))
        
        return result
    
    def get_supplier_by_id(self, supplier_id: int) -> Optional[Supplier]:
        """
        الحصول على مورد بالمعرف
        
        Args:
            supplier_id: معرف المورد
            
        Returns:
            المورد أو None
        """
        query = "SELECT * FROM suppliers WHERE id = ?"
        row = self.db.fetch_one(query, (supplier_id,))
        
        if row:
            return Supplier.from_dict(dict(row))
        return None
    
    def get_supplier_by_code(self, code: str) -> Optional[Supplier]:
        """
        الحصول على مورد بالكود
        
        Args:
            code: كود المورد
            
        Returns:
            المورد أو None
        """
        query = "SELECT * FROM suppliers WHERE code = ?"
        row = self.db.fetch_one(query, (code,))
        
        if row:
            return Supplier.from_dict(dict(row))
        return None
    
    def get_all_suppliers(self) -> List[Supplier]:
        """الحصول على جميع الموردين"""
        try:
            query = "SELECT * FROM suppliers ORDER BY name"
            rows = self.db.fetch_all(query)
            return [Supplier.from_dict(dict(row)) for row in rows]
        except Exception as e:
            print(f"خطأ في الحصول على الموردين: {e}")
            return []
    
    def update_supplier(self, supplier: Supplier) -> bool:
        """تحديث مورد"""
        try:
            # التحقق من صحة البيانات
            errors = supplier.validate()
            if errors:
                raise ValueError(f"بيانات غير صحيحة: {', '.join(errors)}")
            
            query = """
                UPDATE suppliers SET
                    code = ?, name = ?, contact_person = ?, phone = ?, email = ?,
                    address = ?, city = ?, country = ?, tax_number = ?, payment_terms = ?,
                    credit_limit = ?, current_balance = ?, notes = ?, is_active = ?, updated_at = ?
                WHERE id = ?
            """
            
            supplier.updated_at = datetime.now()
            
            self.db.execute_query(query, (
                supplier.code, supplier.name, supplier.contact_person, supplier.phone,
                supplier.email, supplier.address, supplier.city, supplier.country,
                supplier.tax_number, supplier.payment_terms, supplier.credit_limit,
                supplier.current_balance, supplier.notes, supplier.is_active,
                supplier.updated_at, supplier.id
            ))
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحديث المورد: {e}")
            return False
    
    def delete_supplier(self, supplier_id: int) -> bool:
        """حذف مورد"""
        try:
            query = "DELETE FROM suppliers WHERE id = ?"
            self.db.execute_query(query, (supplier_id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف المورد: {e}")
            return False
    
    def search_suppliers(self, search_term: str) -> List[Supplier]:
        """البحث في الموردين"""
        try:
            query = """
                SELECT * FROM suppliers 
                WHERE name LIKE ? OR code LIKE ? OR contact_person LIKE ?
                ORDER BY name
            """
            search_pattern = f"%{search_term}%"
            rows = self.db.fetch_all(query, (search_pattern, search_pattern, search_pattern))
            return [Supplier.from_dict(dict(row)) for row in rows]
        except Exception as e:
            print(f"خطأ في البحث عن الموردين: {e}")
            return []
