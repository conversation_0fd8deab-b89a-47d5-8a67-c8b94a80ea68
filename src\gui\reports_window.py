# -*- coding: utf-8 -*-
"""
نافذة التقارير
Reports Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta

from ..utils.colors import AppColors

class ReportsWindow:
    """نافذة التقارير"""
    
    def __init__(self, parent, db_manager, config):
        """تهيئة نافذة التقارير"""
        self.parent = parent
        self.db = db_manager
        self.config = config
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات التقارير
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 التقارير والإحصائيات")
        self.window.geometry("1400x900")
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم أقل من الحد الأدنى
        self.window.minsize(1200, 700)
    
    def setup_variables(self):
        """إعداد متغيرات التقارير"""
        # فترة التقرير
        self.report_period_var = tk.StringVar(value="هذا الشهر")
        self.start_date_var = tk.StringVar()
        self.end_date_var = tk.StringVar()
        
        # نوع التقرير
        self.report_type_var = tk.StringVar(value="المبيعات")
        
        # تعيين التواريخ الافتراضية
        today = datetime.now()
        start_of_month = today.replace(day=1)
        self.start_date_var.set(start_of_month.strftime("%Y-%m-%d"))
        self.end_date_var.set(today.strftime("%Y-%m-%d"))
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # شريط التحكم
        self.create_control_panel(main_container)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_container, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # إنشاء التبويبات
        self.create_tabs(content_frame)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان (على اليمين للعربية)
        title_label = tk.Label(header_frame,
                              text="�� التقارير والإحصائيات",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.RIGHT)
        
        # زر الإغلاق (على اليسار للعربية)
        close_btn = tk.Button(header_frame,
                             text="✕ إغلاق",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['danger'],
                             relief='flat',
                             padx=20,
                             pady=8,
                             command=self.window.destroy)
        close_btn.pack(side=tk.LEFT)
    
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        control_frame.pack(fill=tk.X, pady=(0, 20))
        
        # الحاوي الداخلي
        inner_frame = tk.Frame(control_frame, bg=self.colors['card'])
        inner_frame.pack(fill=tk.X, padx=20, pady=15)
        
        # الصف الأول - اختيار الفترة
        period_frame = tk.Frame(inner_frame, bg=self.colors['card'])
        period_frame.pack(fill=tk.X, pady=(0, 15))
        
        # عنوان الفترة
        period_label = tk.Label(period_frame,
                               text="📅 فترة التقرير:",
                               font=('Segoe UI', 12, 'bold'),
                               fg=self.colors['text'],
                               bg=self.colors['card'])
        period_label.pack(side=tk.RIGHT, padx=(0, 10))
        
        # قائمة الفترات المحددة مسبقاً
        period_combo = ttk.Combobox(period_frame,
                                   textvariable=self.report_period_var,
                                   values=["اليوم", "أمس", "هذا الأسبوع", "الأسبوع الماضي", 
                                          "هذا الشهر", "الشهر الماضي", "هذا العام", "فترة مخصصة"],
                                   state="readonly",
                                   width=15,
                                   font=('Segoe UI', 11))
        period_combo.pack(side=tk.RIGHT, padx=(0, 20))
        period_combo.bind('<<ComboboxSelected>>', self.on_period_change)
        
        # التواريخ المخصصة
        dates_frame = tk.Frame(period_frame, bg=self.colors['card'])
        dates_frame.pack(side=tk.RIGHT, padx=(0, 20))
        
        # تاريخ البداية
        tk.Label(dates_frame, text="من:", font=('Segoe UI', 10),
                fg=self.colors['text'], bg=self.colors['card']).pack(side=tk.RIGHT, padx=(0, 5))
        
        self.start_date_entry = tk.Entry(dates_frame,
                                        textvariable=self.start_date_var,
                                        width=12,
                                        font=('Segoe UI', 10),
                                        justify='center')
        self.start_date_entry.pack(side=tk.RIGHT, padx=(0, 10))
        
        # تاريخ النهاية
        tk.Label(dates_frame, text="إلى:", font=('Segoe UI', 10),
                fg=self.colors['text'], bg=self.colors['card']).pack(side=tk.RIGHT, padx=(0, 5))
        
        self.end_date_entry = tk.Entry(dates_frame,
                                      textvariable=self.end_date_var,
                                      width=12,
                                      font=('Segoe UI', 10),
                                      justify='center')
        self.end_date_entry.pack(side=tk.RIGHT)
        
        # الصف الثاني - أزرار التحكم
        buttons_frame = tk.Frame(inner_frame, bg=self.colors['card'])
        buttons_frame.pack(fill=tk.X)
        
        # زر إنشاء التقرير
        generate_btn = tk.Button(buttons_frame,
                               text="📊 إنشاء التقرير",
                               font=('Segoe UI', 11, 'bold'),
                               fg='white',
                               bg=self.colors['primary'],
                               relief='flat',
                               padx=20,
                               pady=8,
                               cursor='hand2',
                               command=self.generate_report)
        generate_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        # زر تصدير PDF
        pdf_btn = tk.Button(buttons_frame,
                          text="📄 تصدير PDF",
                          font=('Segoe UI', 10),
                          fg='white',
                          bg=self.colors['success'],
                          relief='flat',
                          padx=15,
                          pady=6,
                          cursor='hand2',
                          command=self.export_pdf)
        pdf_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        # زر تصدير Excel
        excel_btn = tk.Button(buttons_frame,
                            text="📊 تصدير Excel",
                            font=('Segoe UI', 10),
                            fg='white',
                            bg=self.colors['accent'],
                            relief='flat',
                            padx=15,
                            pady=6,
                            cursor='hand2',
                            command=self.export_excel)
        excel_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        # زر التحديث
        refresh_btn = tk.Button(buttons_frame,
                              text="🔄 تحديث",
                              font=('Segoe UI', 10),
                              fg=self.colors['text'],
                              bg=self.colors['background'],
                              relief='flat',
                              padx=15,
                              pady=6,
                              cursor='hand2',
                              command=self.refresh_reports)
        refresh_btn.pack(side=tk.RIGHT)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب تقارير المبيعات
        self.create_sales_tab()
        
        # تبويب تقارير المشتريات
        self.create_purchases_tab()
        
        # تبويب تقارير المخزون
        self.create_inventory_tab()
        
        # تبويب تقارير العملاء
        self.create_customers_tab()
        
        # تبويب الإحصائيات العامة
        self.create_statistics_tab()
    
    def create_sales_tab(self):
        """إنشاء تبويب تقارير المبيعات"""
        sales_frame = ttk.Frame(self.notebook)
        self.notebook.add(sales_frame, text="💰 تقارير المبيعات")
        
        # إطار المحتوى
        content_frame = tk.Frame(sales_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # قسم الإحصائيات السريعة
        stats_frame = tk.Frame(content_frame, bg=self.colors['background'])
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # بطاقات الإحصائيات
        self.create_sales_stats_cards(stats_frame)
        
        # قسم جدول المبيعات
        table_frame = tk.Frame(content_frame, bg=self.colors['card'], relief='flat', bd=1)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان الجدول
        table_header = tk.Frame(table_frame, bg=self.colors['primary'], height=50)
        table_header.pack(fill=tk.X)
        table_header.pack_propagate(False)
        
        header_label = tk.Label(table_header,
                               text="📋 تفاصيل المبيعات",
                               font=('Segoe UI', 14, 'bold'),
                               fg='white',
                               bg=self.colors['primary'])
        header_label.pack(side=tk.RIGHT, padx=20, pady=12)
        
        # إطار الجدول
        table_container = tk.Frame(table_frame, bg=self.colors['card'])
        table_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # إنشاء الجدول
        self.create_sales_table(table_container)
    
    def create_sales_stats_cards(self, parent):
        """إنشاء بطاقات إحصائيات المبيعات"""
        # الصف الأول من البطاقات
        row1 = tk.Frame(parent, bg=self.colors['background'])
        row1.pack(fill=tk.X, pady=(0, 10))
        
        # بطاقة إجمالي المبيعات
        total_sales_card = self.create_stat_card(row1, "💰 إجمالي المبيعات", "0.00 ج.م", self.colors['success'])
        total_sales_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # بطاقة عدد الفواتير
        invoices_card = self.create_stat_card(row1, "📄 عدد الفواتير", "0", self.colors['primary'])
        invoices_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # بطاقة متوسط قيمة الفاتورة
        avg_card = self.create_stat_card(row1, "📊 متوسط الفاتورة", "0.00 ج.م", self.colors['accent'])
        avg_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # بطاقة أفضل منتج
        best_product_card = self.create_stat_card(row1, "🏆 أفضل منتج", "غير محدد", self.colors['warning'])
        best_product_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = tk.Frame(parent, bg=color, relief='flat', bd=0)
        
        # المحتوى
        content_frame = tk.Frame(card, bg=color)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # العنوان
        title_label = tk.Label(content_frame,
                              text=title,
                              font=('Segoe UI', 11, 'bold'),
                              fg='white',
                              bg=color)
        title_label.pack(anchor='w')
        
        # القيمة
        value_label = tk.Label(content_frame,
                              text=value,
                              font=('Segoe UI', 16, 'bold'),
                              fg='white',
                              bg=color)
        value_label.pack(anchor='w', pady=(5, 0))
        
        return card
    
    def create_sales_table(self, parent):
        """إنشاء جدول المبيعات"""
        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(parent, bg=self.colors['card'])
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ('invoice_id', 'date', 'customer', 'total', 'payment_method', 'status')
        self.sales_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        self.sales_tree.heading('invoice_id', text='🔢 رقم الفاتورة', anchor='center')
        self.sales_tree.heading('date', text='📅 التاريخ', anchor='center')
        self.sales_tree.heading('customer', text='👤 العميل', anchor='center')
        self.sales_tree.heading('total', text='💰 المبلغ', anchor='center')
        self.sales_tree.heading('payment_method', text='💳 طريقة الدفع', anchor='center')
        self.sales_tree.heading('status', text='📊 الحالة', anchor='center')
        
        # تعيين عرض الأعمدة
        self.sales_tree.column('invoice_id', width=100, anchor='center')
        self.sales_tree.column('date', width=120, anchor='center')
        self.sales_tree.column('customer', width=200, anchor='w')
        self.sales_tree.column('total', width=120, anchor='center')
        self.sales_tree.column('payment_method', width=120, anchor='center')
        self.sales_tree.column('status', width=100, anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.sales_tree.xview)
        self.sales_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # تحميل بيانات تجريبية
        self.load_sample_sales_data()
    
    def create_purchases_tab(self):
        """إنشاء تبويب تقارير المشتريات"""
        purchases_frame = ttk.Frame(self.notebook)
        self.notebook.add(purchases_frame, text="🛒 تقارير المشتريات")
        
        # إطار المحتوى
        content_frame = tk.Frame(purchases_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # قسم الإحصائيات السريعة
        stats_frame = tk.Frame(content_frame, bg=self.colors['background'])
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # بطاقات الإحصائيات
        self.create_purchases_stats_cards(stats_frame)
        
        # قسم جدول المشتريات
        table_frame = tk.Frame(content_frame, bg=self.colors['card'], relief='flat', bd=1)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان الجدول
        table_header = tk.Frame(table_frame, bg=self.colors['secondary'], height=50)
        table_header.pack(fill=tk.X)
        table_header.pack_propagate(False)
        
        header_label = tk.Label(table_header,
                               text="🛒 تفاصيل المشتريات",
                               font=('Segoe UI', 14, 'bold'),
                               fg='white',
                               bg=self.colors['secondary'])
        header_label.pack(side=tk.RIGHT, padx=20, pady=12)
        
        # إطار الجدول
        table_container = tk.Frame(table_frame, bg=self.colors['card'])
        table_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # إنشاء الجدول
        self.create_purchases_table(table_container)
    
    def create_purchases_stats_cards(self, parent):
        """إنشاء بطاقات إحصائيات المشتريات"""
        # إجمالي المشتريات
        self.create_stat_card(parent, "💰 إجمالي المشتريات", "125,000 ر.س", self.colors['secondary'])
        
        # عدد الفواتير
        self.create_stat_card(parent, "📄 عدد الفواتير", "45", self.colors['info'])
        
        # متوسط قيمة الفاتورة
        self.create_stat_card(parent, "📊 متوسط الفاتورة", "2,778 ر.س", self.colors['warning'])
        
        # أكبر مورد
        self.create_stat_card(parent, "🏢 أكبر مورد", "شركة الأمل", self.colors['primary'])
    
    def create_purchases_table(self, parent):
        """إنشاء جدول المشتريات"""
        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(parent, bg=self.colors['card'])
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ('date', 'supplier', 'invoice_no', 'amount', 'items', 'status')
        self.purchases_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        # تعريف العناوين
        headings = {
            'date': 'التاريخ',
            'supplier': 'المورد',
            'invoice_no': 'رقم الفاتورة',
            'amount': 'المبلغ',
            'items': 'عدد الأصناف',
            'status': 'الحالة'
        }
        
        # إعداد العناوين والأعمدة
        for col in columns:
            self.purchases_tree.heading(col, text=headings[col], anchor='center')
            if col == 'date':
                self.purchases_tree.column(col, width=100, anchor='center')
            elif col == 'supplier':
                self.purchases_tree.column(col, width=150, anchor='e')
            elif col == 'invoice_no':
                self.purchases_tree.column(col, width=100, anchor='center')
            elif col == 'amount':
                self.purchases_tree.column(col, width=120, anchor='center')
            elif col == 'items':
                self.purchases_tree.column(col, width=80, anchor='center')
            else:
                self.purchases_tree.column(col, width=100, anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.purchases_tree.yview)
        self.purchases_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.purchases_tree.xview)
        self.purchases_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.purchases_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # تحميل بيانات المشتريات
        self.load_sample_purchases_data()
    
    def load_sample_purchases_data(self):
        """تحميل بيانات المشتريات التجريبية"""
        sample_data = [
            ('2024-01-15', 'شركة الأمل للتجارة', 'PUR-001', '15,500 ر.س', '25', 'مكتملة'),
            ('2024-01-14', 'مؤسسة النور', 'PUR-002', '8,750 ر.س', '18', 'مكتملة'),
            ('2024-01-13', 'شركة الفجر', 'PUR-003', '22,300 ر.س', '32', 'معلقة'),
            ('2024-01-12', 'تجارة الخليج', 'PUR-004', '12,100 ر.س', '20', 'مكتملة'),
            ('2024-01-11', 'شركة الأمل للتجارة', 'PUR-005', '18,900 ر.س', '28', 'مكتملة'),
            ('2024-01-10', 'مؤسسة الرياض', 'PUR-006', '9,650 ر.س', '15', 'مكتملة'),
            ('2024-01-09', 'شركة النجاح', 'PUR-007', '14,200 ر.س', '22', 'مكتملة'),
            ('2024-01-08', 'تجارة المستقبل', 'PUR-008', '11,800 ر.س', '19', 'مكتملة')
        ]
        
        # مسح البيانات الحالية
        for item in self.purchases_tree.get_children():
            self.purchases_tree.delete(item)
        
        # إضافة البيانات الجديدة
        for data in sample_data:
            self.purchases_tree.insert('', tk.END, values=data)
    
    def create_inventory_tab(self):
        """إنشاء تبويب تقارير المخزون"""
        inventory_frame = ttk.Frame(self.notebook)
        self.notebook.add(inventory_frame, text="📦 تقارير المخزون")
        
        # إطار المحتوى
        content_frame = tk.Frame(inventory_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # قسم الإحصائيات السريعة
        stats_frame = tk.Frame(content_frame, bg=self.colors['background'])
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # بطاقات الإحصائيات
        self.create_inventory_stats_cards(stats_frame)
        
        # قسم جدول المخزون
        table_frame = tk.Frame(content_frame, bg=self.colors['card'], relief='flat', bd=1)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان الجدول
        table_header = tk.Frame(table_frame, bg=self.colors['warning'], height=50)
        table_header.pack(fill=tk.X)
        table_header.pack_propagate(False)
        
        header_label = tk.Label(table_header,
                               text="📦 تقرير المخزون",
                               font=('Segoe UI', 14, 'bold'),
                               fg='white',
                               bg=self.colors['warning'])
        header_label.pack(side=tk.RIGHT, padx=20, pady=12)
        
        # إطار الجدول
        table_container = tk.Frame(table_frame, bg=self.colors['card'])
        table_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # إنشاء الجدول
        self.create_inventory_table(table_container)
    
    def create_inventory_stats_cards(self, parent):
        """إنشاء بطاقات إحصائيات المخزون"""
        # إجمالي الأصناف
        self.create_stat_card(parent, "📦 إجمالي الأصناف", "1,250", self.colors['info'])
        
        # قيمة المخزون
        self.create_stat_card(parent, "💰 قيمة المخزون", "485,000 ر.س", self.colors['success'])
        
        # أصناف منخفضة المخزون
        self.create_stat_card(parent, "⚠️ مخزون منخفض", "23", self.colors['warning'])
        
        # أصناف نفدت
        self.create_stat_card(parent, "🚫 نفد المخزون", "5", self.colors['danger'])
    
    def create_inventory_table(self, parent):
        """إنشاء جدول المخزون"""
        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(parent, bg=self.colors['card'])
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ('product', 'category', 'current_stock', 'min_stock', 'value', 'status')
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        # تعريف العناوين
        headings = {
            'product': 'المنتج',
            'category': 'الفئة',
            'current_stock': 'المخزون الحالي',
            'min_stock': 'الحد الأدنى',
            'value': 'القيمة',
            'status': 'الحالة'
        }
        
        # إعداد العناوين والأعمدة
        for col in columns:
            self.inventory_tree.heading(col, text=headings[col], anchor='center')
            if col == 'product':
                self.inventory_tree.column(col, width=200, anchor='e')
            elif col == 'category':
                self.inventory_tree.column(col, width=120, anchor='center')
            elif col in ['current_stock', 'min_stock']:
                self.inventory_tree.column(col, width=100, anchor='center')
            elif col == 'value':
                self.inventory_tree.column(col, width=120, anchor='center')
            else:
                self.inventory_tree.column(col, width=100, anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.inventory_tree.xview)
        self.inventory_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.inventory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # تحميل بيانات المخزون
        self.load_sample_inventory_data()
    
    def load_sample_inventory_data(self):
        """تحميل بيانات المخزون التجريبية"""
        sample_data = [
            ('لابتوب ديل XPS 13', 'إلكترونيات', '15', '10', '45,000 ر.س', 'متوفر'),
            ('ماوس لوجيتك', 'إكسسوارات', '5', '20', '750 ر.س', 'مخزون منخفض'),
            ('كيبورد ميكانيكي', 'إكسسوارات', '25', '15', '3,750 ر.س', 'متوفر'),
            ('شاشة سامسونج 27"', 'إلكترونيات', '8', '5', '12,000 ر.س', 'متوفر'),
            ('سماعات سوني', 'إلكترونيات', '0', '10', '0 ر.س', 'نفد المخزون'),
            ('طابعة HP LaserJet', 'مكتبية', '12', '8', '18,000 ر.س', 'متوفر'),
            ('كاميرا كانون', 'إلكترونيات', '3', '5', '9,000 ر.س', 'مخزون منخفض'),
            ('هارد ديسك خارجي', 'تخزين', '20', '15', '6,000 ر.س', 'متوفر'),
            ('راوتر TP-Link', 'شبكات', '7', '10', '2,100 ر.س', 'مخزون منخفض'),
            ('تابلت آيباد', 'إلكترونيات', '18', '12', '54,000 ر.س', 'متوفر')
        ]
        
        # مسح البيانات الحالية
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        # إضافة البيانات الجديدة مع تلوين الصفوف حسب الحالة
        for i, data in enumerate(sample_data):
            item_id = self.inventory_tree.insert('', tk.END, values=data)
            
            # تلوين الصفوف حسب حالة المخزون
            status = data[5]
            if status == 'نفد المخزون':
                self.inventory_tree.set(item_id, 'status', '🚫 نفد المخزون')
            elif status == 'مخزون منخفض':
                self.inventory_tree.set(item_id, 'status', '⚠️ مخزون منخفض')
            else:
                self.inventory_tree.set(item_id, 'status', '✅ متوفر')
    
    def create_customers_tab(self):
        """إنشاء تبويب تقارير العملاء"""
        customers_frame = ttk.Frame(self.notebook)
        self.notebook.add(customers_frame, text="👥 تقارير العملاء")
        
        # إطار المحتوى
        content_frame = tk.Frame(customers_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # قسم الإحصائيات السريعة
        stats_frame = tk.Frame(content_frame, bg=self.colors['background'])
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # بطاقات الإحصائيات
        self.create_customers_stats_cards(stats_frame)
        
        # قسم جدول العملاء
        table_frame = tk.Frame(content_frame, bg=self.colors['card'], relief='flat', bd=1)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان الجدول
        table_header = tk.Frame(table_frame, bg=self.colors['info'], height=50)
        table_header.pack(fill=tk.X)
        table_header.pack_propagate(False)
        
        header_label = tk.Label(table_header,
                               text="👥 تقرير العملاء",
                               font=('Segoe UI', 14, 'bold'),
                               fg='white',
                               bg=self.colors['info'])
        header_label.pack(side=tk.RIGHT, padx=20, pady=12)
        
        # إطار الجدول
        table_container = tk.Frame(table_frame, bg=self.colors['card'])
        table_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # إنشاء الجدول
        self.create_customers_table(table_container)
    
    def create_customers_stats_cards(self, parent):
        """إنشاء بطاقات إحصائيات العملاء"""
        # إجمالي العملاء
        self.create_stat_card(parent, "👥 إجمالي العملاء", "342", self.colors['info'])
        
        # عملاء نشطون
        self.create_stat_card(parent, "✅ عملاء نشطون", "298", self.colors['success'])
        
        # إجمالي المديونية
        self.create_stat_card(parent, "💳 إجمالي المديونية", "85,500 ر.س", self.colors['warning'])
        
        # أفضل عميل
        self.create_stat_card(parent, "⭐ أفضل عميل", "أحمد محمد", self.colors['primary'])
    
    def create_customers_table(self, parent):
        """إنشاء جدول العملاء"""
        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(parent, bg=self.colors['card'])
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ('name', 'phone', 'total_purchases', 'debt', 'last_purchase', 'status')
        self.customers_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        # تعريف العناوين
        headings = {
            'name': 'اسم العميل',
            'phone': 'رقم الهاتف',
            'total_purchases': 'إجمالي المشتريات',
            'debt': 'المديونية',
            'last_purchase': 'آخر شراء',
            'status': 'الحالة'
        }
        
        # إعداد العناوين والأعمدة
        for col in columns:
            self.customers_tree.heading(col, text=headings[col], anchor='center')
            if col == 'name':
                self.customers_tree.column(col, width=150, anchor='e')
            elif col == 'phone':
                self.customers_tree.column(col, width=120, anchor='center')
            elif col in ['total_purchases', 'debt']:
                self.customers_tree.column(col, width=130, anchor='center')
            elif col == 'last_purchase':
                self.customers_tree.column(col, width=100, anchor='center')
            else:
                self.customers_tree.column(col, width=100, anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.customers_tree.xview)
        self.customers_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # تحميل بيانات العملاء
        self.load_sample_customers_data()
    
    def load_sample_customers_data(self):
        """تحميل بيانات العملاء التجريبية"""
        sample_data = [
            ('أحمد محمد علي', '0501234567', '45,500 ر.س', '2,500 ر.س', '2024-01-15', 'نشط'),
            ('فاطمة أحمد', '0509876543', '32,100 ر.س', '0 ر.س', '2024-01-14', 'نشط'),
            ('محمد عبدالله', '0512345678', '28,750 ر.س', '1,200 ر.س', '2024-01-13', 'نشط'),
            ('نورا سالم', '0556789012', '18,900 ر.س', '0 ر.س', '2024-01-12', 'نشط'),
            ('خالد الأحمد', '0534567890', '15,600 ر.س', '3,800 ر.س', '2024-01-10', 'نشط'),
            ('سارة محمد', '0567890123', '22,300 ر.س', '0 ر.س', '2024-01-09', 'نشط'),
            ('عبدالرحمن علي', '0578901234', '8,750 ر.س', '750 ر.س', '2023-12-28', 'غير نشط'),
            ('مريم أحمد', '0589012345', '35,400 ر.س', '4,200 ر.س', '2024-01-08', 'نشط'),
            ('يوسف سالم', '0590123456', '12,100 ر.س', '0 ر.س', '2024-01-07', 'نشط'),
            ('هند عبدالله', '0501112233', '19,850 ر.س', '1,850 ر.س', '2024-01-06', 'نشط')
        ]
        
        # مسح البيانات الحالية
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)
        
        # إضافة البيانات الجديدة مع تلوين الصفوف حسب الحالة
        for i, data in enumerate(sample_data):
            item_id = self.customers_tree.insert('', tk.END, values=data)
            
            # تلوين الصفوف حسب حالة العميل
            status = data[5]
            debt = data[3]
            if status == 'غير نشط':
                self.customers_tree.set(item_id, 'status', '⚪ غير نشط')
            elif debt != '0 ر.س':
                self.customers_tree.set(item_id, 'status', '💳 مديون')
            else:
                self.customers_tree.set(item_id, 'status', '✅ نشط')
    
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات العامة"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="📊 إحصائيات عامة")
        
        # إطار المحتوى
        content_frame = tk.Frame(stats_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الصف الأول - مؤشرات الأداء الرئيسية
        kpi_frame = tk.Frame(content_frame, bg=self.colors['background'])
        kpi_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.create_kpi_cards(kpi_frame)
        
        # الصف الثاني - الرسوم البيانية والتحليلات
        charts_frame = tk.Frame(content_frame, bg=self.colors['background'])
        charts_frame.pack(fill=tk.BOTH, expand=True)
        
        # قسم الرسم البياني (يسار)
        chart_container = tk.Frame(charts_frame, bg=self.colors['card'], relief='flat', bd=1)
        chart_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        self.create_chart_section(chart_container)
        
        # قسم التحليلات السريعة (يمين)
        analysis_container = tk.Frame(charts_frame, bg=self.colors['card'], relief='flat', bd=1)
        analysis_container.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        self.create_analysis_section(analysis_container)
    
    def create_kpi_cards(self, parent):
        """إنشاء بطاقات مؤشرات الأداء الرئيسية"""
        # الصف الأول
        row1 = tk.Frame(parent, bg=self.colors['background'])
        row1.pack(fill=tk.X, pady=(0, 10))
        
        # إجمالي المبيعات
        sales_kpi = self.create_stat_card(row1, "💰 إجمالي المبيعات", "12,450.75 ج.م", self.colors['success'])
        sales_kpi.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عدد العملاء
        customers_kpi = self.create_stat_card(row1, "👥 إجمالي العملاء", "156", self.colors['primary'])
        customers_kpi.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عدد المنتجات
        products_kpi = self.create_stat_card(row1, "📦 إجمالي المنتجات", "89", self.colors['accent'])
        products_kpi.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # معدل النمو
        growth_kpi = self.create_stat_card(row1, "📈 معدل النمو", "+15.2%", self.colors['success'])
        growth_kpi.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # الصف الثاني
        row2 = tk.Frame(parent, bg=self.colors['background'])
        row2.pack(fill=tk.X)
        
        # متوسط قيمة الفاتورة
        avg_invoice_kpi = self.create_stat_card(row2, "📊 متوسط الفاتورة", "1,556.34 ج.م", self.colors['info'])
        avg_invoice_kpi.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عدد الفواتير
        invoices_kpi = self.create_stat_card(row2, "📄 عدد الفواتير", "8", self.colors['warning'])
        invoices_kpi.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # المنتجات منخفضة المخزون
        low_stock_kpi = self.create_stat_card(row2, "⚠️ تنبيهات المخزون", "3", self.colors['danger'])
        low_stock_kpi.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # أفضل عميل
        best_customer_kpi = self.create_stat_card(row2, "🏆 أفضل عميل", "أحمد محمد", self.colors['accent'])
        best_customer_kpi.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    def create_chart_section(self, parent):
        """إنشاء قسم الرسم البياني"""
        # عنوان القسم
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(header_frame,
                               text="📈 اتجاهات المبيعات",
                               font=('Segoe UI', 14, 'bold'),
                               fg='white',
                               bg=self.colors['primary'])
        header_label.pack(side=tk.RIGHT, padx=20, pady=12)
        
        # محتوى الرسم البياني
        chart_content = tk.Frame(parent, bg=self.colors['card'])
        chart_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # رسم بياني مبسط (نص)
        chart_text = tk.Text(chart_content,
                           font=('Courier New', 10),
                           bg=self.colors['background'],
                           fg=self.colors['text'],
                           relief='flat',
                           wrap=tk.NONE,
                           state='disabled')
        chart_text.pack(fill=tk.BOTH, expand=True)
        
        # إضافة رسم بياني نصي بسيط
        chart_data = """
    📊 مبيعات آخر 7 أيام:
    
    اليوم        المبيعات      الرسم البياني
    ────────────────────────────────────────────
    الاثنين      1,250 ج.م     ████████████░░░░░░░░
    الثلاثاء     1,850 ج.م     ██████████████████░░
    الأربعاء     950 ج.م      █████████░░░░░░░░░░░
    الخميس      2,100 ج.م     ████████████████████
    الجمعة      1,675 ج.م     ████████████████░░░░
    السبت       1,420 ج.م     ██████████████░░░░░░
    الأحد       1,890 ج.م     ██████████████████░░
    
    📈 الاتجاه العام: صاعد (+15.2%)
    💰 إجمالي الأسبوع: 11,135 ج.م
    📊 متوسط يومي: 1,590.71 ج.م
        """
        
        chart_text.config(state='normal')
        chart_text.insert('1.0', chart_data)
        chart_text.config(state='disabled')
    
    def create_analysis_section(self, parent):
        """إنشاء قسم التحليلات السريعة"""
        # عنوان القسم
        header_frame = tk.Frame(parent, bg=self.colors['accent'], height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(header_frame,
                               text="🔍 تحليلات سريعة",
                               font=('Segoe UI', 14, 'bold'),
                               fg='white',
                               bg=self.colors['accent'])
        header_label.pack(pady=12)
        
        # محتوى التحليلات
        analysis_content = tk.Frame(parent, bg=self.colors['card'])
        analysis_content.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # قائمة التحليلات
        analyses = [
            "📈 نمو المبيعات: +15.2% مقارنة بالشهر الماضي",
            "🏆 أفضل منتج: لابتوب ديل (25 وحدة)",
            "👤 أفضل عميل: أحمد محمد (3,250 ج.م)",
            "💳 طريقة الدفع الأكثر استخداماً: نقدي (45%)",
            "📅 أفضل يوم مبيعات: الخميس",
            "⏰ أفضل وقت للمبيعات: 2-4 مساءً",
            "📦 منتجات تحتاج إعادة تخزين: 3",
            "🎯 معدل تحويل العملاء: 68%",
            "💰 متوسط هامش الربح: 35%",
            "📊 توقعات الشهر القادم: +8%"
        ]
        
        for i, analysis in enumerate(analyses):
            analysis_frame = tk.Frame(analysis_content, bg=self.colors['card'])
            analysis_frame.pack(fill=tk.X, pady=5)
            
            analysis_label = tk.Label(analysis_frame,
                                     text=f"• {analysis}",
                                     font=('Segoe UI', 10),
                                     fg=self.colors['text'],
                                     bg=self.colors['card'],
                                     anchor='w',
                                     wraplength=250)
            analysis_label.pack(fill=tk.X, padx=10, pady=2)
            
            # خط فاصل
            if i < len(analyses) - 1:
                separator = tk.Frame(analysis_content, bg=self.colors['border'], height=1)
                separator.pack(fill=tk.X, padx=10, pady=2)
    
    # الطرق المطلوبة
    def on_period_change(self, event=None):
        """عند تغيير فترة التقرير"""
        period = self.report_period_var.get()
        today = datetime.now()
        
        if period == "اليوم":
            start_date = today
            end_date = today
        elif period == "أمس":
            yesterday = today - timedelta(days=1)
            start_date = yesterday
            end_date = yesterday
        elif period == "هذا الأسبوع":
            start_date = today - timedelta(days=today.weekday())
            end_date = today
        elif period == "الأسبوع الماضي":
            start_date = today - timedelta(days=today.weekday() + 7)
            end_date = today - timedelta(days=today.weekday() + 1)
        elif period == "هذا الشهر":
            start_date = today.replace(day=1)
            end_date = today
        elif period == "الشهر الماضي":
            if today.month == 1:
                start_date = today.replace(year=today.year-1, month=12, day=1)
                end_date = today.replace(day=1) - timedelta(days=1)
            else:
                start_date = today.replace(month=today.month-1, day=1)
                end_date = today.replace(day=1) - timedelta(days=1)
        elif period == "هذا العام":
            start_date = today.replace(month=1, day=1)
            end_date = today
        else:  # فترة مخصصة
            return
        
        self.start_date_var.set(start_date.strftime("%Y-%m-%d"))
        self.end_date_var.set(end_date.strftime("%Y-%m-%d"))
        
        # تحديث التقارير
        self.refresh_reports()
    
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            # التحقق من صحة التواريخ
            start_date = datetime.strptime(self.start_date_var.get(), "%Y-%m-%d")
            end_date = datetime.strptime(self.end_date_var.get(), "%Y-%m-%d")
            
            if start_date > end_date:
                messagebox.showerror("خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return
            
            # تحديث البيانات
            self.load_sales_data(start_date, end_date)
            messagebox.showinfo("تم", "تم إنشاء التقرير بنجاح")
            
        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
    
    def export_pdf(self):
        """تصدير PDF"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                title="حفظ التقرير كـ PDF"
            )
            if filename:
                messagebox.showinfo("معلومات", f"سيتم تصدير التقرير إلى:\n{filename}\n\nهذه الميزة قيد التطوير")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF: {str(e)}")
    
    def export_excel(self):
        """تصدير Excel"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title="حفظ التقرير كـ Excel"
            )
            if filename:
                messagebox.showinfo("معلومات", f"سيتم تصدير التقرير إلى:\n{filename}\n\nهذه الميزة قيد التطوير")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير Excel: {str(e)}")
    
    def refresh_reports(self):
        """تحديث التقارير"""
        try:
            # تحديث بيانات المبيعات
            self.load_sample_sales_data()
            messagebox.showinfo("تم", "تم تحديث التقارير بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث التقارير: {str(e)}")
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحميل بيانات المبيعات التجريبية
            self.load_sample_sales_data()
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {str(e)}")
    
    def load_sample_sales_data(self):
        """تحميل بيانات مبيعات تجريبية"""
        try:
            # مسح البيانات الحالية
            if hasattr(self, 'sales_tree'):
                for item in self.sales_tree.get_children():
                    self.sales_tree.delete(item)
                
                # بيانات تجريبية
                sample_data = [
                    ("INV-001", "2024-01-15", "أحمد محمد", "1,250.00 ج.م", "نقدي", "مكتملة"),
                    ("INV-002", "2024-01-15", "فاطمة علي", "850.50 ج.م", "بطاقة ائتمان", "مكتملة"),
                    ("INV-003", "2024-01-14", "محمد حسن", "2,100.00 ج.م", "تحويل بنكي", "مكتملة"),
                    ("INV-004", "2024-01-14", "سارة أحمد", "675.25 ج.م", "نقدي", "مكتملة"),
                    ("INV-005", "2024-01-13", "عمر خالد", "1,890.75 ج.م", "بطاقة ائتمان", "مكتملة"),
                    ("INV-006", "2024-01-13", "نور محمود", "420.00 ج.م", "نقدي", "مكتملة"),
                    ("INV-007", "2024-01-12", "يوسف إبراهيم", "3,250.00 ج.م", "تحويل بنكي", "مكتملة"),
                    ("INV-008", "2024-01-12", "مريم سالم", "1,125.50 ج.م", "بطاقة ائتمان", "مكتملة")
                ]
                
                # إضافة البيانات للجدول
                for data in sample_data:
                    self.sales_tree.insert('', 'end', values=data)
        
        except Exception as e:
            print(f"خطأ في تحميل بيانات المبيعات: {str(e)}")
    
    def load_sales_data(self, start_date, end_date):
        """تحميل بيانات المبيعات من قاعدة البيانات"""
        try:
            # هنا سيتم الاستعلام من قاعدة البيانات الفعلية
            # حالياً نستخدم البيانات التجريبية
            self.load_sample_sales_data()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المبيعات: {str(e)}")
