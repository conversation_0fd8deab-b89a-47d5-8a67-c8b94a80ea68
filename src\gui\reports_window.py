# -*- coding: utf-8 -*-
"""
نافذة التقارير
Reports Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date, timedelta
import json

from ..reports.report_generator import ReportGenerator

class ReportsWindow:
    """نافذة التقارير"""
    
    def __init__(self, parent, db_manager, config):
        """
        تهيئة نافذة التقارير
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.report_generator = ReportGenerator(db_manager)
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("التقارير")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        # متغيرات
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        today = date.today()
        month_start = today.replace(day=1)
        
        self.start_date_var = tk.StringVar(value=month_start.strftime('%Y-%m-%d'))
        self.end_date_var = tk.StringVar(value=today.strftime('%Y-%m-%d'))
        self.report_type_var = tk.StringVar(value="sales")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إطار التحكم
        self.create_control_frame(main_frame)
        
        # إطار النتائج
        self.create_results_frame(main_frame)
    
    def create_control_frame(self, parent):
        """إنشاء إطار التحكم"""
        control_frame = ttk.LabelFrame(parent, text="إعدادات التقرير", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول - نوع التقرير
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, pady=2)
        
        ttk.Label(row1, text="نوع التقرير:", width=15).pack(side=tk.LEFT)
        report_combo = ttk.Combobox(row1, textvariable=self.report_type_var, width=20)
        report_combo['values'] = (
            'sales', 'inventory', 'profit_loss', 'customers'
        )
        report_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        # الصف الثاني - التواريخ
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, pady=2)
        
        ttk.Label(row2, text="من تاريخ:", width=15).pack(side=tk.LEFT)
        ttk.Entry(row2, textvariable=self.start_date_var, width=15).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(row2, text="إلى تاريخ:", width=15).pack(side=tk.LEFT)
        ttk.Entry(row2, textvariable=self.end_date_var, width=15).pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار سريعة للتواريخ
        ttk.Button(row2, text="اليوم", command=self.set_today).pack(side=tk.LEFT, padx=2)
        ttk.Button(row2, text="هذا الأسبوع", command=self.set_this_week).pack(side=tk.LEFT, padx=2)
        ttk.Button(row2, text="هذا الشهر", command=self.set_this_month).pack(side=tk.LEFT, padx=2)
        
        # الصف الثالث - أزرار العمليات
        row3 = ttk.Frame(control_frame)
        row3.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(row3, text="إنشاء التقرير", command=self.generate_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(row3, text="تصدير إلى Excel", command=self.export_to_excel).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(row3, text="طباعة", command=self.print_report).pack(side=tk.LEFT)
    
    def create_results_frame(self, parent):
        """إنشاء إطار النتائج"""
        results_frame = ttk.LabelFrame(parent, text="نتائج التقرير", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(results_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب الملخص
        self.summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.summary_frame, text="الملخص")
        
        # تبويب التفاصيل
        self.details_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.details_frame, text="التفاصيل")
        
        # تبويب الرسوم البيانية
        self.charts_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.charts_frame, text="الرسوم البيانية")
    
    def set_today(self):
        """تعيين تاريخ اليوم"""
        today = date.today()
        self.start_date_var.set(today.strftime('%Y-%m-%d'))
        self.end_date_var.set(today.strftime('%Y-%m-%d'))
    
    def set_this_week(self):
        """تعيين هذا الأسبوع"""
        today = date.today()
        week_start = today - timedelta(days=today.weekday())
        self.start_date_var.set(week_start.strftime('%Y-%m-%d'))
        self.end_date_var.set(today.strftime('%Y-%m-%d'))
    
    def set_this_month(self):
        """تعيين هذا الشهر"""
        today = date.today()
        month_start = today.replace(day=1)
        self.start_date_var.set(month_start.strftime('%Y-%m-%d'))
        self.end_date_var.set(today.strftime('%Y-%m-%d'))
    
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            # التحقق من التواريخ
            start_date = datetime.strptime(self.start_date_var.get(), '%Y-%m-%d').date()
            end_date = datetime.strptime(self.end_date_var.get(), '%Y-%m-%d').date()
            
            if start_date > end_date:
                messagebox.showerror("خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return
            
            # مسح النتائج السابقة
            self.clear_results()
            
            # إنشاء التقرير حسب النوع
            report_type = self.report_type_var.get()
            
            if report_type == "sales":
                self.generate_sales_report(start_date, end_date)
            elif report_type == "inventory":
                self.generate_inventory_report()
            elif report_type == "profit_loss":
                self.generate_profit_loss_report(start_date, end_date)
            elif report_type == "customers":
                self.generate_customers_report()
            
        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {str(e)}")
    
    def clear_results(self):
        """مسح النتائج السابقة"""
        for widget in self.summary_frame.winfo_children():
            widget.destroy()
        for widget in self.details_frame.winfo_children():
            widget.destroy()
        for widget in self.charts_frame.winfo_children():
            widget.destroy()
    
    def generate_sales_report(self, start_date, end_date):
        """إنشاء تقرير المبيعات"""
        try:
            data = self.report_generator.sales_report(start_date, end_date)
            
            # عرض الملخص
            self.display_sales_summary(data['summary'])
            
            # عرض التفاصيل
            self.display_sales_details(data)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المبيعات: {str(e)}")
    
    def display_sales_summary(self, summary):
        """عرض ملخص المبيعات"""
        # إطار الملخص
        summary_info = ttk.LabelFrame(self.summary_frame, text="ملخص المبيعات", padding=10)
        summary_info.pack(fill=tk.X, pady=(0, 10))
        
        # البيانات
        data = [
            ("عدد الفواتير:", f"{summary['invoice_count']}"),
            ("إجمالي المبيعات:", f"{summary['total_sales']:.2f} ج.م"),
            ("المبلغ المدفوع:", f"{summary['total_paid']:.2f} ج.م"),
            ("المبلغ المتبقي:", f"{summary['total_remaining']:.2f} ج.م")
        ]
        
        for i, (label, value) in enumerate(data):
            ttk.Label(summary_info, text=label, font=('Arial', 10, 'bold')).grid(row=i, column=0, sticky=tk.W, pady=2)
            ttk.Label(summary_info, text=value, font=('Arial', 10)).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)
    
    def display_sales_details(self, data):
        """عرض تفاصيل المبيعات"""
        # جدول المبيعات اليومية
        daily_frame = ttk.LabelFrame(self.details_frame, text="المبيعات اليومية", padding=10)
        daily_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # إنشاء الجدول
        columns = ('date', 'invoices', 'amount')
        daily_tree = ttk.Treeview(daily_frame, columns=columns, show='headings', height=8)
        
        daily_tree.heading('date', text='التاريخ')
        daily_tree.heading('invoices', text='عدد الفواتير')
        daily_tree.heading('amount', text='المبلغ')
        
        daily_tree.column('date', width=100)
        daily_tree.column('invoices', width=100)
        daily_tree.column('amount', width=150)
        
        # إضافة البيانات
        for row in data['daily_sales']:
            daily_tree.insert('', tk.END, values=(
                row['invoice_date'],
                row['invoice_count'],
                f"{row['daily_total']:.2f} ج.م"
            ))
        
        daily_tree.pack(fill=tk.BOTH, expand=True)
        
        # جدول أفضل العملاء
        customers_frame = ttk.LabelFrame(self.details_frame, text="أفضل العملاء", padding=10)
        customers_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('customer', 'invoices', 'amount')
        customers_tree = ttk.Treeview(customers_frame, columns=columns, show='headings', height=8)
        
        customers_tree.heading('customer', text='العميل')
        customers_tree.heading('invoices', text='عدد الفواتير')
        customers_tree.heading('amount', text='المبلغ')
        
        customers_tree.column('customer', width=200)
        customers_tree.column('invoices', width=100)
        customers_tree.column('amount', width=150)
        
        # إضافة البيانات
        for row in data['top_customers']:
            customers_tree.insert('', tk.END, values=(
                row['customer_name'],
                row['invoice_count'],
                f"{row['total_amount']:.2f} ج.م"
            ))
        
        customers_tree.pack(fill=tk.BOTH, expand=True)
    
    def generate_inventory_report(self):
        """إنشاء تقرير المخزون"""
        try:
            data = self.report_generator.inventory_report()
            
            # عرض الملخص
            self.display_inventory_summary(data['summary'])
            
            # عرض التفاصيل
            self.display_inventory_details(data)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المخزون: {str(e)}")
    
    def display_inventory_summary(self, summary):
        """عرض ملخص المخزون"""
        summary_info = ttk.LabelFrame(self.summary_frame, text="ملخص المخزون", padding=10)
        summary_info.pack(fill=tk.X, pady=(0, 10))
        
        data = [
            ("عدد المنتجات:", f"{summary['product_count']}"),
            ("إجمالي الكمية:", f"{summary['total_quantity']:.2f}"),
            ("قيمة التكلفة:", f"{summary['total_cost_value']:.2f} ج.م"),
            ("قيمة البيع:", f"{summary['total_selling_value']:.2f} ج.م")
        ]
        
        for i, (label, value) in enumerate(data):
            ttk.Label(summary_info, text=label, font=('Arial', 10, 'bold')).grid(row=i, column=0, sticky=tk.W, pady=2)
            ttk.Label(summary_info, text=value, font=('Arial', 10)).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)
    
    def display_inventory_details(self, data):
        """عرض تفاصيل المخزون"""
        # جدول المنتجات منخفضة المخزون
        low_stock_frame = ttk.LabelFrame(self.details_frame, text="منتجات منخفضة المخزون", padding=10)
        low_stock_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        columns = ('code', 'name', 'current', 'minimum')
        low_stock_tree = ttk.Treeview(low_stock_frame, columns=columns, show='headings', height=8)
        
        low_stock_tree.heading('code', text='الكود')
        low_stock_tree.heading('name', text='اسم المنتج')
        low_stock_tree.heading('current', text='المخزون الحالي')
        low_stock_tree.heading('minimum', text='الحد الأدنى')
        
        for row in data['low_stock']:
            low_stock_tree.insert('', tk.END, values=(
                row['code'],
                row['name'],
                row['stock_quantity'],
                row['min_stock_level']
            ))
        
        low_stock_tree.pack(fill=tk.BOTH, expand=True)
    
    def generate_profit_loss_report(self, start_date, end_date):
        """إنشاء تقرير الأرباح والخسائر"""
        try:
            data = self.report_generator.profit_loss_report(start_date, end_date)
            
            # عرض الملخص
            self.display_profit_loss_summary(data)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير الأرباح والخسائر: {str(e)}")
    
    def display_profit_loss_summary(self, data):
        """عرض ملخص الأرباح والخسائر"""
        summary_info = ttk.LabelFrame(self.summary_frame, text="الأرباح والخسائر", padding=10)
        summary_info.pack(fill=tk.X, pady=(0, 10))
        
        # الإيرادات
        revenue_frame = ttk.LabelFrame(summary_info, text="الإيرادات", padding=5)
        revenue_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(revenue_frame, text=f"إجمالي المبيعات: {data['revenue']['total_sales']:.2f} ج.م").pack(anchor=tk.W)
        ttk.Label(revenue_frame, text=f"الضرائب: {data['revenue']['tax_amount']:.2f} ج.م").pack(anchor=tk.W)
        ttk.Label(revenue_frame, text=f"صافي المبيعات: {data['revenue']['net_sales']:.2f} ج.م").pack(anchor=tk.W)
        
        # المصروفات
        expenses_frame = ttk.LabelFrame(summary_info, text="المصروفات", padding=5)
        expenses_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(expenses_frame, text=f"تكلفة البضاعة المباعة: {data['expenses']['cost_of_goods_sold']:.2f} ج.م").pack(anchor=tk.W)
        ttk.Label(expenses_frame, text=f"إجمالي المشتريات: {data['expenses']['total_purchases']:.2f} ج.م").pack(anchor=tk.W)
        
        # الأرباح
        profit_frame = ttk.LabelFrame(summary_info, text="الأرباح", padding=5)
        profit_frame.pack(fill=tk.X)
        
        ttk.Label(profit_frame, text=f"الربح الإجمالي: {data['profit']['gross_profit']:.2f} ج.م", 
                 font=('Arial', 10, 'bold'), foreground='green').pack(anchor=tk.W)
        ttk.Label(profit_frame, text=f"الربح الصافي: {data['profit']['net_profit']:.2f} ج.م", 
                 font=('Arial', 10, 'bold'), foreground='darkgreen').pack(anchor=tk.W)
        ttk.Label(profit_frame, text=f"هامش الربح: {data['profit']['profit_margin']:.1f}%", 
                 font=('Arial', 10, 'bold'), foreground='blue').pack(anchor=tk.W)
    
    def generate_customers_report(self):
        """إنشاء تقرير العملاء"""
        try:
            data = self.report_generator.customer_report()
            
            # عرض الملخص
            self.display_customers_summary(data['summary'])
            
            # عرض التفاصيل
            self.display_customers_details(data)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير العملاء: {str(e)}")
    
    def display_customers_summary(self, summary):
        """عرض ملخص العملاء"""
        summary_info = ttk.LabelFrame(self.summary_frame, text="ملخص العملاء", padding=10)
        summary_info.pack(fill=tk.X, pady=(0, 10))
        
        data = [
            ("إجمالي العملاء:", f"{summary['total_customers']}"),
            ("إجمالي الأرصدة:", f"{summary['total_balance']:.2f} ج.م"),
            ("إجمالي حدود الائتمان:", f"{summary['total_credit_limit']:.2f} ج.م")
        ]
        
        for i, (label, value) in enumerate(data):
            ttk.Label(summary_info, text=label, font=('Arial', 10, 'bold')).grid(row=i, column=0, sticky=tk.W, pady=2)
            ttk.Label(summary_info, text=value, font=('Arial', 10)).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)
    
    def display_customers_details(self, data):
        """عرض تفاصيل العملاء"""
        # جدول العملاء المدينين
        debtors_frame = ttk.LabelFrame(self.details_frame, text="العملاء المدينون", padding=10)
        debtors_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('code', 'name', 'balance', 'credit_limit')
        debtors_tree = ttk.Treeview(debtors_frame, columns=columns, show='headings', height=10)
        
        debtors_tree.heading('code', text='الكود')
        debtors_tree.heading('name', text='اسم العميل')
        debtors_tree.heading('balance', text='الرصيد')
        debtors_tree.heading('credit_limit', text='حد الائتمان')
        
        for row in data['debtors']:
            debtors_tree.insert('', tk.END, values=(
                row['code'],
                row['name'],
                f"{row['current_balance']:.2f} ج.م",
                f"{row['credit_limit']:.2f} ج.م"
            ))
        
        debtors_tree.pack(fill=tk.BOTH, expand=True)
    
    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        messagebox.showinfo("معلومات", "سيتم إضافة وظيفة التصدير قريباً")
    
    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("معلومات", "سيتم إضافة وظيفة الطباعة قريباً")
