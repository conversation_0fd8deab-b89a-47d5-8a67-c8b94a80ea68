# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager
"""

import sqlite3
import os
import hashlib
from datetime import datetime
from typing import Optional, List, Dict, Any

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path: str = "data/almezan.db"):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.connection = None
        
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # الاتصال بقاعدة البيانات
        self.connect()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            print(f"تم الاتصال بقاعدة البيانات: {self.db_path}")
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            raise
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            print("تم إغلاق الاتصال بقاعدة البيانات")
    
    def execute_query(self, query: str, params: tuple = ()) -> sqlite3.Cursor:
        """
        تنفيذ استعلام SQL
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            cursor: مؤشر النتائج
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return cursor
        except Exception as e:
            self.connection.rollback()
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    def fetch_one(self, query: str, params: tuple = ()) -> Optional[Dict]:
        """
        جلب سجل واحد
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            dict: السجل أو None
        """
        cursor = self.execute_query(query, params)
        row = cursor.fetchone()
        return dict(row) if row else None
    
    def fetch_all(self, query: str, params: tuple = ()) -> List[Dict]:
        """
        جلب جميع السجلات
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            list: قائمة السجلات
        """
        cursor = self.execute_query(query, params)
        rows = cursor.fetchall()
        return [dict(row) for row in rows]
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        users_table = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'cashier',
            email TEXT,
            phone TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            permissions TEXT DEFAULT '[]'
        )
        """
        
        # جدول العملات
        currencies_table = """
        CREATE TABLE IF NOT EXISTS currencies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            exchange_rate REAL DEFAULT 1.0,
            is_default BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول فئات المنتجات
        categories_table = """
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT,
            parent_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories (id)
        )
        """
        
        # جدول المنتجات
        products_table = """
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            category_id INTEGER,
            unit TEXT DEFAULT 'قطعة',
            cost_price REAL DEFAULT 0,
            selling_price REAL NOT NULL,
            stock_quantity INTEGER DEFAULT 0,
            min_stock_level INTEGER DEFAULT 0,
            max_stock_level INTEGER DEFAULT 1000,
            barcode TEXT,
            image_path TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
        """
        
        # جدول العملاء
        customers_table = """
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            credit_limit REAL DEFAULT 0,
            current_balance REAL DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول الموردين
        suppliers_table = """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            current_balance REAL DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول الفواتير
        invoices_table = """
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT UNIQUE NOT NULL,
            invoice_type TEXT NOT NULL, -- 'sale' or 'purchase'
            customer_id INTEGER,
            supplier_id INTEGER,
            user_id INTEGER NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            subtotal REAL NOT NULL DEFAULT 0,
            discount_amount REAL DEFAULT 0,
            discount_percentage REAL DEFAULT 0,
            tax_amount REAL DEFAULT 0,
            tax_percentage REAL DEFAULT 0,
            total_amount REAL NOT NULL DEFAULT 0,
            paid_amount REAL DEFAULT 0,
            remaining_amount REAL DEFAULT 0,
            payment_status TEXT DEFAULT 'pending', -- 'pending', 'partial', 'paid'
            payment_method TEXT DEFAULT 'cash', -- 'cash', 'credit', 'bank'
            notes TEXT,
            is_cancelled BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # جدول تفاصيل الفواتير
        invoice_items_table = """
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            discount_amount REAL DEFAULT 0,
            total_price REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        """

        # جدول المدفوعات
        payments_table = """
        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER,
            customer_id INTEGER,
            supplier_id INTEGER,
            payment_type TEXT NOT NULL, -- 'receive', 'pay'
            amount REAL NOT NULL,
            payment_method TEXT DEFAULT 'cash',
            payment_date DATE NOT NULL,
            reference_number TEXT,
            notes TEXT,
            user_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id),
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # جدول حركة المخزون
        stock_movements_table = """
        CREATE TABLE IF NOT EXISTS stock_movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
            quantity REAL NOT NULL,
            unit_cost REAL,
            reference_type TEXT, -- 'invoice', 'adjustment', 'initial'
            reference_id INTEGER,
            notes TEXT,
            user_id INTEGER NOT NULL,
            movement_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # جدول إعدادات النظام
        system_settings_table = """
        CREATE TABLE IF NOT EXISTS system_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type TEXT DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
            description TEXT,
            category TEXT DEFAULT 'general',
            is_system BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول سجل العمليات (Audit Log)
        audit_log_table = """
        CREATE TABLE IF NOT EXISTS audit_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            table_name TEXT,
            record_id INTEGER,
            old_values TEXT, -- JSON
            new_values TEXT, -- JSON
            ip_address TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # جدول جلسات المستخدمين
        user_sessions_table = """
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            session_token TEXT UNIQUE NOT NULL,
            ip_address TEXT,
            user_agent TEXT,
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            logout_time TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # جدول النسخ الاحتياطية
        backups_table = """
        CREATE TABLE IF NOT EXISTS backups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            backup_name TEXT NOT NULL,
            backup_path TEXT NOT NULL,
            backup_size INTEGER,
            backup_type TEXT DEFAULT 'manual', -- 'manual', 'automatic'
            status TEXT DEFAULT 'completed', -- 'in_progress', 'completed', 'failed'
            user_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # تنفيذ إنشاء الجداول
        tables = [
            users_table, currencies_table, categories_table, products_table,
            customers_table, suppliers_table, invoices_table, invoice_items_table,
            payments_table, stock_movements_table, system_settings_table,
            audit_log_table, user_sessions_table, backups_table
        ]

        for table in tables:
            self.execute_query(table)

        # إنشاء الفهارس لتحسين الأداء
        self._create_indexes()

        # إدراج البيانات الافتراضية
        self._insert_default_data()

        print("تم إنشاء جداول قاعدة البيانات بنجاح")

    def _create_indexes(self):
        """إنشاء الفهارس لتحسين الأداء"""
        indexes = [
            # فهارس جدول المستخدمين
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",

            # فهارس جدول المنتجات
            "CREATE INDEX IF NOT EXISTS idx_products_code ON products(code)",
            "CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",

            # فهارس جدول العملاء
            "CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(code)",
            "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)",
            "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)",

            # فهارس جدول الموردين
            "CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(code)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_phone ON suppliers(phone)",

            # فهارس جدول الفواتير
            "CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_type ON invoices(invoice_type)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(invoice_date)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_supplier ON invoices(supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_user ON invoices(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(payment_status)",

            # فهارس جدول تفاصيل الفواتير
            "CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice ON invoice_items(invoice_id)",
            "CREATE INDEX IF NOT EXISTS idx_invoice_items_product ON invoice_items(product_id)",

            # فهارس جدول المدفوعات
            "CREATE INDEX IF NOT EXISTS idx_payments_invoice ON payments(invoice_id)",
            "CREATE INDEX IF NOT EXISTS idx_payments_customer ON payments(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_payments_supplier ON payments(supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date)",
            "CREATE INDEX IF NOT EXISTS idx_payments_type ON payments(payment_type)",

            # فهارس جدول حركة المخزون
            "CREATE INDEX IF NOT EXISTS idx_stock_movements_product ON stock_movements(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON stock_movements(movement_type)",
            "CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON stock_movements(movement_date)",
            "CREATE INDEX IF NOT EXISTS idx_stock_movements_reference ON stock_movements(reference_type, reference_id)",

            # فهارس جدول سجل العمليات
            "CREATE INDEX IF NOT EXISTS idx_audit_log_user ON audit_log(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action)",
            "CREATE INDEX IF NOT EXISTS idx_audit_log_table ON audit_log(table_name)",
            "CREATE INDEX IF NOT EXISTS idx_audit_log_date ON audit_log(created_at)",

            # فهارس جدول الجلسات
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)",
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active)",

            # فهارس جدول الإعدادات
            "CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key)",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category)"
        ]

        for index in indexes:
            try:
                self.execute_query(index)
            except Exception as e:
                print(f"تحذير: فشل في إنشاء فهرس: {e}")

    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        
        # إدراج المستخدم الافتراضي (المدير) - تم تعطيله لتجنب التضارب
        # سيتم إنشاء المستخدم الافتراضي من خلال UserManager
        pass
        
        # إدراج العملة الافتراضية (الجنيه المصري)
        egp_exists = self.fetch_one("SELECT id FROM currencies WHERE code = ?", ("EGP",))
        if not egp_exists:
            self.execute_query("""
                INSERT INTO currencies (code, name, symbol, exchange_rate, is_default)
                VALUES (?, ?, ?, ?, ?)
            """, ("EGP", "الجنيه المصري", "ج.م", 1.0, 1))
        
        # إدراج فئات افتراضية
        default_categories = [
            ("عام", "فئة عامة للمنتجات"),
            ("إلكترونيات", "الأجهزة الإلكترونية"),
            ("ملابس", "الملابس والأزياء"),
            ("أغذية", "المواد الغذائية"),
            ("مستلزمات منزلية", "أدوات ومستلزمات المنزل")
        ]
        
        for name, description in default_categories:
            exists = self.fetch_one("SELECT id FROM categories WHERE name = ?", (name,))
            if not exists:
                self.execute_query("""
                    INSERT INTO categories (name, description)
                    VALUES (?, ?)
                """, (name, description))

        # إدراج إعدادات النظام الافتراضية
        default_settings = [
            # إعدادات الشركة
            ("company_name", "الميزان للتجارة", "string", "اسم الشركة", "company"),
            ("company_address", "القاهرة، مصر", "string", "عنوان الشركة", "company"),
            ("company_phone", "0*********0", "string", "هاتف الشركة", "company"),
            ("company_email", "<EMAIL>", "string", "بريد الشركة الإلكتروني", "company"),
            ("company_tax_number", "*********", "string", "الرقم الضريبي للشركة", "company"),

            # إعدادات العملة
            ("default_currency", "EGP", "string", "العملة الافتراضية", "currency"),
            ("currency_decimal_places", "2", "number", "عدد الخانات العشرية للعملة", "currency"),

            # إعدادات الفواتير
            ("invoice_prefix_sale", "INV-", "string", "بادئة رقم فاتورة البيع", "invoice"),
            ("invoice_prefix_purchase", "PUR-", "string", "بادئة رقم فاتورة الشراء", "invoice"),
            ("auto_generate_invoice_number", "true", "boolean", "توليد رقم الفاتورة تلقائياً", "invoice"),
            ("default_tax_rate", "14", "number", "معدل الضريبة الافتراضي (%)", "invoice"),
            ("print_invoice_after_save", "true", "boolean", "طباعة الفاتورة بعد الحفظ", "invoice"),

            # إعدادات المخزون
            ("low_stock_warning", "true", "boolean", "تحذير عند انخفاض المخزون", "inventory"),
            ("auto_update_stock", "true", "boolean", "تحديث المخزون تلقائياً", "inventory"),
            ("allow_negative_stock", "false", "boolean", "السماح بالمخزون السالب", "inventory"),

            # إعدادات النظام
            ("system_language", "ar", "string", "لغة النظام", "system"),
            ("date_format", "yyyy-mm-dd", "string", "تنسيق التاريخ", "system"),
            ("session_timeout", "60", "number", "انتهاء الجلسة (دقيقة)", "system"),
            ("enable_audit_log", "true", "boolean", "تفعيل سجل العمليات", "system"),

            # إعدادات النسخ الاحتياطي
            ("auto_backup_enabled", "true", "boolean", "تفعيل النسخ الاحتياطي التلقائي", "backup"),
            ("backup_frequency", "daily", "string", "تكرار النسخ الاحتياطي", "backup"),
            ("backup_retention_days", "30", "number", "مدة الاحتفاظ بالنسخ الاحتياطية (يوم)", "backup"),
            ("backup_location", "./backups", "string", "مجلد النسخ الاحتياطية", "backup")
        ]

        for key, value, type_, description, category in default_settings:
            exists = self.fetch_one("SELECT id FROM system_settings WHERE setting_key = ?", (key,))
            if not exists:
                self.execute_query("""
                    INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category)
                    VALUES (?, ?, ?, ?, ?)
                """, (key, value, type_, description, category))
    
    def _hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """التحقق من كلمة المرور"""
        return self._hash_password(password) == hashed

    # طرق إدارة الإعدادات
    def get_setting(self, key: str, default_value=None):
        """الحصول على قيمة إعداد"""
        result = self.fetch_one("SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?", (key,))
        if result:
            value = result['setting_value']
            setting_type = result['setting_type']

            # تحويل القيمة حسب النوع
            if setting_type == 'boolean':
                return value.lower() in ('true', '1', 'yes')
            elif setting_type == 'number':
                try:
                    return float(value) if '.' in value else int(value)
                except ValueError:
                    return default_value
            elif setting_type == 'json':
                try:
                    import json
                    return json.loads(value)
                except (json.JSONDecodeError, ValueError):
                    return default_value
            else:
                return value
        return default_value

    def set_setting(self, key: str, value, setting_type: str = 'string', description: str = '', category: str = 'general'):
        """تعيين قيمة إعداد"""
        # تحويل القيمة إلى نص
        if setting_type == 'boolean':
            str_value = 'true' if value else 'false'
        elif setting_type == 'json':
            import json
            str_value = json.dumps(value, ensure_ascii=False)
        else:
            str_value = str(value)

        # التحقق من وجود الإعداد
        exists = self.fetch_one("SELECT id FROM system_settings WHERE setting_key = ?", (key,))

        if exists:
            # تحديث الإعداد الموجود
            self.execute_query("""
                UPDATE system_settings
                SET setting_value = ?, setting_type = ?, description = ?, category = ?, updated_at = CURRENT_TIMESTAMP
                WHERE setting_key = ?
            """, (str_value, setting_type, description, category, key))
        else:
            # إنشاء إعداد جديد
            self.execute_query("""
                INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category)
                VALUES (?, ?, ?, ?, ?)
            """, (key, str_value, setting_type, description, category))

    def get_settings_by_category(self, category: str):
        """الحصول على جميع الإعدادات في فئة معينة"""
        return self.fetch_all("""
            SELECT setting_key, setting_value, setting_type, description
            FROM system_settings
            WHERE category = ?
            ORDER BY setting_key
        """, (category,))

    # طرق سجل العمليات
    def log_action(self, user_id: int, action: str, table_name: str = None, record_id: int = None,
                   old_values: dict = None, new_values: dict = None, ip_address: str = None, user_agent: str = None):
        """تسجيل عملية في سجل العمليات"""
        import json

        old_values_json = json.dumps(old_values, ensure_ascii=False) if old_values else None
        new_values_json = json.dumps(new_values, ensure_ascii=False) if new_values else None

        self.execute_query("""
            INSERT INTO audit_log (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (user_id, action, table_name, record_id, old_values_json, new_values_json, ip_address, user_agent))

    def get_audit_log(self, limit: int = 100, user_id: int = None, action: str = None, table_name: str = None):
        """الحصول على سجل العمليات"""
        query = """
            SELECT al.*, u.username, u.full_name
            FROM audit_log al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE 1=1
        """
        params = []

        if user_id:
            query += " AND al.user_id = ?"
            params.append(user_id)

        if action:
            query += " AND al.action = ?"
            params.append(action)

        if table_name:
            query += " AND al.table_name = ?"
            params.append(table_name)

        query += " ORDER BY al.created_at DESC LIMIT ?"
        params.append(limit)

        return self.fetch_all(query, params)

    # طرق إدارة الجلسات
    def create_user_session(self, user_id: int, session_token: str, ip_address: str = None, user_agent: str = None):
        """إنشاء جلسة مستخدم جديدة"""
        self.execute_query("""
            INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent)
            VALUES (?, ?, ?, ?)
        """, (user_id, session_token, ip_address, user_agent))

    def update_session_activity(self, session_token: str):
        """تحديث آخر نشاط للجلسة"""
        self.execute_query("""
            UPDATE user_sessions
            SET last_activity = CURRENT_TIMESTAMP
            WHERE session_token = ? AND is_active = 1
        """, (session_token,))

    def end_user_session(self, session_token: str):
        """إنهاء جلسة المستخدم"""
        self.execute_query("""
            UPDATE user_sessions
            SET is_active = 0, logout_time = CURRENT_TIMESTAMP
            WHERE session_token = ?
        """, (session_token,))

    def get_active_sessions(self, user_id: int = None):
        """الحصول على الجلسات النشطة"""
        query = """
            SELECT us.*, u.username, u.full_name
            FROM user_sessions us
            JOIN users u ON us.user_id = u.id
            WHERE us.is_active = 1
        """
        params = []

        if user_id:
            query += " AND us.user_id = ?"
            params.append(user_id)

        query += " ORDER BY us.last_activity DESC"
        return self.fetch_all(query, params)

    def cleanup_expired_sessions(self, timeout_minutes: int = 60):
        """تنظيف الجلسات المنتهية الصلاحية"""
        self.execute_query("""
            UPDATE user_sessions
            SET is_active = 0, logout_time = CURRENT_TIMESTAMP
            WHERE is_active = 1
            AND datetime(last_activity, '+{} minutes') < datetime('now')
        """.format(timeout_minutes))

    # طرق إدارة النسخ الاحتياطية
    def create_backup_record(self, backup_name: str, backup_path: str, backup_size: int = None,
                           backup_type: str = 'manual', user_id: int = None):
        """إنشاء سجل نسخة احتياطية"""
        cursor = self.execute_query("""
            INSERT INTO backups (backup_name, backup_path, backup_size, backup_type, user_id)
            VALUES (?, ?, ?, ?, ?)
        """, (backup_name, backup_path, backup_size, backup_type, user_id))
        return cursor.lastrowid

    def update_backup_status(self, backup_id: int, status: str):
        """تحديث حالة النسخة الاحتياطية"""
        self.execute_query("""
            UPDATE backups SET status = ? WHERE id = ?
        """, (status, backup_id))

    def get_backups(self, limit: int = 50):
        """الحصول على قائمة النسخ الاحتياطية"""
        return self.fetch_all("""
            SELECT b.*, u.username, u.full_name
            FROM backups b
            LEFT JOIN users u ON b.user_id = u.id
            ORDER BY b.created_at DESC
            LIMIT ?
        """, (limit,))

    def delete_old_backups(self, retention_days: int = 30):
        """حذف النسخ الاحتياطية القديمة"""
        self.execute_query("""
            DELETE FROM backups
            WHERE date(created_at, '+{} days') < date('now')
        """.format(retention_days))

    # طرق إحصائيات قاعدة البيانات
    def get_database_stats(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        stats = {}

        # عدد السجلات في كل جدول
        tables = ['users', 'products', 'customers', 'suppliers', 'invoices',
                 'invoice_items', 'payments', 'stock_movements', 'categories']

        for table in tables:
            try:
                result = self.fetch_one(f"SELECT COUNT(*) as count FROM {table}")
                stats[f'{table}_count'] = result['count'] if result else 0
            except:
                stats[f'{table}_count'] = 0

        # حجم قاعدة البيانات
        try:
            import os
            if os.path.exists(self.db_path):
                stats['database_size'] = os.path.getsize(self.db_path)
            else:
                stats['database_size'] = 0
        except:
            stats['database_size'] = 0

        return stats
