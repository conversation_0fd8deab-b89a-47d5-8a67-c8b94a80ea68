# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager
"""

import sqlite3
import os
import hashlib
from datetime import datetime
from typing import Optional, List, Dict, Any

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path: str = "data/almezan.db"):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.connection = None
        
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # الاتصال بقاعدة البيانات
        self.connect()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            print(f"تم الاتصال بقاعدة البيانات: {self.db_path}")
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            raise
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            print("تم إغلاق الاتصال بقاعدة البيانات")
    
    def execute_query(self, query: str, params: tuple = ()) -> sqlite3.Cursor:
        """
        تنفيذ استعلام SQL
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            cursor: مؤشر النتائج
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return cursor
        except Exception as e:
            self.connection.rollback()
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    def fetch_one(self, query: str, params: tuple = ()) -> Optional[Dict]:
        """
        جلب سجل واحد
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            dict: السجل أو None
        """
        cursor = self.execute_query(query, params)
        row = cursor.fetchone()
        return dict(row) if row else None
    
    def fetch_all(self, query: str, params: tuple = ()) -> List[Dict]:
        """
        جلب جميع السجلات
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            list: قائمة السجلات
        """
        cursor = self.execute_query(query, params)
        rows = cursor.fetchall()
        return [dict(row) for row in rows]
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        users_table = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'cashier',
            email TEXT,
            phone TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول العملات
        currencies_table = """
        CREATE TABLE IF NOT EXISTS currencies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            exchange_rate REAL DEFAULT 1.0,
            is_default BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول فئات المنتجات
        categories_table = """
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT,
            parent_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories (id)
        )
        """
        
        # جدول المنتجات
        products_table = """
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            category_id INTEGER,
            unit TEXT DEFAULT 'قطعة',
            cost_price REAL DEFAULT 0,
            selling_price REAL NOT NULL,
            stock_quantity INTEGER DEFAULT 0,
            min_stock_level INTEGER DEFAULT 0,
            max_stock_level INTEGER DEFAULT 1000,
            barcode TEXT,
            image_path TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
        """
        
        # جدول العملاء
        customers_table = """
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            credit_limit REAL DEFAULT 0,
            current_balance REAL DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول الموردين
        suppliers_table = """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            current_balance REAL DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول الفواتير
        invoices_table = """
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT UNIQUE NOT NULL,
            invoice_type TEXT NOT NULL, -- 'sale' or 'purchase'
            customer_id INTEGER,
            supplier_id INTEGER,
            user_id INTEGER NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            subtotal REAL NOT NULL DEFAULT 0,
            discount_amount REAL DEFAULT 0,
            discount_percentage REAL DEFAULT 0,
            tax_amount REAL DEFAULT 0,
            tax_percentage REAL DEFAULT 0,
            total_amount REAL NOT NULL DEFAULT 0,
            paid_amount REAL DEFAULT 0,
            remaining_amount REAL DEFAULT 0,
            payment_status TEXT DEFAULT 'pending', -- 'pending', 'partial', 'paid'
            payment_method TEXT DEFAULT 'cash', -- 'cash', 'credit', 'bank'
            notes TEXT,
            is_cancelled BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # جدول تفاصيل الفواتير
        invoice_items_table = """
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            discount_amount REAL DEFAULT 0,
            total_price REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        """

        # جدول المدفوعات
        payments_table = """
        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER,
            customer_id INTEGER,
            supplier_id INTEGER,
            payment_type TEXT NOT NULL, -- 'receive', 'pay'
            amount REAL NOT NULL,
            payment_method TEXT DEFAULT 'cash',
            payment_date DATE NOT NULL,
            reference_number TEXT,
            notes TEXT,
            user_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id),
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # جدول حركة المخزون
        stock_movements_table = """
        CREATE TABLE IF NOT EXISTS stock_movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
            quantity REAL NOT NULL,
            unit_cost REAL,
            reference_type TEXT, -- 'invoice', 'adjustment', 'initial'
            reference_id INTEGER,
            notes TEXT,
            user_id INTEGER NOT NULL,
            movement_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """

        # تنفيذ إنشاء الجداول
        tables = [
            users_table, currencies_table, categories_table, products_table,
            customers_table, suppliers_table, invoices_table, invoice_items_table,
            payments_table, stock_movements_table
        ]

        for table in tables:
            self.execute_query(table)

        # إدراج البيانات الافتراضية
        self._insert_default_data()

        print("تم إنشاء جداول قاعدة البيانات بنجاح")
    
    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        
        # إدراج المستخدم الافتراضي (المدير)
        admin_exists = self.fetch_one("SELECT id FROM users WHERE username = ?", ("admin",))
        if not admin_exists:
            admin_password = self._hash_password("admin123")
            self.execute_query("""
                INSERT INTO users (username, password_hash, full_name, role, email)
                VALUES (?, ?, ?, ?, ?)
            """, ("admin", admin_password, "مدير النظام", "admin", "<EMAIL>"))
        
        # إدراج العملة الافتراضية (الجنيه المصري)
        egp_exists = self.fetch_one("SELECT id FROM currencies WHERE code = ?", ("EGP",))
        if not egp_exists:
            self.execute_query("""
                INSERT INTO currencies (code, name, symbol, exchange_rate, is_default)
                VALUES (?, ?, ?, ?, ?)
            """, ("EGP", "الجنيه المصري", "ج.م", 1.0, 1))
        
        # إدراج فئات افتراضية
        default_categories = [
            ("عام", "فئة عامة للمنتجات"),
            ("إلكترونيات", "الأجهزة الإلكترونية"),
            ("ملابس", "الملابس والأزياء"),
            ("أغذية", "المواد الغذائية"),
            ("مستلزمات منزلية", "أدوات ومستلزمات المنزل")
        ]
        
        for name, description in default_categories:
            exists = self.fetch_one("SELECT id FROM categories WHERE name = ?", (name,))
            if not exists:
                self.execute_query("""
                    INSERT INTO categories (name, description)
                    VALUES (?, ?)
                """, (name, description))
    
    def _hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """التحقق من كلمة المرور"""
        return self._hash_password(password) == hashed
