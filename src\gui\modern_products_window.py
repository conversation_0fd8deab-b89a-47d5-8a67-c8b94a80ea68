# -*- coding: utf-8 -*-
"""
نافذة إدارة المنتجات الحديثة
Modern Products Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from ..models.product import Product, ProductManager

class ModernProductsWindow:
    """نافذة إدارة المنتجات الحديثة"""
    
    def __init__(self, parent, db_manager, config):
        """تهيئة نافذة المنتجات"""
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.product_manager = ProductManager(db_manager)
        
        # ألوان أنيقة
        self.colors = {
            'primary': '#1e40af',
            'secondary': '#7c3aed',
            'success': '#059669',
            'warning': '#d97706',
            'danger': '#dc2626',
            'light': '#f8fafc',
            'dark': '#1e293b',
            'background': '#ffffff',
            'card': '#f1f5f9',
            'border': '#e2e8f0',
            'text': '#334155',
            'text_light': '#64748b'
        }
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات
        self.products = []
        self.selected_product = None
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        # إعداد الواجهة
        self.setup_ui()
        self.load_products()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📦 إدارة المنتجات")
        self.window.geometry("1300x850")
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم أقل من الحد الأدنى
        self.window.minsize(1000, 600)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # شريط البحث والأدوات
        self.create_toolbar(main_container)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_container, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # قائمة المنتجات
        self.create_products_list(content_frame)
        
        # لوحة التفاصيل
        self.create_details_panel(content_frame)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان
        title_label = tk.Label(header_frame,
                              text="📦 إدارة المنتجات",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.LEFT)
        
        # زر الإغلاق
        close_btn = tk.Button(header_frame,
                             text="✕ إغلاق",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['danger'],
                             relief='flat',
                             padx=20,
                             pady=8,
                             command=self.window.destroy)
        close_btn.pack(side=tk.RIGHT)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        toolbar_frame.pack(fill=tk.X, pady=(0, 20))
        
        # الجانب الأيسر - البحث
        search_frame = tk.Frame(toolbar_frame, bg=self.colors['card'])
        search_frame.pack(side=tk.LEFT, padx=15, pady=15)
        
        search_label = tk.Label(search_frame,
                               text="🔍 البحث:",
                               font=('Segoe UI', 11),
                               fg=self.colors['text'],
                               bg=self.colors['card'])
        search_label.pack(side=tk.LEFT)
        
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=('Segoe UI', 11),
                               width=30,
                               relief='flat',
                               bd=1)
        search_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # الجانب الأيمن - الأزرار
        buttons_frame = tk.Frame(toolbar_frame, bg=self.colors['card'])
        buttons_frame.pack(side=tk.RIGHT, padx=15, pady=15)
        
        # أزرار العمليات
        buttons = [
            ("➕ إضافة منتج", self.add_product, self.colors['success']),
            ("✏️ تعديل", self.edit_product, self.colors['primary']),
            ("🗑️ حذف", self.delete_product, self.colors['danger']),
            ("🔄 تحديث", self.refresh_products, self.colors['secondary'])
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(buttons_frame,
                           text=text,
                           font=('Segoe UI', 10),
                           fg='white',
                           bg=color,
                           relief='flat',
                           padx=15,
                           pady=8,
                           command=command)
            btn.pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_products_list(self, parent):
        """إنشاء قائمة المنتجات"""
        # إطار القائمة
        list_frame = tk.Frame(parent, bg=self.colors['background'])
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عنوان القائمة
        list_title = tk.Label(list_frame,
                             text="📋 قائمة المنتجات",
                             font=('Segoe UI', 14, 'bold'),
                             fg=self.colors['text'],
                             bg=self.colors['background'])
        list_title.pack(anchor='w', pady=(0, 10))
        
        # إطار الجدول
        table_frame = tk.Frame(list_frame, bg=self.colors['card'], relief='flat', bd=1)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ('code', 'name', 'category', 'stock', 'selling_price', 'status')
        
        self.products_tree = ttk.Treeview(table_frame, 
                                         columns=columns, 
                                         show='headings',
                                         height=20)
        
        # تعيين عناوين الأعمدة
        headers = {
            'code': 'الكود',
            'name': 'اسم المنتج',
            'category': 'الفئة',
            'stock': 'المخزون',
            'selling_price': 'سعر البيع',
            'status': 'الحالة'
        }
        
        for col, header in headers.items():
            self.products_tree.heading(col, text=header)
            self.products_tree.column(col, width=120, anchor='center')
        
        # تعيين عرض أعمدة محددة
        self.products_tree.column('name', width=200, anchor='w')
        self.products_tree.column('category', width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # ربط الأحداث
        self.products_tree.bind('<<TreeviewSelect>>', self.on_product_select)
        self.products_tree.bind('<Double-1>', self.edit_product)
    
    def create_details_panel(self, parent):
        """إنشاء لوحة التفاصيل"""
        # إطار التفاصيل
        details_frame = tk.Frame(parent, bg=self.colors['card'], width=350, relief='flat', bd=1)
        details_frame.pack(side=tk.RIGHT, fill=tk.Y)
        details_frame.pack_propagate(False)
        
        # عنوان التفاصيل
        details_title = tk.Label(details_frame,
                                text="📄 تفاصيل المنتج",
                                font=('Segoe UI', 14, 'bold'),
                                fg=self.colors['text'],
                                bg=self.colors['card'])
        details_title.pack(pady=(20, 15))
        
        # إطار المعلومات
        info_frame = tk.Frame(details_frame, bg=self.colors['card'])
        info_frame.pack(fill=tk.X, padx=20)
        
        # تسميات المعلومات
        self.detail_labels = {}
        details_info = [
            ('code', 'الكود:'),
            ('name', 'اسم المنتج:'),
            ('category', 'الفئة:'),
            ('cost_price', 'سعر التكلفة:'),
            ('selling_price', 'سعر البيع:'),
            ('stock_quantity', 'الكمية:'),
            ('min_stock_level', 'الحد الأدنى:'),
            ('status', 'الحالة:')
        ]
        
        for i, (key, label) in enumerate(details_info):
            # تسمية الحقل
            field_label = tk.Label(info_frame,
                                  text=label,
                                  font=('Segoe UI', 10, 'bold'),
                                  fg=self.colors['text'],
                                  bg=self.colors['card'],
                                  anchor='w')
            field_label.grid(row=i, column=0, sticky='w', pady=5)
            
            # قيمة الحقل
            value_label = tk.Label(info_frame,
                                  text='-',
                                  font=('Segoe UI', 10),
                                  fg=self.colors['text_light'],
                                  bg=self.colors['card'],
                                  anchor='w')
            value_label.grid(row=i, column=1, sticky='w', padx=(10, 0), pady=5)
            
            self.detail_labels[key] = value_label
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(details_frame, bg=self.colors['background'], relief='flat', bd=1)
        stats_frame.pack(fill=tk.X, padx=20, pady=20)
        
        stats_title = tk.Label(stats_frame,
                              text="📊 إحصائيات سريعة",
                              font=('Segoe UI', 12, 'bold'),
                              fg=self.colors['text'],
                              bg=self.colors['background'])
        stats_title.pack(pady=(10, 5))
        
        # إحصائيات المنتجات
        self.stats_labels = {}
        stats_info = [
            ('total_products', 'إجمالي المنتجات:'),
            ('low_stock', 'منتجات منخفضة:'),
            ('out_of_stock', 'منتجات نافدة:'),
            ('total_value', 'قيمة المخزون:')
        ]
        
        for i, (key, label) in enumerate(stats_info):
            stat_frame = tk.Frame(stats_frame, bg=self.colors['background'])
            stat_frame.pack(fill=tk.X, pady=2)
            
            stat_label = tk.Label(stat_frame,
                                 text=label,
                                 font=('Segoe UI', 9),
                                 fg=self.colors['text'],
                                 bg=self.colors['background'])
            stat_label.pack(side=tk.LEFT)
            
            stat_value = tk.Label(stat_frame,
                                 text='0',
                                 font=('Segoe UI', 9, 'bold'),
                                 fg=self.colors['primary'],
                                 bg=self.colors['background'])
            stat_value.pack(side=tk.RIGHT)
            
            self.stats_labels[key] = stat_value
    
    def load_products(self):
        """تحميل المنتجات"""
        try:
            # مسح البيانات السابقة
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
            
            # تحميل المنتجات من قاعدة البيانات
            self.products = self.product_manager.get_all_products()
            
            # إضافة المنتجات للجدول
            for product in self.products:
                # تحديد حالة المخزون
                if product.stock_quantity <= 0:
                    status = "نافد"
                elif product.stock_quantity <= product.min_stock_level:
                    status = "منخفض"
                else:
                    status = "متوفر"
                
                self.products_tree.insert('', tk.END, values=(
                    product.code,
                    product.name,
                    product.category or 'غير محدد',
                    f"{product.stock_quantity:.2f}",
                    f"{product.selling_price:.2f} ج.م",
                    status
                ))
            
            # تحديث الإحصائيات
            self.update_statistics()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المنتجات: {str(e)}")
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            total_products = len(self.products)
            low_stock = sum(1 for p in self.products if 0 < p.stock_quantity <= p.min_stock_level)
            out_of_stock = sum(1 for p in self.products if p.stock_quantity <= 0)
            total_value = sum(p.stock_quantity * p.selling_price for p in self.products)
            
            self.stats_labels['total_products'].config(text=str(total_products))
            self.stats_labels['low_stock'].config(text=str(low_stock))
            self.stats_labels['out_of_stock'].config(text=str(out_of_stock))
            self.stats_labels['total_value'].config(text=f"{total_value:.2f} ج.م")
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def on_product_select(self, event):
        """عند اختيار منتج"""
        selection = self.products_tree.selection()
        if selection:
            item = self.products_tree.item(selection[0])
            product_code = item['values'][0]
            
            # البحث عن المنتج
            self.selected_product = None
            for product in self.products:
                if product.code == product_code:
                    self.selected_product = product
                    break
            
            # تحديث التفاصيل
            self.update_details()
    
    def update_details(self):
        """تحديث تفاصيل المنتج"""
        if self.selected_product:
            details = {
                'code': self.selected_product.code,
                'name': self.selected_product.name,
                'category': self.selected_product.category or 'غير محدد',
                'cost_price': f"{self.selected_product.cost_price:.2f} ج.م",
                'selling_price': f"{self.selected_product.selling_price:.2f} ج.م",
                'stock_quantity': f"{self.selected_product.stock_quantity:.2f}",
                'min_stock_level': f"{self.selected_product.min_stock_level:.2f}",
                'status': 'نشط' if self.selected_product.is_active else 'غير نشط'
            }
            
            for key, value in details.items():
                if key in self.detail_labels:
                    self.detail_labels[key].config(text=value)
        else:
            # مسح التفاصيل
            for label in self.detail_labels.values():
                label.config(text='-')
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        search_text = self.search_var.get().lower()
        
        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # تصفية وعرض المنتجات
        for product in self.products:
            if (search_text in product.code.lower() or 
                search_text in product.name.lower() or
                search_text in (product.category or '').lower()):
                
                # تحديد حالة المخزون
                if product.stock_quantity <= 0:
                    status = "نافد"
                elif product.stock_quantity <= product.min_stock_level:
                    status = "منخفض"
                else:
                    status = "متوفر"
                
                self.products_tree.insert('', tk.END, values=(
                    product.code,
                    product.name,
                    product.category or 'غير محدد',
                    f"{product.stock_quantity:.2f}",
                    f"{product.selling_price:.2f} ج.م",
                    status
                ))
    
    def add_product(self):
        """إضافة منتج جديد"""
        try:
            from .product_dialog import ProductDialog
            dialog = ProductDialog(self.window, self.db, self.config)
            if dialog.result:
                self.load_products()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إضافة المنتج: {str(e)}")
    
    def edit_product(self):
        """تعديل المنتج المحدد"""
        if not self.selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return
        
        try:
            from .product_dialog import ProductDialog
            dialog = ProductDialog(self.window, self.db, self.config, self.selected_product)
            if dialog.result:
                self.load_products()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة تعديل المنتج: {str(e)}")
    
    def delete_product(self):
        """حذف المنتج المحدد"""
        if not self.selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل تريد حذف المنتج '{self.selected_product.name}'؟"):
            try:
                self.product_manager.delete_product(self.selected_product.id)
                messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
                self.load_products()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المنتج: {str(e)}")
    
    def refresh_products(self):
        """تحديث قائمة المنتجات"""
        self.load_products()
        messagebox.showinfo("تم", "تم تحديث قائمة المنتجات")
