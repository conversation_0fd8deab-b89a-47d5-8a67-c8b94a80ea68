# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطية
Backup Manager
"""

import os
import shutil
import sqlite3
import zipfile
import threading
import schedule
import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict

class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    def __init__(self, db_manager, config):
        """تهيئة مدير النسخ الاحتياطية"""
        self.db = db_manager
        self.config = config
        self.backup_thread = None
        self.is_running = False
        
        # إعداد مجلد النسخ الاحتياطية
        self.backup_dir = self.db.get_setting("backup_location", "./backups")
        self.ensure_backup_directory()
    
    def ensure_backup_directory(self):
        """التأكد من وجود مجلد النسخ الاحتياطية"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, backup_type: str = "manual", user_id: Optional[int] = None) -> Dict:
        """إنشاء نسخة احتياطية"""
        try:
            # إنشاء اسم النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"almezan_backup_{timestamp}.zip"
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            # إنشاء سجل النسخة الاحتياطية
            backup_id = self.db.create_backup_record(
                backup_name=backup_name,
                backup_path=backup_path,
                backup_type=backup_type,
                user_id=user_id
            )
            
            # تحديث الحالة إلى "قيد التنفيذ"
            self.db.update_backup_status(backup_id, "in_progress")
            
            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ قاعدة البيانات
                db_path = self.db.db_path
                if os.path.exists(db_path):
                    zipf.write(db_path, "database.db")
                
                # نسخ ملفات الإعدادات
                config_path = "config.json"
                if os.path.exists(config_path):
                    zipf.write(config_path, "config.json")
                
                # نسخ مجلد الصور (إذا كان موجوداً)
                images_dir = "images"
                if os.path.exists(images_dir):
                    for root, dirs, files in os.walk(images_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_path = os.path.relpath(file_path, ".")
                            zipf.write(file_path, arc_path)
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    "backup_date": datetime.now().isoformat(),
                    "backup_type": backup_type,
                    "database_stats": self.db.get_database_stats(),
                    "version": "1.0"
                }
                
                import json
                zipf.writestr("backup_info.json", json.dumps(backup_info, ensure_ascii=False, indent=2))
            
            # حساب حجم النسخة الاحتياطية
            backup_size = os.path.getsize(backup_path)
            
            # تحديث سجل النسخة الاحتياطية
            self.db.execute_query("""
                UPDATE backups 
                SET backup_size = ?, status = 'completed'
                WHERE id = ?
            """, (backup_size, backup_id))
            
            # تسجيل العملية في سجل العمليات
            if user_id:
                self.db.log_action(
                    user_id=user_id,
                    action="create_backup",
                    table_name="backups",
                    record_id=backup_id,
                    new_values={"backup_name": backup_name, "backup_size": backup_size}
                )
            
            return {
                "success": True,
                "backup_id": backup_id,
                "backup_name": backup_name,
                "backup_path": backup_path,
                "backup_size": backup_size,
                "message": "تم إنشاء النسخة الاحتياطية بنجاح"
            }
            
        except Exception as e:
            # تحديث الحالة إلى "فشل"
            if 'backup_id' in locals():
                self.db.update_backup_status(backup_id, "failed")
            
            return {
                "success": False,
                "error": str(e),
                "message": f"فشل في إنشاء النسخة الاحتياطية: {str(e)}"
            }
    
    def restore_backup(self, backup_path: str, user_id: Optional[int] = None) -> Dict:
        """استعادة نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                return {
                    "success": False,
                    "error": "backup_not_found",
                    "message": "ملف النسخة الاحتياطية غير موجود"
                }
            
            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup = self.create_backup("pre_restore", user_id)
            if not current_backup["success"]:
                return {
                    "success": False,
                    "error": "pre_restore_backup_failed",
                    "message": "فشل في إنشاء نسخة احتياطية من الحالة الحالية"
                }
            
            # استخراج النسخة الاحتياطية
            restore_dir = os.path.join(self.backup_dir, "restore_temp")
            if os.path.exists(restore_dir):
                shutil.rmtree(restore_dir)
            os.makedirs(restore_dir)
            
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                zipf.extractall(restore_dir)
            
            # التحقق من صحة النسخة الاحتياطية
            db_file = os.path.join(restore_dir, "database.db")
            if not os.path.exists(db_file):
                return {
                    "success": False,
                    "error": "invalid_backup",
                    "message": "النسخة الاحتياطية غير صالحة - ملف قاعدة البيانات مفقود"
                }
            
            # إغلاق الاتصال الحالي بقاعدة البيانات
            self.db.close()
            
            # استعادة قاعدة البيانات
            current_db_path = self.db.db_path
            backup_db_path = current_db_path + ".backup"
            
            # نسخ قاعدة البيانات الحالية كنسخة احتياطية
            shutil.copy2(current_db_path, backup_db_path)
            
            # استعادة قاعدة البيانات من النسخة الاحتياطية
            shutil.copy2(db_file, current_db_path)
            
            # استعادة ملف الإعدادات
            config_file = os.path.join(restore_dir, "config.json")
            if os.path.exists(config_file):
                shutil.copy2(config_file, "config.json")
            
            # استعادة مجلد الصور
            images_restore_dir = os.path.join(restore_dir, "images")
            if os.path.exists(images_restore_dir):
                if os.path.exists("images"):
                    shutil.rmtree("images")
                shutil.copytree(images_restore_dir, "images")
            
            # إعادة الاتصال بقاعدة البيانات
            self.db.connect()
            
            # تنظيف المجلد المؤقت
            shutil.rmtree(restore_dir)
            
            # تسجيل العملية في سجل العمليات
            if user_id:
                self.db.log_action(
                    user_id=user_id,
                    action="restore_backup",
                    new_values={"backup_path": backup_path}
                )
            
            return {
                "success": True,
                "message": "تم استعادة النسخة الاحتياطية بنجاح",
                "pre_restore_backup": current_backup["backup_name"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"فشل في استعادة النسخة الاحتياطية: {str(e)}"
            }
    
    def delete_backup(self, backup_id: int, user_id: Optional[int] = None) -> Dict:
        """حذف نسخة احتياطية"""
        try:
            # الحصول على معلومات النسخة الاحتياطية
            backup = self.db.fetch_one("SELECT * FROM backups WHERE id = ?", (backup_id,))
            if not backup:
                return {
                    "success": False,
                    "error": "backup_not_found",
                    "message": "النسخة الاحتياطية غير موجودة"
                }
            
            # حذف الملف
            if os.path.exists(backup['backup_path']):
                os.remove(backup['backup_path'])
            
            # حذف السجل من قاعدة البيانات
            self.db.execute_query("DELETE FROM backups WHERE id = ?", (backup_id,))
            
            # تسجيل العملية في سجل العمليات
            if user_id:
                self.db.log_action(
                    user_id=user_id,
                    action="delete_backup",
                    table_name="backups",
                    record_id=backup_id,
                    old_values={"backup_name": backup['backup_name']}
                )
            
            return {
                "success": True,
                "message": "تم حذف النسخة الاحتياطية بنجاح"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"فشل في حذف النسخة الاحتياطية: {str(e)}"
            }
    
    def cleanup_old_backups(self) -> Dict:
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            retention_days = self.db.get_setting("backup_retention_days", 30)
            
            # الحصول على النسخ الاحتياطية القديمة
            old_backups = self.db.fetch_all("""
                SELECT * FROM backups 
                WHERE date(created_at, '+{} days') < date('now')
            """.format(retention_days))
            
            deleted_count = 0
            for backup in old_backups:
                # حذف الملف
                if os.path.exists(backup['backup_path']):
                    os.remove(backup['backup_path'])
                deleted_count += 1
            
            # حذف السجلات من قاعدة البيانات
            self.db.delete_old_backups(retention_days)
            
            return {
                "success": True,
                "deleted_count": deleted_count,
                "message": f"تم حذف {deleted_count} نسخة احتياطية قديمة"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"فشل في تنظيف النسخ الاحتياطية القديمة: {str(e)}"
            }
    
    def start_automatic_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if self.is_running:
            return
        
        # التحقق من تفعيل النسخ الاحتياطي التلقائي
        if not self.db.get_setting("auto_backup_enabled", True):
            return
        
        # جدولة النسخ الاحتياطي
        frequency = self.db.get_setting("backup_frequency", "daily")
        
        if frequency == "daily":
            schedule.every().day.at("02:00").do(self._automatic_backup_job)
        elif frequency == "weekly":
            schedule.every().week.do(self._automatic_backup_job)
        elif frequency == "monthly":
            schedule.every(30).days.do(self._automatic_backup_job)
        
        # بدء الخيط
        self.is_running = True
        self.backup_thread = threading.Thread(target=self._backup_scheduler, daemon=True)
        self.backup_thread.start()
    
    def stop_automatic_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.is_running = False
        schedule.clear()
    
    def _backup_scheduler(self):
        """مجدول النسخ الاحتياطي"""
        while self.is_running:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة
    
    def _automatic_backup_job(self):
        """مهمة النسخ الاحتياطي التلقائي"""
        result = self.create_backup("automatic")
        if result["success"]:
            print(f"تم إنشاء نسخة احتياطية تلقائية: {result['backup_name']}")
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
        else:
            print(f"فشل في إنشاء النسخة الاحتياطية التلقائية: {result['message']}")
    
    def get_backup_list(self) -> List[Dict]:
        """الحصول على قائمة النسخ الاحتياطية"""
        return self.db.get_backups()
    
    def get_backup_info(self, backup_path: str) -> Dict:
        """الحصول على معلومات النسخة الاحتياطية"""
        try:
            if not os.path.exists(backup_path):
                return {"success": False, "error": "backup_not_found"}
            
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                if "backup_info.json" in zipf.namelist():
                    import json
                    info_data = zipf.read("backup_info.json").decode('utf-8')
                    backup_info = json.loads(info_data)
                    backup_info["success"] = True
                    return backup_info
            
            return {"success": False, "error": "no_backup_info"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
