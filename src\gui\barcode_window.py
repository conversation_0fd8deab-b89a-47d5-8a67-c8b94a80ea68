# -*- coding: utf-8 -*-
"""
نافذة إدارة الباركود
Barcode Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional

from ..utils.colors import AppColors
from ..utils.barcode_manager import BarcodeManager

class BarcodeWindow:
    """نافذة إدارة الباركود"""
    
    def __init__(self, parent, db_manager, config, current_user):
        """تهيئة نافذة الباركود"""
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.current_user = current_user
        self.barcode_manager = BarcodeManager(db_manager)
        
        # التحقق من الصلاحيات
        if not self.current_user.has_permission("manage_products"):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة الباركود")
            return
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.load_products()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 إدارة الباركود")
        self.window.geometry("900x700")
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.configure(bg=self.colors['background'])
        
        # منع تغيير الحجم أقل من الحد الأدنى
        self.window.minsize(800, 600)
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.search_var = tk.StringVar()
        self.barcode_type_var = tk.StringVar(value="code128")
        self.selected_products = []
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الشريط العلوي
        self.create_header(main_container)
        
        # شريط الأدوات
        self.create_toolbar(main_container)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_container, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # قائمة المنتجات
        self.create_products_list(content_frame)
        
        # لوحة التحكم
        self.create_control_panel(content_frame)
    
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان (على اليمين للعربية)
        title_label = tk.Label(header_frame,
                              text="📊 إدارة الباركود",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack(side=tk.RIGHT)
        
        # زر الإغلاق (على اليسار للعربية)
        close_btn = tk.Button(header_frame,
                             text="✕ إغلاق",
                             font=('Segoe UI', 11),
                             fg='white',
                             bg=self.colors['danger'],
                             relief='flat',
                             padx=20,
                             pady=8,
                             command=self.window.destroy)
        close_btn.pack(side=tk.LEFT)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent, bg=self.colors['background'])
        toolbar_frame.pack(fill=tk.X, pady=(0, 20))
        
        # البحث (على اليمين)
        search_frame = tk.Frame(toolbar_frame, bg=self.colors['background'])
        search_frame.pack(side=tk.RIGHT)
        
        tk.Label(search_frame,
                text="🔍 البحث:",
                font=('Segoe UI', 10),
                fg=self.colors['text'],
                bg=self.colors['background']).pack(side=tk.RIGHT)
        
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=('Segoe UI', 10),
                               width=20)
        search_entry.pack(side=tk.RIGHT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # أزرار العمليات (على اليسار)
        buttons_frame = tk.Frame(toolbar_frame, bg=self.colors['background'])
        buttons_frame.pack(side=tk.LEFT)
        
        # زر تحديث
        refresh_btn = tk.Button(buttons_frame,
                               text="🔄 تحديث",
                               font=('Segoe UI', 10),
                               fg='white',
                               bg=self.colors['secondary'],
                               relief='flat',
                               padx=15,
                               pady=5,
                               command=self.load_products)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر إنشاء باركود متعدد
        batch_btn = tk.Button(buttons_frame,
                             text="📊 إنشاء متعدد",
                             font=('Segoe UI', 10),
                             fg='white',
                             bg=self.colors['primary'],
                             relief='flat',
                             padx=15,
                             pady=5,
                             command=self.batch_generate)
        batch_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    def create_products_list(self, parent):
        """إنشاء قائمة المنتجات"""
        # إطار القائمة
        list_frame = tk.LabelFrame(parent,
                                  text="📦 قائمة المنتجات",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.colors['text'],
                                  bg=self.colors['background'])
        list_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(0, 20))
        
        # إطار الجدول
        table_frame = tk.Frame(list_frame, bg=self.colors['background'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # إنشاء الجدول
        columns = ("id", "name", "barcode", "price", "stock")
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين
        self.products_tree.heading("id", text="المعرف")
        self.products_tree.heading("name", text="اسم المنتج")
        self.products_tree.heading("barcode", text="الباركود")
        self.products_tree.heading("price", text="السعر")
        self.products_tree.heading("stock", text="المخزون")
        
        # تعريف عرض الأعمدة
        self.products_tree.column("id", width=80, anchor="center")
        self.products_tree.column("name", width=200, anchor="e")
        self.products_tree.column("barcode", width=150, anchor="center")
        self.products_tree.column("price", width=100, anchor="center")
        self.products_tree.column("stock", width=100, anchor="center")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول والشريط
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.products_tree.bind('<Double-1>', self.on_product_select)
    
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = tk.LabelFrame(parent,
                                     text="🎛️ لوحة التحكم",
                                     font=('Segoe UI', 12, 'bold'),
                                     fg=self.colors['text'],
                                     bg=self.colors['background'])
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 0))
        
        # إطار المحتوى
        content_frame = tk.Frame(control_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # نوع الباركود
        type_frame = tk.Frame(content_frame, bg=self.colors['background'])
        type_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(type_frame,
                text="نوع الباركود:",
                font=('Segoe UI', 11, 'bold'),
                fg=self.colors['text'],
                bg=self.colors['background']).pack(anchor='e')
        
        barcode_types = self.barcode_manager.get_barcode_types()
        if barcode_types:
            type_combo = ttk.Combobox(type_frame,
                                     textvariable=self.barcode_type_var,
                                     values=barcode_types,
                                     state="readonly",
                                     width=15)
            type_combo.pack(pady=(5, 0))
        else:
            tk.Label(type_frame,
                    text="مكتبات الباركود غير مثبتة",
                    font=('Segoe UI', 9),
                    fg=self.colors['danger'],
                    bg=self.colors['background']).pack(pady=(5, 0))
        
        # أزرار العمليات
        buttons_frame = tk.Frame(content_frame, bg=self.colors['background'])
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # زر إنشاء باركود
        generate_btn = tk.Button(buttons_frame,
                                text="📊 إنشاء باركود",
                                font=('Segoe UI', 11, 'bold'),
                                fg='white',
                                bg=self.colors['primary'],
                                relief='flat',
                                padx=20,
                                pady=10,
                                command=self.generate_barcode)
        generate_btn.pack(fill=tk.X, pady=(0, 10))
        
        # زر إنشاء QR
        qr_btn = tk.Button(buttons_frame,
                          text="🔲 إنشاء QR",
                          font=('Segoe UI', 11),
                          fg='white',
                          bg=self.colors['secondary'],
                          relief='flat',
                          padx=20,
                          pady=10,
                          command=self.generate_qr)
        qr_btn.pack(fill=tk.X, pady=(0, 10))
        
        # زر البحث بالباركود
        search_btn = tk.Button(buttons_frame,
                              text="🔍 بحث بالباركود",
                              font=('Segoe UI', 11),
                              fg='white',
                              bg=self.colors['info'],
                              relief='flat',
                              padx=20,
                              pady=10,
                              command=self.search_by_barcode)
        search_btn.pack(fill=tk.X, pady=(0, 10))
        
        # زر تثبيت المكتبات
        if not self.barcode_manager.is_available():
            install_btn = tk.Button(buttons_frame,
                                   text="📥 تثبيت المكتبات",
                                   font=('Segoe UI', 11),
                                   fg='white',
                                   bg=self.colors['warning'],
                                   relief='flat',
                                   padx=20,
                                   pady=10,
                                   command=self.install_libraries)
            install_btn.pack(fill=tk.X, pady=(0, 10))
        
        # معلومات المكتبات
        info_frame = tk.Frame(content_frame, bg=self.colors['background'])
        info_frame.pack(fill=tk.X, pady=(30, 0))
        
        tk.Label(info_frame,
                text="ℹ️ معلومات المكتبات:",
                font=('Segoe UI', 10, 'bold'),
                fg=self.colors['text'],
                bg=self.colors['background']).pack(anchor='e')
        
        # حالة المكتبات
        if self.barcode_manager.barcode_library:
            tk.Label(info_frame,
                    text="✅ python-barcode",
                    font=('Segoe UI', 9),
                    fg=self.colors['success'],
                    bg=self.colors['background']).pack(anchor='e')
        else:
            tk.Label(info_frame,
                    text="❌ python-barcode",
                    font=('Segoe UI', 9),
                    fg=self.colors['danger'],
                    bg=self.colors['background']).pack(anchor='e')
        
        if self.barcode_manager.qr_library:
            tk.Label(info_frame,
                    text="✅ qrcode",
                    font=('Segoe UI', 9),
                    fg=self.colors['success'],
                    bg=self.colors['background']).pack(anchor='e')
        else:
            tk.Label(info_frame,
                    text="❌ qrcode",
                    font=('Segoe UI', 9),
                    fg=self.colors['danger'],
                    bg=self.colors['background']).pack(anchor='e')
    
    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
            
            # جلب المنتجات
            query = """
            SELECT id, name, barcode, selling_price, stock_quantity
            FROM products
            ORDER BY name
            """
            products = self.db.fetch_all(query)
            
            # إضافة المنتجات للجدول
            for product in products:
                barcode = product['barcode'] if product['barcode'] else "غير محدد"
                price = f"{product['selling_price']:.2f}" if product['selling_price'] else "0.00"
                stock = product['stock_quantity'] if product['stock_quantity'] else 0
                
                self.products_tree.insert("", tk.END, values=(
                    product['id'],
                    product['name'],
                    barcode,
                    price,
                    stock
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المنتجات: {str(e)}")
    
    def on_search(self, event=None):
        """البحث في المنتجات"""
        search_term = self.search_var.get().strip()
        
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        try:
            if search_term:
                query = """
                SELECT id, name, barcode, selling_price, stock_quantity
                FROM products
                WHERE name LIKE ? OR barcode LIKE ?
                ORDER BY name
                """
                products = self.db.fetch_all(query, (f"%{search_term}%", f"%{search_term}%"))
            else:
                query = """
                SELECT id, name, barcode, selling_price, stock_quantity
                FROM products
                ORDER BY name
                """
                products = self.db.fetch_all(query)
            
            # إضافة المنتجات للجدول
            for product in products:
                barcode = product['barcode'] if product['barcode'] else "غير محدد"
                price = f"{product['selling_price']:.2f}" if product['selling_price'] else "0.00"
                stock = product['stock_quantity'] if product['stock_quantity'] else 0
                
                self.products_tree.insert("", tk.END, values=(
                    product['id'],
                    product['name'],
                    barcode,
                    price,
                    stock
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    def on_product_select(self, event=None):
        """عند اختيار منتج"""
        selection = self.products_tree.selection()
        if selection:
            item = self.products_tree.item(selection[0])
            product_id = item['values'][0]
            product_name = item['values'][1]
            
            # عرض تفاصيل المنتج
            messagebox.showinfo("تفاصيل المنتج", 
                               f"المعرف: {product_id}\nالاسم: {product_name}")
    
    def generate_barcode(self):
        """إنشاء باركود للمنتج المحدد"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج أولاً")
            return
        
        item = self.products_tree.item(selection[0])
        product_id = item['values'][0]
        product_name = item['values'][1]
        
        # إنشاء الباركود
        barcode_type = self.barcode_type_var.get()
        result = self.barcode_manager.generate_product_barcode(product_id, barcode_type)
        
        if result["success"]:
            messagebox.showinfo("نجح", f"تم إنشاء الباركود بنجاح\n{result['message']}")
            self.load_products()  # تحديث القائمة
        else:
            messagebox.showerror("خطأ", result["message"])
    
    def generate_qr(self):
        """إنشاء رمز QR للمنتج المحدد"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج أولاً")
            return
        
        item = self.products_tree.item(selection[0])
        product_id = item['values'][0]
        
        # إنشاء رمز QR
        result = self.barcode_manager.generate_product_qr(product_id)
        
        if result["success"]:
            messagebox.showinfo("نجح", f"تم إنشاء رمز QR بنجاح\n{result['message']}")
        else:
            messagebox.showerror("خطأ", result["message"])
    
    def search_by_barcode(self):
        """البحث عن منتج بالباركود"""
        from tkinter.simpledialog import askstring
        
        barcode = askstring("البحث بالباركود", "أدخل رقم الباركود:")
        if not barcode:
            return
        
        result = self.barcode_manager.search_product_by_barcode(barcode.strip())
        
        if result["success"]:
            product = result["product"]
            message = f"تم العثور على المنتج:\n"
            message += f"الاسم: {product['name']}\n"
            message += f"السعر: {product['selling_price']}\n"
            message += f"المخزون: {product['stock_quantity']}"
            messagebox.showinfo("نتيجة البحث", message)
        else:
            messagebox.showwarning("لم يتم العثور", result["message"])
    
    def batch_generate(self):
        """إنشاء باركود متعدد"""
        # الحصول على المنتجات بدون باركود
        products = self.barcode_manager.get_products_without_barcode()
        
        if not products:
            messagebox.showinfo("معلومات", "جميع المنتجات لديها باركود")
            return
        
        # تأكيد العملية
        count = len(products)
        if messagebox.askyesno("تأكيد", f"هل تريد إنشاء باركود لـ {count} منتج؟"):
            product_ids = [p['id'] for p in products]
            result = self.barcode_manager.batch_generate_barcodes(product_ids)
            
            if result["success"]:
                messagebox.showinfo("نجح", result["message"])
                self.load_products()  # تحديث القائمة
            else:
                messagebox.showerror("خطأ", result["message"])
    
    def install_libraries(self):
        """تثبيت المكتبات المطلوبة"""
        if messagebox.askyesno("تأكيد", "هل تريد تثبيت مكتبات الباركود؟"):
            result = self.barcode_manager.install_required_libraries()
            
            if result["success"]:
                messagebox.showinfo("نجح", result["message"])
                # إعادة إنشاء النافذة لتحديث الواجهة
                self.window.destroy()
                BarcodeWindow(self.parent, self.db, self.config, self.current_user)
            else:
                messagebox.showerror("خطأ", result["message"])
