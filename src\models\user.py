# -*- coding: utf-8 -*-
"""
نموذج المستخدم
User Model
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, List
import hashlib
import secrets

@dataclass
class User:
    """نموذج المستخدم"""
    id: Optional[int] = None
    username: str = ""
    email: str = ""
    password_hash: str = ""
    full_name: str = ""
    role: str = "user"  # admin, manager, user, viewer
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    permissions: List[str] = field(default_factory=list)
    
    def set_password(self, password: str):
        """تعيين كلمة المرور مع التشفير"""
        salt = secrets.token_hex(16)
        self.password_hash = hashlib.pbkdf2_hmac('sha256', 
                                               password.encode('utf-8'), 
                                               salt.encode('utf-8'), 
                                               100000).hex() + ':' + salt
    
    def check_password(self, password: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            stored_hash, salt = self.password_hash.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256',
                                              password.encode('utf-8'),
                                              salt.encode('utf-8'),
                                              100000).hex()
            return password_hash == stored_hash
        except:
            return False
    
    def has_permission(self, permission: str) -> bool:
        """التحقق من وجود صلاحية معينة"""
        if self.role == "admin":
            return True
        return permission in self.permissions
    
    def get_role_display(self) -> str:
        """الحصول على اسم الدور للعرض"""
        roles = {
            "admin": "مدير النظام",
            "manager": "مدير",
            "user": "مستخدم",
            "viewer": "مشاهد"
        }
        return roles.get(self.role, "غير محدد")

@dataclass
class UserSession:
    """جلسة المستخدم"""
    id: Optional[int] = None
    user_id: int = 0
    session_token: str = ""
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    is_active: bool = True
    ip_address: str = ""
    user_agent: str = ""

class UserManager:
    """مدير المستخدمين"""
    
    def __init__(self, db_manager):
        """تهيئة مدير المستخدمين"""
        self.db = db_manager
        self.current_user = None
        self.current_session = None
        self.create_tables()
    
    def create_tables(self):
        """إنشاء جداول المستخدمين"""
        try:
            # جدول المستخدمين
            users_table = """
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                permissions TEXT DEFAULT '[]'
            )
            """
            
            # جدول الجلسات
            sessions_table = """
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                ip_address TEXT,
                user_agent TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            """
            
            # جدول الصلاحيات
            permissions_table = """
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                category TEXT DEFAULT 'general'
            )
            """
            
            self.db.execute_query(users_table)
            self.db.execute_query(sessions_table)
            self.db.execute_query(permissions_table)
            
            # إنشاء المستخدم الافتراضي إذا لم يكن موجوداً
            self.create_default_admin()
            
            # إنشاء الصلاحيات الافتراضية
            self.create_default_permissions()
            
        except Exception as e:
            print(f"خطأ في إنشاء جداول المستخدمين: {e}")
    
    def create_default_admin(self):
        """إنشاء مستخدم مدير افتراضي"""
        try:
            # التحقق من وجود مستخدمين
            query = "SELECT COUNT(*) FROM users"
            result = self.db.fetch_one(query)
            
            if result and result[0] == 0:
                # إنشاء مستخدم مدير افتراضي
                admin = User(
                    username="admin",
                    email="<EMAIL>",
                    full_name="مدير النظام",
                    role="admin",
                    is_active=True
                )
                admin.set_password("admin123")
                
                self.create_user(admin)
                print("تم إنشاء مستخدم مدير افتراضي: admin / admin123")
                
        except Exception as e:
            # تجاهل الخطأ إذا كان المستخدم موجود بالفعل
            if "UNIQUE constraint failed" not in str(e):
                print(f"خطأ في إنشاء المستخدم الافتراضي: {e}")
    
    def create_default_permissions(self):
        """إنشاء الصلاحيات الافتراضية"""
        try:
            default_permissions = [
                ("view_products", "عرض المنتجات", "products"),
                ("add_products", "إضافة المنتجات", "products"),
                ("edit_products", "تعديل المنتجات", "products"),
                ("delete_products", "حذف المنتجات", "products"),
                ("view_customers", "عرض العملاء", "customers"),
                ("add_customers", "إضافة العملاء", "customers"),
                ("edit_customers", "تعديل العملاء", "customers"),
                ("delete_customers", "حذف العملاء", "customers"),
                ("view_suppliers", "عرض الموردين", "suppliers"),
                ("add_suppliers", "إضافة الموردين", "suppliers"),
                ("edit_suppliers", "تعديل الموردين", "suppliers"),
                ("delete_suppliers", "حذف الموردين", "suppliers"),
                ("view_invoices", "عرض الفواتير", "invoices"),
                ("create_invoices", "إنشاء الفواتير", "invoices"),
                ("edit_invoices", "تعديل الفواتير", "invoices"),
                ("delete_invoices", "حذف الفواتير", "invoices"),
                ("view_reports", "عرض التقارير", "reports"),
                ("export_reports", "تصدير التقارير", "reports"),
                ("view_settings", "عرض الإعدادات", "settings"),
                ("edit_settings", "تعديل الإعدادات", "settings"),
                ("manage_users", "إدارة المستخدمين", "users"),
                ("manage_permissions", "إدارة الصلاحيات", "users"),
                ("manage_backups", "إدارة النسخ الاحتياطية", "system"),
                ("view_audit_log", "عرض سجل العمليات", "system")
            ]
            
            for name, description, category in default_permissions:
                query = "SELECT COUNT(*) FROM permissions WHERE name = ?"
                result = self.db.fetch_one(query, (name,))
                
                if result and result[0] == 0:
                    insert_query = """
                    INSERT INTO permissions (name, description, category)
                    VALUES (?, ?, ?)
                    """
                    self.db.execute_query(insert_query, (name, description, category))
                    
        except Exception as e:
            # تجاهل الخطأ إذا كانت الصلاحيات موجودة بالفعل
            if "UNIQUE constraint failed" not in str(e):
                print(f"خطأ في إنشاء الصلاحيات الافتراضية: {e}")
    
    def create_user(self, user: User) -> bool:
        """إنشاء مستخدم جديد"""
        try:
            query = """
            INSERT INTO users (username, email, password_hash, full_name, role, is_active, permissions)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            import json
            permissions_json = json.dumps(user.permissions)
            
            self.db.execute_query(query, (
                user.username,
                user.email,
                user.password_hash,
                user.full_name,
                user.role,
                user.is_active,
                permissions_json
            ))
            
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء المستخدم: {e}")
            return False
    
    def authenticate(self, username: str, password: str) -> Optional[User]:
        """تسجيل دخول المستخدم"""
        try:
            query = "SELECT * FROM users WHERE username = ? AND is_active = 1"
            result = self.db.fetch_one(query, (username,))
            
            if result:
                user = self._row_to_user(result)
                if user.check_password(password):
                    # تحديث آخر تسجيل دخول
                    update_query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?"
                    self.db.execute_query(update_query, (user.id,))
                    
                    self.current_user = user
                    return user
            
            return None
            
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            return None
    
    def logout(self):
        """تسجيل خروج المستخدم"""
        self.current_user = None
        self.current_session = None
    
    def get_all_users(self) -> List[User]:
        """الحصول على جميع المستخدمين"""
        try:
            query = "SELECT * FROM users ORDER BY created_at DESC"
            results = self.db.fetch_all(query)
            
            return [self._row_to_user(row) for row in results]
            
        except Exception as e:
            print(f"خطأ في جلب المستخدمين: {e}")
            return []
    
    def _row_to_user(self, row) -> User:
        """تحويل صف قاعدة البيانات إلى كائن مستخدم"""
        import json
        
        permissions = []
        try:
            if row[10]:  # permissions column
                permissions = json.loads(row[10])
        except:
            permissions = []
        
        return User(
            id=row[0],
            username=row[1],
            email=row[2],
            password_hash=row[3],
            full_name=row[4],
            role=row[5],
            is_active=bool(row[6]),
            created_at=datetime.fromisoformat(row[7]) if row[7] else None,
            updated_at=datetime.fromisoformat(row[8]) if row[8] else None,
            last_login=datetime.fromisoformat(row[9]) if row[9] else None,
            permissions=permissions
        )
