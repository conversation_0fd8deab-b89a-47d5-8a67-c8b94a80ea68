# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
Application Configuration
"""

import json
import os
from typing import Dict, Any

class Config:
    """فئة إدارة إعدادات التطبيق"""
    
    def __init__(self, config_file: str = "data/config.json"):
        """
        تهيئة إعدادات التطبيق
        
        Args:
            config_file: مسار ملف الإعدادات
        """
        self.config_file = config_file
        self.settings = self._load_default_settings()
        
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        
        # تحميل الإعدادات من الملف
        self.load_settings()
    
    def _load_default_settings(self) -> Dict[str, Any]:
        """تحميل الإعدادات الافتراضية"""
        return {
            "app": {
                "name": "الميزان",
                "version": "1.0.0",
                "language": "ar",
                "theme": "light",
                "font_family": "Arial",
                "font_size": 10
            },
            "business": {
                "name": "محل تجاري",
                "address": "",
                "phone": "",
                "email": "",
                "tax_number": "",
                "logo_path": ""
            },
            "invoice": {
                "auto_generate_number": True,
                "number_prefix": "INV",
                "number_length": 6,
                "default_tax_rate": 14.0,
                "default_payment_terms": 30,
                "print_after_save": False
            },
            "inventory": {
                "auto_update_cost": True,
                "low_stock_alert": True,
                "negative_stock_allowed": False,
                "default_unit": "قطعة"
            },
            "financial": {
                "default_currency": "EGP",
                "decimal_places": 2,
                "show_currency_symbol": True
            },
            "backup": {
                "auto_backup": True,
                "backup_interval": 24,  # ساعات
                "backup_location": "data/backups",
                "max_backup_files": 10
            },
            "security": {
                "session_timeout": 480,  # دقائق
                "password_min_length": 6,
                "require_login": True
            },
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "show_toolbar": True,
                "show_statusbar": True,
                "confirm_delete": True
            }
        }
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_settings = json.load(f)
                    # دمج الإعدادات مع الافتراضية
                    self._merge_settings(self.settings, file_settings)
                print("تم تحميل الإعدادات من الملف")
            else:
                # حفظ الإعدادات الافتراضية
                self.save_settings()
                print("تم إنشاء ملف الإعدادات الافتراضي")
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def save_settings(self):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=4)
            print("تم حفظ الإعدادات")
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
    
    def get(self, key: str, default=None):
        """
        الحصول على قيمة إعداد
        
        Args:
            key: مفتاح الإعداد (مثل: app.name)
            default: القيمة الافتراضية
            
        Returns:
            قيمة الإعداد
        """
        keys = key.split('.')
        value = self.settings
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        تعيين قيمة إعداد
        
        Args:
            key: مفتاح الإعداد
            value: القيمة الجديدة
        """
        keys = key.split('.')
        setting = self.settings
        
        # الوصول إلى المستوى الأخير
        for k in keys[:-1]:
            if k not in setting:
                setting[k] = {}
            setting = setting[k]
        
        # تعيين القيمة
        setting[keys[-1]] = value
    
    def _merge_settings(self, default: Dict, file_settings: Dict):
        """دمج إعدادات الملف مع الافتراضية"""
        for key, value in file_settings.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                self._merge_settings(default[key], value)
            else:
                default[key] = value
    
    def reset_to_default(self):
        """إعادة تعيين الإعدادات للافتراضية"""
        self.settings = self._load_default_settings()
        self.save_settings()
    
    def get_business_info(self) -> Dict[str, str]:
        """الحصول على معلومات المحل التجاري"""
        return self.settings.get("business", {})
    
    def get_invoice_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات الفواتير"""
        return self.settings.get("invoice", {})
    
    def get_ui_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات الواجهة"""
        return self.settings.get("ui", {})
