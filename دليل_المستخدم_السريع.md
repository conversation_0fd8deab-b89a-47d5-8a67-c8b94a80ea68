# دليل المستخدم السريع - برنامج الميزان

## البدء السريع

### 1. تشغيل البرنامج
- انقر نقراً مزدوجاً على ملف `run.bat`
- أو افتح موجه الأوامر واكتب: `python main.py`

### 2. تسجيل الدخول الأولي
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## الوظائف الأساسية

### إدارة المنتجات

#### إضافة منتج جديد:
1. اذهب إلى **المخزون** → **المنتجات**
2. اضغط **إضافة منتج**
3. املأ البيانات:
   - **الكود:** كود فريد للمنتج
   - **الاسم:** اسم المنتج
   - **الفئة:** اختر الفئة المناسبة
   - **سعر التكلفة:** سعر الشراء
   - **سعر البيع:** سعر البيع للعميل
   - **الكمية:** الكمية الحالية في المخزون
   - **الحد الأدنى:** الحد الأدنى للتنبيه
4. اضغط **حفظ**

#### البحث عن منتج:
- استخدم مربع البحث في أعلى نافذة المنتجات
- يمكن البحث بالكود أو الاسم

### إدارة العملاء

#### إضافة عميل جديد:
1. اذهب إلى **العملاء** → **العملاء**
2. اضغط **إضافة عميل**
3. املأ البيانات:
   - **الكود:** كود فريد للعميل
   - **الاسم:** اسم العميل
   - **الهاتف:** رقم الهاتف
   - **العنوان:** عنوان العميل
   - **حد الائتمان:** الحد الأقصى للدين
4. اضغط **حفظ**

### إنشاء فاتورة بيع

#### خطوات إنشاء الفاتورة:
1. اذهب إلى **المبيعات** → **فاتورة بيع جديدة**
2. **اختر العميل** من القائمة المنسدلة
3. **أضف المنتجات:**
   - اختر المنتج من القائمة
   - أدخل الكمية
   - تأكد من السعر
   - اضغط **إضافة**
4. **راجع الإجماليات:**
   - المجموع الفرعي
   - الخصم (إن وجد)
   - الضريبة
   - المجموع النهائي
5. **اختر طريقة الدفع:**
   - نقدي
   - آجل
   - بنكي
6. اضغط **حفظ**

### عرض التقارير

#### تقرير المبيعات:
1. اذهب إلى **التقارير**
2. اختر **sales** من نوع التقرير
3. حدد **التاريخ من** و **التاريخ إلى**
4. اضغط **إنشاء التقرير**

#### تقرير المخزون:
1. اذهب إلى **التقارير**
2. اختر **inventory** من نوع التقرير
3. اضغط **إنشاء التقرير**

## نصائح مهمة

### 🔒 الأمان
- غيّر كلمة مرور المدير فور التشغيل الأول
- أنشئ نسخ احتياطية دورية من البيانات

### 📊 إدارة المخزون
- راقب تنبيهات المخزون المنخفض في لوحة التحكم
- حدّث أسعار المنتجات بانتظام
- راجع تقارير المخزون شهرياً

### 💰 إدارة المبيعات
- راجع الفواتير غير المدفوعة بانتظام
- تابع حدود الائتمان للعملاء
- استخدم التقارير لتحليل الأداء

### 🔧 الصيانة
- أعد تشغيل البرنامج يومياً لضمان الأداء الأمثل
- احتفظ بنسخ احتياطية في مكان آمن
- تحقق من تحديثات البرنامج دورياً

## حل المشاكل الشائعة

### البرنامج لا يبدأ:
- تأكد من تثبيت Python 3.8 أو أحدث
- تحقق من وجود جميع الملفات في المجلد
- شغّل البرنامج كمدير (Run as Administrator)

### خطأ في قاعدة البيانات:
- تأكد من صلاحيات الكتابة في مجلد البرنامج
- أعد تشغيل البرنامج
- استعد نسخة احتياطية إذا لزم الأمر

### الواجهة تظهر بشكل غريب:
- تأكد من دعم النظام للخطوط العربية
- أعد تشغيل البرنامج
- تحقق من إعدادات العرض في النظام

## اختصارات لوحة المفاتيح

- **Ctrl + N:** فاتورة جديدة
- **Ctrl + S:** حفظ
- **Ctrl + P:** طباعة
- **F5:** تحديث البيانات
- **Escape:** إلغاء/إغلاق

## الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف README.md للتفاصيل التقنية
3. تواصل مع فريق الدعم

---

**نتمنى لك تجربة ممتعة مع برنامج الميزان! 🏪✨**
