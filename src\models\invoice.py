# -*- coding: utf-8 -*-
"""
نموذج الفاتورة
Invoice Model
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, field

@dataclass
class InvoiceItem:
    """عنصر في الفاتورة"""
    
    id: Optional[int] = None
    invoice_id: Optional[int] = None
    product_id: int = 0
    product_code: str = ""
    product_name: str = ""
    quantity: float = 0.0
    unit_price: float = 0.0
    discount_amount: float = 0.0
    total_price: float = 0.0
    
    def __post_init__(self):
        """حساب السعر الإجمالي"""
        self.calculate_total()
    
    def calculate_total(self):
        """حساب السعر الإجمالي للعنصر"""
        self.total_price = (self.quantity * self.unit_price) - self.discount_amount
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'product_id': self.product_id,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'discount_amount': self.discount_amount,
            'total_price': self.total_price
        }

@dataclass
class Invoice:
    """نموذج بيانات الفاتورة"""
    
    id: Optional[int] = None
    invoice_number: str = ""
    invoice_type: str = "sale"  # 'sale' or 'purchase'
    customer_id: Optional[int] = None
    supplier_id: Optional[int] = None
    customer_name: str = ""
    supplier_name: str = ""
    user_id: int = 1
    invoice_date: date = field(default_factory=date.today)
    due_date: Optional[date] = None
    subtotal: float = 0.0
    discount_amount: float = 0.0
    discount_percentage: float = 0.0
    tax_amount: float = 0.0
    tax_percentage: float = 14.0
    total_amount: float = 0.0
    paid_amount: float = 0.0
    remaining_amount: float = 0.0
    payment_status: str = "pending"  # 'pending', 'partial', 'paid'
    payment_method: str = "cash"  # 'cash', 'credit', 'bank'
    notes: str = ""
    is_cancelled: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    items: List[InvoiceItem] = field(default_factory=list)
    
    def __post_init__(self):
        """معالجة ما بعد التهيئة"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        
        # حساب الإجماليات
        self.calculate_totals()
    
    def add_item(self, item: InvoiceItem):
        """إضافة عنصر للفاتورة"""
        self.items.append(item)
        self.calculate_totals()
    
    def remove_item(self, index: int):
        """حذف عنصر من الفاتورة"""
        if 0 <= index < len(self.items):
            del self.items[index]
            self.calculate_totals()
    
    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        # حساب المجموع الفرعي
        self.subtotal = sum(item.total_price for item in self.items)
        
        # حساب الخصم
        if self.discount_percentage > 0:
            self.discount_amount = self.subtotal * (self.discount_percentage / 100)
        
        # المبلغ بعد الخصم
        amount_after_discount = self.subtotal - self.discount_amount
        
        # حساب الضريبة
        if self.tax_percentage > 0:
            self.tax_amount = amount_after_discount * (self.tax_percentage / 100)
        
        # المجموع النهائي
        self.total_amount = amount_after_discount + self.tax_amount
        
        # الرصيد المتبقي
        self.remaining_amount = self.total_amount - self.paid_amount
        
        # تحديث حالة الدفع
        self.update_payment_status()
    
    def update_payment_status(self):
        """تحديث حالة الدفع"""
        if self.paid_amount <= 0:
            self.payment_status = "pending"
        elif self.paid_amount >= self.total_amount:
            self.payment_status = "paid"
        else:
            self.payment_status = "partial"
    
    def add_payment(self, amount: float):
        """إضافة دفعة"""
        self.paid_amount += amount
        self.calculate_totals()
    
    @property
    def is_paid(self) -> bool:
        """هل الفاتورة مدفوعة بالكامل"""
        return self.payment_status == "paid"
    
    @property
    def payment_status_text(self) -> str:
        """نص حالة الدفع"""
        status_map = {
            "pending": "غير مدفوعة",
            "partial": "مدفوعة جزئياً",
            "paid": "مدفوعة بالكامل"
        }
        return status_map.get(self.payment_status, "غير محدد")
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'invoice_type': self.invoice_type,
            'customer_id': self.customer_id,
            'supplier_id': self.supplier_id,
            'user_id': self.user_id,
            'invoice_date': self.invoice_date.isoformat() if self.invoice_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'subtotal': self.subtotal,
            'discount_amount': self.discount_amount,
            'discount_percentage': self.discount_percentage,
            'tax_amount': self.tax_amount,
            'tax_percentage': self.tax_percentage,
            'total_amount': self.total_amount,
            'paid_amount': self.paid_amount,
            'remaining_amount': self.remaining_amount,
            'payment_status': self.payment_status,
            'payment_method': self.payment_method,
            'notes': self.notes,
            'is_cancelled': self.is_cancelled,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Invoice':
        """إنشاء من قاموس"""
        invoice = cls()
        for key, value in data.items():
            if hasattr(invoice, key):
                if key in ['invoice_date', 'due_date'] and value:
                    if isinstance(value, str):
                        setattr(invoice, key, date.fromisoformat(value))
                    else:
                        setattr(invoice, key, value)
                elif key in ['created_at', 'updated_at'] and value:
                    if isinstance(value, str):
                        setattr(invoice, key, datetime.fromisoformat(value))
                    else:
                        setattr(invoice, key, value)
                else:
                    setattr(invoice, key, value)
        return invoice
    
    def validate(self) -> List[str]:
        """التحقق من صحة البيانات"""
        errors = []
        
        if not self.invoice_number.strip():
            errors.append("رقم الفاتورة مطلوب")
        
        if self.invoice_type not in ['sale', 'purchase']:
            errors.append("نوع الفاتورة غير صحيح")
        
        if self.invoice_type == 'sale' and not self.customer_id:
            errors.append("العميل مطلوب لفاتورة البيع")
        
        if self.invoice_type == 'purchase' and not self.supplier_id:
            errors.append("المورد مطلوب لفاتورة الشراء")
        
        if not self.items:
            errors.append("يجب إضافة عنصر واحد على الأقل للفاتورة")
        
        if self.discount_percentage < 0 or self.discount_percentage > 100:
            errors.append("نسبة الخصم يجب أن تكون بين 0 و 100")
        
        if self.tax_percentage < 0 or self.tax_percentage > 100:
            errors.append("نسبة الضريبة يجب أن تكون بين 0 و 100")
        
        return errors

class InvoiceManager:
    """مدير الفواتير"""
    
    def __init__(self, db_manager):
        """
        تهيئة مدير الفواتير
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def generate_invoice_number(self, invoice_type: str = "sale") -> str:
        """
        توليد رقم فاتورة جديد
        
        Args:
            invoice_type: نوع الفاتورة
            
        Returns:
            رقم الفاتورة الجديد
        """
        prefix = "INV-S" if invoice_type == "sale" else "INV-P"
        
        # البحث عن آخر رقم فاتورة
        query = """
            SELECT invoice_number FROM invoices 
            WHERE invoice_type = ? AND invoice_number LIKE ?
            ORDER BY id DESC LIMIT 1
        """
        
        result = self.db.fetch_one(query, (invoice_type, f"{prefix}%"))
        
        if result:
            last_number = result['invoice_number']
            # استخراج الرقم
            try:
                number_part = int(last_number.split('-')[-1])
                new_number = number_part + 1
            except:
                new_number = 1
        else:
            new_number = 1
        
        return f"{prefix}-{new_number:06d}"
    
    def create_invoice(self, invoice: Invoice) -> Optional[int]:
        """
        إنشاء فاتورة جديدة
        
        Args:
            invoice: بيانات الفاتورة
            
        Returns:
            معرف الفاتورة الجديدة أو None في حالة الفشل
        """
        try:
            # التحقق من صحة البيانات
            errors = invoice.validate()
            if errors:
                raise ValueError(f"أخطاء في البيانات: {', '.join(errors)}")
            
            # توليد رقم الفاتورة إذا لم يكن موجوداً
            if not invoice.invoice_number:
                invoice.invoice_number = self.generate_invoice_number(invoice.invoice_type)
            
            # إدراج الفاتورة
            query = """
                INSERT INTO invoices (
                    invoice_number, invoice_type, customer_id, supplier_id, user_id,
                    invoice_date, due_date, subtotal, discount_amount, discount_percentage,
                    tax_amount, tax_percentage, total_amount, paid_amount, remaining_amount,
                    payment_status, payment_method, notes, is_cancelled
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor = self.db.execute_query(query, (
                invoice.invoice_number, invoice.invoice_type, invoice.customer_id,
                invoice.supplier_id, invoice.user_id, invoice.invoice_date,
                invoice.due_date, invoice.subtotal, invoice.discount_amount,
                invoice.discount_percentage, invoice.tax_amount, invoice.tax_percentage,
                invoice.total_amount, invoice.paid_amount, invoice.remaining_amount,
                invoice.payment_status, invoice.payment_method, invoice.notes,
                invoice.is_cancelled
            ))
            
            invoice_id = cursor.lastrowid
            
            # إدراج عناصر الفاتورة
            for item in invoice.items:
                item.invoice_id = invoice_id
                self.create_invoice_item(item)
            
            return invoice_id
            
        except Exception as e:
            print(f"خطأ في إنشاء الفاتورة: {e}")
            raise
    
    def create_invoice_item(self, item: InvoiceItem) -> Optional[int]:
        """
        إنشاء عنصر فاتورة
        
        Args:
            item: بيانات العنصر
            
        Returns:
            معرف العنصر الجديد
        """
        query = """
            INSERT INTO invoice_items (
                invoice_id, product_id, quantity, unit_price, discount_amount, total_price
            ) VALUES (?, ?, ?, ?, ?, ?)
        """
        
        cursor = self.db.execute_query(query, (
            item.invoice_id, item.product_id, item.quantity,
            item.unit_price, item.discount_amount, item.total_price
        ))
        
        return cursor.lastrowid
    
    def get_invoice_by_id(self, invoice_id: int) -> Optional[Invoice]:
        """
        الحصول على فاتورة بالمعرف
        
        Args:
            invoice_id: معرف الفاتورة
            
        Returns:
            الفاتورة أو None
        """
        query = """
            SELECT i.*, c.name as customer_name, s.name as supplier_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            LEFT JOIN suppliers s ON i.supplier_id = s.id
            WHERE i.id = ?
        """
        
        row = self.db.fetch_one(query, (invoice_id,))
        if row:
            invoice = Invoice.from_dict(dict(row))
            
            # تحميل عناصر الفاتورة
            items_query = """
                SELECT ii.*, p.code as product_code, p.name as product_name
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
            """
            
            items_rows = self.db.fetch_all(items_query, (invoice_id,))
            invoice.items = []
            
            for item_row in items_rows:
                item = InvoiceItem(
                    id=item_row['id'],
                    invoice_id=item_row['invoice_id'],
                    product_id=item_row['product_id'],
                    product_code=item_row['product_code'],
                    product_name=item_row['product_name'],
                    quantity=item_row['quantity'],
                    unit_price=item_row['unit_price'],
                    discount_amount=item_row['discount_amount'],
                    total_price=item_row['total_price']
                )
                invoice.items.append(item)
            
            return invoice
        
        return None
    
    def get_invoices(self, invoice_type: str = None, limit: int = 100) -> List[Invoice]:
        """
        الحصول على قائمة الفواتير
        
        Args:
            invoice_type: نوع الفاتورة (None للكل)
            limit: عدد الفواتير المطلوبة
            
        Returns:
            قائمة الفواتير
        """
        query = """
            SELECT i.*, c.name as customer_name, s.name as supplier_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            LEFT JOIN suppliers s ON i.supplier_id = s.id
        """
        
        params = []
        if invoice_type:
            query += " WHERE i.invoice_type = ?"
            params.append(invoice_type)
        
        query += " ORDER BY i.created_at DESC LIMIT ?"
        params.append(limit)
        
        rows = self.db.fetch_all(query, tuple(params))
        return [Invoice.from_dict(dict(row)) for row in rows]
