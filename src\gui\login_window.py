# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable

from ..utils.colors import AppColors
from ..models.user import UserManager, User

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, db_manager, on_login_success: Callable[[User], None]):
        """تهيئة نافذة تسجيل الدخول"""
        self.db = db_manager
        self.on_login_success = on_login_success
        self.user_manager = UserManager(db_manager)
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Tk()
        self.window.title("🔐 تسجيل الدخول - الميزان")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.configure(bg=self.colors['background'])
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد أيقونة النافذة
        try:
            self.window.iconbitmap("assets/icon.ico")
        except:
            pass
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)
        
        # شعار التطبيق
        self.create_logo_section(main_container)
        
        # نموذج تسجيل الدخول
        self.create_login_form(main_container)
        
        # معلومات إضافية
        self.create_info_section(main_container)
    
    def create_logo_section(self, parent):
        """إنشاء قسم الشعار"""
        logo_frame = tk.Frame(parent, bg=self.colors['background'])
        logo_frame.pack(fill=tk.X, pady=(0, 30))
        
        # شعار التطبيق
        logo_label = tk.Label(logo_frame,
                             text="⚖️",
                             font=('Segoe UI', 48),
                             fg=self.colors['primary'],
                             bg=self.colors['background'])
        logo_label.pack()
        
        # اسم التطبيق
        title_label = tk.Label(logo_frame,
                              text="الميزان",
                              font=('Segoe UI', 24, 'bold'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'])
        title_label.pack()
        
        # وصف التطبيق
        subtitle_label = tk.Label(logo_frame,
                                 text="نظام إدارة الأعمال التجارية",
                                 font=('Segoe UI', 12),
                                 fg=self.colors['text_secondary'],
                                 bg=self.colors['background'])
        subtitle_label.pack()
    
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = tk.Frame(parent, bg=self.colors['card'], relief='flat', bd=1)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # عنوان النموذج
        title_frame = tk.Frame(form_frame, bg=self.colors['primary'])
        title_frame.pack(fill=tk.X)
        
        title_label = tk.Label(title_frame,
                              text="🔐 تسجيل الدخول",
                              font=('Segoe UI', 14, 'bold'),
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(pady=10)
        
        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg=self.colors['card'])
        content_frame.pack(fill=tk.X, padx=30, pady=20)
        
        # حقل اسم المستخدم
        username_frame = tk.Frame(content_frame, bg=self.colors['card'])
        username_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(username_frame,
                text="👤 اسم المستخدم:",
                font=('Segoe UI', 11),
                fg=self.colors['text'],
                bg=self.colors['card']).pack(anchor='e')
        
        username_entry = tk.Entry(username_frame,
                                 textvariable=self.username_var,
                                 font=('Segoe UI', 12),
                                 relief='flat',
                                 bd=5)
        username_entry.pack(fill=tk.X, pady=(5, 0))
        username_entry.focus()
        
        # حقل كلمة المرور
        password_frame = tk.Frame(content_frame, bg=self.colors['card'])
        password_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(password_frame,
                text="🔒 كلمة المرور:",
                font=('Segoe UI', 11),
                fg=self.colors['text'],
                bg=self.colors['card']).pack(anchor='e')
        
        password_entry = tk.Entry(password_frame,
                                 textvariable=self.password_var,
                                 font=('Segoe UI', 12),
                                 show='*',
                                 relief='flat',
                                 bd=5)
        password_entry.pack(fill=tk.X, pady=(5, 0))
        
        # ربط Enter بتسجيل الدخول
        password_entry.bind('<Return>', lambda e: self.login())
        username_entry.bind('<Return>', lambda e: password_entry.focus())
        
        # خيار تذكر المستخدم
        remember_frame = tk.Frame(content_frame, bg=self.colors['card'])
        remember_frame.pack(fill=tk.X, pady=(0, 20))
        
        remember_check = tk.Checkbutton(remember_frame,
                                       text="تذكرني",
                                       variable=self.remember_var,
                                       font=('Segoe UI', 10),
                                       fg=self.colors['text'],
                                       bg=self.colors['card'],
                                       selectcolor=self.colors['card'])
        remember_check.pack(side=tk.RIGHT)
        
        # زر تسجيل الدخول
        login_btn = tk.Button(content_frame,
                             text="🚀 تسجيل الدخول",
                             font=('Segoe UI', 12, 'bold'),
                             fg='white',
                             bg=self.colors['primary'],
                             relief='flat',
                             padx=30,
                             pady=10,
                             command=self.login)
        login_btn.pack(fill=tk.X)
    
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg=self.colors['background'])
        info_frame.pack(fill=tk.X)
        
        # معلومات المستخدم الافتراضي
        info_text = """
        💡 معلومات تسجيل الدخول الافتراضية:
        اسم المستخدم: admin
        كلمة المرور: admin123
        """
        
        info_label = tk.Label(info_frame,
                             text=info_text,
                             font=('Segoe UI', 9),
                             fg=self.colors['text_secondary'],
                             bg=self.colors['background'],
                             justify=tk.CENTER)
        info_label.pack()
        
        # رابط نسيان كلمة المرور
        forgot_btn = tk.Button(info_frame,
                              text="نسيت كلمة المرور؟",
                              font=('Segoe UI', 9, 'underline'),
                              fg=self.colors['primary'],
                              bg=self.colors['background'],
                              relief='flat',
                              bd=0,
                              command=self.forgot_password)
        forgot_btn.pack(pady=(10, 0))
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        # التحقق من البيانات
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            return
        
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            return
        
        # محاولة تسجيل الدخول
        user = self.user_manager.authenticate(username, password)
        
        if user:
            # نجح تسجيل الدخول
            messagebox.showinfo("نجح", f"مرحباً {user.full_name}")
            
            # إخفاء نافذة تسجيل الدخول
            self.window.withdraw()
            
            # استدعاء دالة النجاح
            self.on_login_success(user)
            
            # إغلاق نافذة تسجيل الدخول
            self.window.destroy()
        else:
            # فشل تسجيل الدخول
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_var.set("")
    
    def forgot_password(self):
        """نسيان كلمة المرور"""
        messagebox.showinfo("نسيان كلمة المرور", 
                           "يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()
    
    def destroy(self):
        """إغلاق النافذة"""
        self.window.destroy()
