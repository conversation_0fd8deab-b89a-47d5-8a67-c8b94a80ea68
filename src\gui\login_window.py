# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable

from ..utils.colors import AppColors
from ..models.user import UserManager, User

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, db_manager, on_login_success: Callable[[User], None]):
        """تهيئة نافذة تسجيل الدخول"""
        self.db = db_manager
        self.on_login_success = on_login_success
        self.user_manager = UserManager(db_manager)
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()

        # تأثير الظهور التدريجي
        self.fade_in_effect()

    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()

        # متغيرات السحب
        self.x = 0
        self.y = 0

    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Tk()
        self.window.title("الميزان - تسجيل الدخول")
        self.window.geometry("480x600")
        self.window.resizable(False, False)

        # تعيين لون الخلفية
        self.window.configure(bg='#ecf0f1')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.attributes('-topmost', True)
        self.window.lift()
        self.window.focus_force()

        # إضافة شفافية للنافذة (Windows only)
        try:
            self.window.attributes('-alpha', 0.98)
        except:
            pass
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        # تحديث النافذة للحصول على الأبعاد الصحيحة
        self.window.update_idletasks()

        # الحصول على أبعاد النافذة
        window_width = 480
        window_height = 600

        # الحصول على أبعاد الشاشة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()

        # حساب الموضع المتوسط
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        if x < 0:
            x = 0
        if y < 0:
            y = 0

        # تطبيق الموضع
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء الحاوي الرئيسي
        main_container = tk.Frame(self.window, bg='#ecf0f1')
        main_container.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # العنوان والشعار
        title_frame = tk.Frame(main_container, bg='#ecf0f1')
        title_frame.pack(fill=tk.X, pady=(0, 30))

        # شعار التطبيق
        logo_label = tk.Label(title_frame,
                             text="⚖️",
                             font=('Segoe UI', 48),
                             bg='#ecf0f1',
                             fg='#2c3e50')
        logo_label.pack()

        # اسم التطبيق
        app_name = tk.Label(title_frame,
                           text="الميزان",
                           font=('Segoe UI', 24, 'bold'),
                           bg='#ecf0f1',
                           fg='#2c3e50')
        app_name.pack(pady=(10, 0))

        # وصف التطبيق
        app_desc = tk.Label(title_frame,
                           text="نظام إدارة المحال التجارية",
                           font=('Segoe UI', 12),
                           bg='#ecf0f1',
                           fg='#7f8c8d')
        app_desc.pack(pady=(5, 0))

        # نموذج تسجيل الدخول
        self.create_simple_login_form(main_container)

        # معلومات افتراضية
        self.create_simple_info_section(main_container)

        # زر الإغلاق
        close_btn = tk.Button(main_container,
                             text="✕",
                             font=('Segoe UI', 12, 'bold'),
                             bg='#e74c3c',
                             fg='white',
                             relief='flat',
                             width=3,
                             command=self.close_window)
        close_btn.place(relx=1.0, rely=0.0, anchor='ne')

    def create_simple_login_form(self, parent):
        """إنشاء نموذج تسجيل دخول مبسط"""
        form_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        form_frame.pack(fill=tk.X, pady=20, padx=20)

        # عنوان النموذج
        form_title = tk.Label(form_frame,
                             text="🔐 تسجيل الدخول",
                             font=('Segoe UI', 16, 'bold'),
                             bg='white',
                             fg='#2c3e50')
        form_title.pack(pady=(20, 15))

        # اسم المستخدم
        username_label = tk.Label(form_frame,
                                 text="👤 اسم المستخدم:",
                                 font=('Segoe UI', 11),
                                 bg='white',
                                 fg='#34495e')
        username_label.pack(anchor='w', padx=20, pady=(10, 5))

        self.username_entry = tk.Entry(form_frame,
                                      textvariable=self.username_var,
                                      font=('Segoe UI', 12),
                                      relief='solid',
                                      bd=1,
                                      width=25)
        self.username_entry.pack(padx=20, pady=(0, 15), ipady=8)

        # كلمة المرور
        password_label = tk.Label(form_frame,
                                 text="🔒 كلمة المرور:",
                                 font=('Segoe UI', 11),
                                 bg='white',
                                 fg='#34495e')
        password_label.pack(anchor='w', padx=20, pady=(0, 5))

        self.password_entry = tk.Entry(form_frame,
                                      textvariable=self.password_var,
                                      font=('Segoe UI', 12),
                                      show='*',
                                      relief='solid',
                                      bd=1,
                                      width=25)
        self.password_entry.pack(padx=20, pady=(0, 20), ipady=8)

        # زر تسجيل الدخول
        self.login_btn = tk.Button(form_frame,
                                  text="🚀 تسجيل الدخول",
                                  font=('Segoe UI', 12, 'bold'),
                                  bg='#27ae60',
                                  fg='white',
                                  relief='flat',
                                  width=20,
                                  pady=10,
                                  state='disabled',
                                  command=self.login)
        self.login_btn.pack(pady=(0, 25))

        # ربط الأحداث
        self.username_var.trace('w', self.check_login_button_state)
        self.password_var.trace('w', self.check_login_button_state)
        self.window.bind('<Return>', lambda e: self.login() if self.login_btn['state'] == 'normal' else None)

    def create_simple_info_section(self, parent):
        """إنشاء قسم معلومات مبسط"""
        info_frame = tk.Frame(parent, bg='#e8f4fd', relief='solid', bd=1)
        info_frame.pack(fill=tk.X, pady=10, padx=20)

        info_title = tk.Label(info_frame,
                             text="💡 بيانات تسجيل الدخول الافتراضية:",
                             font=('Segoe UI', 11, 'bold'),
                             bg='#e8f4fd',
                             fg='#2c3e50')
        info_title.pack(pady=(15, 5))

        username_info = tk.Label(info_frame,
                                text="اسم المستخدم: admin",
                                font=('Segoe UI', 10),
                                bg='#e8f4fd',
                                fg='#34495e')
        username_info.pack()

        password_info = tk.Label(info_frame,
                                text="كلمة المرور: admin123",
                                font=('Segoe UI', 10),
                                bg='#e8f4fd',
                                fg='#34495e')
        password_info.pack(pady=(0, 15))

    def check_login_button_state(self, *args):
        """فحص حالة زر تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if username and password:
            self.login_btn.config(state='normal', bg='#27ae60', cursor='hand2')
        else:
            self.login_btn.config(state='disabled', bg='#95a5a6', cursor='arrow')

    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()

    def login(self):
        """تسجيل الدخول"""
        if self.login_btn['state'] != 'normal':
            return

        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if not username or not password:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تغيير حالة الزر
        self.login_btn.config(text="⏳ جاري تسجيل الدخول...", state='disabled', bg='#f39c12')
        self.window.update()

        try:
            user = self.user_manager.authenticate(username, password)
            if user:
                self.login_btn.config(text="✅ تم بنجاح!", bg='#27ae60')
                self.window.update()
                self.window.after(500, lambda: self.on_login_success(user))
            else:
                self.login_btn.config(text="❌ فشل تسجيل الدخول", bg='#e74c3c')
                self.window.update()
                self.window.after(1500, self._reset_login_button)
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            self.login_btn.config(text="❌ خطأ", bg='#e74c3c')
            self.window.update()
            self.window.after(1500, self._reset_login_button)
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {str(e)}")

    def _reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.login_btn.config(text="🚀 تسجيل الدخول", state='normal', bg='#27ae60')

    def fade_in_effect(self):
        """تأثير الظهور التدريجي"""
        try:
            self.window.attributes('-alpha', 0.0)
            self._fade_in_step(0.0)
        except:
            pass

    def _fade_in_step(self, alpha):
        """خطوة في تأثير الظهور"""
        try:
            alpha += 0.1
            if alpha >= 1.0:
                alpha = 1.0
            self.window.attributes('-alpha', alpha)
            if alpha < 1.0:
                self.window.after(30, lambda: self._fade_in_step(alpha))
        except:
            pass

    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

    def show(self):
        """عرض النافذة"""
        self.window.deiconify()
        self.window.lift()
        self.window.focus_force()

    def hide(self):
        """إخفاء النافذة"""
        self.window.withdraw()

    # الدوال القديمة (محفوظة للتوافق)
    def create_title_bar(self, parent):
        """إنشاء شريط العنوان مع زر الإغلاق"""
        title_bar = tk.Frame(parent, bg='white', height=50)
        title_bar.pack(fill=tk.X)
        title_bar.pack_propagate(False)

        # زر الإغلاق
        close_btn = tk.Button(title_bar,
                             text="✕",
                             font=('Segoe UI', 12, 'bold'),
                             fg='#666666',
                             bg='white',
                             relief='flat',
                             bd=0,
                             width=3,
                             command=self.close_window)
        close_btn.pack(side=tk.RIGHT, padx=10, pady=10)

        # تأثيرات التفاعل لزر الإغلاق
        close_btn.bind('<Enter>', lambda e: close_btn.config(bg='#f44336', fg='white'))
        close_btn.bind('<Leave>', lambda e: close_btn.config(bg='white', fg='#666666'))

        # ربط السحب لتحريك النافذة
        title_bar.bind('<Button-1>', self.start_move)
        title_bar.bind('<B1-Motion>', self.on_move)

    def create_logo_section(self, parent):
        """إنشاء قسم الشعار"""
        logo_frame = tk.Frame(parent, bg='#ecf0f1')
        logo_frame.pack(fill=tk.X, pady=(0, 30))

        # شعار التطبيق
        logo_label = tk.Label(logo_frame,
                             text="⚖️",
                             font=('Segoe UI', 60),
                             fg='#2196F3',
                             bg='white')
        logo_label.pack(pady=(0, 15))

        # اسم التطبيق مع تصميم حديث
        title_label = tk.Label(logo_frame,
                              text="Al Mizan",
                              font=('Segoe UI', 24, 'bold'),
                              fg='#1976D2',
                              bg='white')
        title_label.pack(pady=(0, 30))
        separator.pack(fill=tk.X, padx=100, pady=(20, 0))
    
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = tk.Frame(parent, bg='white')
        form_frame.pack(fill=tk.X, padx=50, pady=(0, 20))

        # حقل اسم المستخدم
        username_frame = tk.Frame(form_frame, bg='#f5f5f5', relief='solid', bd=1)
        username_frame.pack(fill=tk.X, pady=(0, 15))

        username_icon = tk.Label(username_frame,
                                text="👤",
                                font=('Segoe UI', 14),
                                fg='#666666',
                                bg='#f5f5f5')
        username_icon.pack(side=tk.LEFT, padx=(15, 5), pady=12)

        self.username_entry = tk.Entry(username_frame,
                                      textvariable=self.username_var,
                                      font=('Segoe UI', 12),
                                      relief='flat',
                                      bd=0,
                                      bg='#f5f5f5',
                                      fg='#333333',
                                      insertbackground='#333333')
        self.username_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15), pady=12)
        self.username_entry.insert(0, "Username")
        self.username_entry.config(fg='#999999')

        # حقل كلمة المرور
        password_frame = tk.Frame(form_frame, bg='#f5f5f5', relief='solid', bd=1)
        password_frame.pack(fill=tk.X, pady=(0, 15))

        password_icon = tk.Label(password_frame,
                                text="🔒",
                                font=('Segoe UI', 14),
                                fg='#666666',
                                bg='#f5f5f5')
        password_icon.pack(side=tk.LEFT, padx=(15, 5), pady=12)

        self.password_entry = tk.Entry(password_frame,
                                      textvariable=self.password_var,
                                      font=('Segoe UI', 12),
                                      show='●',
                                      relief='flat',
                                      bd=0,
                                      bg='#f5f5f5',
                                      fg='#333333',
                                      insertbackground='#333333')
        self.password_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 40), pady=12)
        self.password_entry.insert(0, "Password")
        self.password_entry.config(fg='#999999', show='')

        # أيقونة القفل في كلمة المرور
        lock_icon = tk.Label(password_frame,
                            text="🔒",
                            font=('Segoe UI', 12),
                            fg='#2196F3',
                            bg='#f5f5f5')
        lock_icon.pack(side=tk.RIGHT, padx=(0, 15), pady=12)

        # خانة "تذكرني"
        remember_frame = tk.Frame(form_frame, bg='white')
        remember_frame.pack(fill=tk.X, pady=(0, 20))

        self.remember_var = tk.BooleanVar()
        remember_check = tk.Checkbutton(remember_frame,
                                       text="Remember me",
                                       variable=self.remember_var,
                                       font=('Segoe UI', 10),
                                       fg='#666666',
                                       bg='white',
                                       relief='flat',
                                       bd=0)
        remember_check.pack(side=tk.LEFT)

        # رابط "نسيت كلمة المرور"
        forgot_link = tk.Label(remember_frame,
                              text="Forgot password?",
                              font=('Segoe UI', 10, 'underline'),
                              fg='#2196F3',
                              bg='white',
                              cursor='hand2')
        forgot_link.pack(side=tk.RIGHT)
        forgot_link.bind('<Button-1>', lambda e: self.forgot_password())

        # زر تسجيل الدخول
        self.login_btn = tk.Button(form_frame,
                                  text="LOG IN",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg='white',
                                  bg='#2196F3',
                                  relief='flat',
                                  bd=0,
                                  padx=40,
                                  pady=15,
                                  command=self.login,
                                  cursor='hand2')
        self.login_btn.pack(fill=tk.X, pady=(0, 10))

        # تأثيرات التفاعل
        self.setup_entry_effects()
        self.setup_button_effects()

        # ربط Enter بتسجيل الدخول
        self.password_entry.bind('<Return>', lambda e: self.login())
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())

        # ربط تغيير النص لتفعيل/إلغاء تفعيل الزر
        self.username_var.trace('w', self.check_login_button_state)
        self.password_var.trace('w', self.check_login_button_state)

    def setup_entry_effects(self):
        """إعداد تأثيرات حقول الإدخال"""
        # تأثيرات حقل اسم المستخدم
        def on_username_focus_in(event):
            if self.username_entry.get() == "Username":
                self.username_entry.delete(0, tk.END)
                self.username_entry.config(fg='#333333')
        
        def on_username_focus_out(event):
            if not self.username_entry.get():
                self.username_entry.insert(0, "Username")
                self.username_entry.config(fg='#999999')
        
        # تأثيرات حقل كلمة المرور
        def on_password_focus_in(event):
            if self.password_entry.get() == "Password":
                self.password_entry.delete(0, tk.END)
                self.password_entry.config(fg='#333333', show='●')
        
        def on_password_focus_out(event):
            if not self.password_entry.get():
                self.password_entry.insert(0, "Password")
                self.password_entry.config(fg='#999999', show='')
        
        self.username_entry.bind('<FocusIn>', on_username_focus_in)
        self.username_entry.bind('<FocusOut>', on_username_focus_out)
        self.password_entry.bind('<FocusIn>', on_password_focus_in)
        self.password_entry.bind('<FocusOut>', on_password_focus_out)
    
    def setup_button_effects(self):
        """إعداد تأثيرات الأزرار"""
        def on_login_btn_enter(event):
            self.login_btn.config(bg='#1976D2')
        
        def on_login_btn_leave(event):
            self.login_btn.config(bg='#2196F3')
        
        self.login_btn.bind('<Enter>', on_login_btn_enter)
        self.login_btn.bind('<Leave>', on_login_btn_leave)
    
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg='white')
        info_frame.pack(fill=tk.X, padx=40, pady=(0, 15))

        # إطار معلومات المستخدم الافتراضي مع خلفية ملونة
        info_bg = tk.Frame(info_frame, bg='#ecf0f1', relief='flat', bd=0)
        info_bg.pack(fill=tk.X, pady=(0, 15))

        # أيقونة المعلومات
        info_icon = tk.Label(info_bg,
                            text="💡",
                            font=('Segoe UI', 16),
                            fg='#f39c12',
                            bg='#ecf0f1')
        info_icon.pack(pady=(10, 5))

        # نص المعلومات
        info_text = "بيانات تسجيل الدخول الافتراضية"
        info_label = tk.Label(info_bg,
                             text=info_text,
                             font=('Segoe UI', 11, 'bold'),
                             fg='#2c3e50',
                             bg='#ecf0f1')
        info_label.pack()

        # بيانات الدخول
        credentials_text = "اسم المستخدم: admin\nكلمة المرور: admin123"
        credentials_label = tk.Label(info_bg,
                                   text=credentials_text,
                                   font=('Segoe UI', 10),
                                   fg='#7f8c8d',
                                   bg='#ecf0f1',
                                   justify=tk.CENTER)
        credentials_label.pack(pady=(5, 15))

        # رابط نسيان كلمة المرور
        forgot_btn = tk.Button(info_frame,
                              text="نسيت كلمة المرور؟",
                              font=('Segoe UI', 11, 'underline'),
                              fg='#3498db',
                              bg='white',
                              relief='flat',
                              bd=0,
                              cursor='hand2',
                              command=self.forgot_password)
        forgot_btn.pack(pady=(0, 15))

        # تأثيرات التفاعل لرابط نسيان كلمة المرور
        forgot_btn.bind('<Enter>', lambda e: forgot_btn.config(fg='#2980b9'))
        forgot_btn.bind('<Leave>', lambda e: forgot_btn.config(fg='#3498db'))
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        # التحقق من البيانات
        if not username or not password:
            self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تعطيل زر تسجيل الدخول أثناء المعالجة
        self.login_btn.config(state='disabled', text="⏳ جاري تسجيل الدخول...", bg='#f39c12')
        self.window.update()

        try:
            # محاولة تسجيل الدخول
            user = self.user_manager.authenticate(username, password)

            if user:
                # تسجيل الدخول ناجح
                self.login_btn.config(text="✅ تم بنجاح!", bg='#27ae60')
                self.window.update()

                # انتظار قصير لإظهار الرسالة
                self.window.after(1000, lambda: self._complete_login(user))
            else:
                self.show_error_message("اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_var.set("")
                self._reset_login_button()

        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            self.show_error_message("حدث خطأ أثناء تسجيل الدخول")
            self._reset_login_button()

    def _complete_login(self, user):
        """إكمال عملية تسجيل الدخول"""
        # تأثير الاختفاء التدريجي
        self._fade_out_and_close(user)

    def _fade_out_and_close(self, user):
        """تأثير الاختفاء التدريجي وإغلاق النافذة"""
        try:
            self.window.attributes('-alpha', 0.8)
            self.window.after(50, lambda: self._fade_out_step(0.8, user))
        except:
            self._close_and_callback(user)

    def _fade_out_step(self, alpha, user):
        """خطوة في تأثير الاختفاء التدريجي"""
        try:
            alpha -= 0.1
            if alpha <= 0.0:
                self._close_and_callback(user)
                return
            self.window.attributes('-alpha', alpha)
            self.window.after(30, lambda: self._fade_out_step(alpha, user))
        except:
            self._close_and_callback(user)

    def _close_and_callback(self, user):
        """إغلاق النافذة واستدعاء دالة النجاح"""
        try:
            # استدعاء callback أولاً
            self.on_login_success(user)
        except Exception as e:
            print(f"خطأ في callback: {e}")
        finally:
            # تدمير النافذة في النهاية
            try:
                if self.window and self.window.winfo_exists():
                    self.window.destroy()
            except:
                pass

    def _reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.login_btn.config(state='normal', text="🚀 تسجيل الدخول", bg='#27ae60')

    def show_error_message(self, message):
        """عرض رسالة خطأ مع تأثير بصري"""
        # تأثير اهتزاز للنافذة
        self.shake_window()

        # عرض رسالة الخطأ
        messagebox.showerror("خطأ", message)

    def shake_window(self):
        """تأثير اهتزاز للنافذة"""
        try:
            original_x = self.window.winfo_x()
            original_y = self.window.winfo_y()

            for i in range(6):
                if i % 2 == 0:
                    self.window.geometry(f"+{original_x + 3}+{original_y}")
                else:
                    self.window.geometry(f"+{original_x - 3}+{original_y}")
                self.window.update()
                self.window.after(50)

            # العودة للموضع الأصلي
            self.window.geometry(f"+{original_x}+{original_y}")
        except:
            pass
    
    def check_login_button_state(self, *args):
        """فحص حالة زر تسجيل الدخول وتفعيله/إلغاء تفعيله"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if username and password:
            # تفعيل الزر
            self.login_btn.config(state='normal', bg='#3498db', cursor='hand2')
        else:
            # إلغاء تفعيل الزر
            self.login_btn.config(state='disabled', bg='#bdc3c7', cursor='arrow')

    def on_login_btn_enter(self, event):
        """تأثير عند تمرير الماوس على زر تسجيل الدخول"""
        if self.login_btn['state'] == 'normal':
            self.login_btn.config(bg='#2980b9', relief='raised')

    def on_login_btn_leave(self, event):
        """تأثير عند إزالة الماوس من زر تسجيل الدخول"""
        if self.login_btn['state'] == 'normal':
            self.login_btn.config(bg='#3498db', relief='flat')

    def minimize_window(self):
        """تصغير النافذة"""
        self.window.iconify()

    def start_move(self, event):
        """بداية سحب النافذة"""
        self.x = event.x
        self.y = event.y

    def on_move(self, event):
        """تحريك النافذة"""
        deltax = event.x - self.x
        deltay = event.y - self.y
        x = self.window.winfo_x() + deltax
        y = self.window.winfo_y() + deltay
        self.window.geometry(f"+{x}+{y}")

    def close_window(self):
        """إغلاق النافذة"""
        self.window.quit()
        self.window.destroy()

    def fade_in_effect(self):
        """تأثير الظهور التدريجي للنافذة"""
        try:
            # بدء النافذة بشفافية 0
            self.window.attributes('-alpha', 0.0)
            self.window.after(50, self._fade_in_step, 0.0)
        except:
            # في حالة عدم دعم الشفافية
            self.window.attributes('-alpha', 1.0)

    def _fade_in_step(self, alpha):
        """خطوة في تأثير الظهور التدريجي"""
        try:
            alpha += 0.05
            if alpha >= 1.0:
                alpha = 1.0
            self.window.attributes('-alpha', alpha)
            if alpha < 1.0:
                self.window.after(30, self._fade_in_step, alpha)
        except:
            self.window.attributes('-alpha', 1.0)

    def forgot_password(self):
        """نسيان كلمة المرور"""
        messagebox.showinfo("نسيان كلمة المرور",
                           "يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()
    
    def destroy(self):
        """إغلاق النافذة"""
        self.window.destroy()
