# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable

from ..utils.colors import AppColors
from ..models.user import UserManager, User

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, db_manager, on_login_success: Callable[[User], None]):
        """تهيئة نافذة تسجيل الدخول"""
        self.db = db_manager
        self.on_login_success = on_login_success
        self.user_manager = UserManager(db_manager)
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()

        # تأثير الظهور التدريجي
        self.fade_in_effect()

    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()

        # متغيرات السحب
        self.x = 0
        self.y = 0

    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Tk()
        self.window.title("الميزان - تسجيل الدخول")
        self.window.geometry("480x600")
        self.window.resizable(False, False)

        # إزالة شريط العنوان والحدود
        self.window.overrideredirect(True)

        # تعيين لون الخلفية مع تدرج
        self.window.configure(bg='#ecf0f1')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.attributes('-topmost', True)
        self.window.lift()
        self.window.focus_force()

        # إضافة ظل للنافذة (Windows only)
        try:
            self.window.wm_attributes('-transparentcolor', '#ecf0f1')
        except:
            pass
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        # تحديث النافذة للحصول على الأبعاد الصحيحة
        self.window.update_idletasks()

        # الحصول على أبعاد النافذة
        window_width = 480
        window_height = 600

        # الحصول على أبعاد الشاشة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()

        # حساب الموضع المتوسط
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # تطبيق الموضع
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء الحاوي الرئيسي مع ظل وتدرج
        main_container = tk.Frame(self.window,
                                 bg='white',
                                 relief='flat',
                                 bd=0)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # إضافة ظل للحاوي الرئيسي
        shadow_frame = tk.Frame(self.window, bg='#bdc3c7', height=3)
        shadow_frame.place(x=18, y=18, width=464, height=582)

        # رفع الحاوي الرئيسي فوق الظل
        main_container.lift()

        # شريط العنوان مع زر الإغلاق
        self.create_title_bar(main_container)

        # شعار التطبيق
        self.create_logo_section(main_container)

        # نموذج تسجيل الدخول
        self.create_login_form(main_container)

        # معلومات إضافية
        self.create_info_section(main_container)
    
    def create_title_bar(self, parent):
        """إنشاء شريط العنوان مع زر الإغلاق"""
        title_bar = tk.Frame(parent, bg='#2c3e50', height=50)
        title_bar.pack(fill=tk.X)
        title_bar.pack_propagate(False)

        # أيقونة التطبيق
        icon_label = tk.Label(title_bar,
                             text="⚖️",
                             font=('Segoe UI', 16),
                             fg='#f39c12',
                             bg='#2c3e50')
        icon_label.pack(side=tk.LEFT, padx=(15, 5), pady=12)

        # عنوان النافذة
        title_label = tk.Label(title_bar,
                              text="تسجيل الدخول - الميزان",
                              font=('Segoe UI', 14, 'bold'),
                              fg='white',
                              bg='#2c3e50')
        title_label.pack(side=tk.LEFT, padx=5, pady=12)

        # زر تصغير (اختياري)
        minimize_btn = tk.Button(title_bar,
                                text="─",
                                font=('Segoe UI', 12, 'bold'),
                                fg='white',
                                bg='#f39c12',
                                relief='flat',
                                bd=0,
                                width=3,
                                command=self.minimize_window)
        minimize_btn.pack(side=tk.RIGHT, padx=(0, 5), pady=8)

        # زر الإغلاق
        close_btn = tk.Button(title_bar,
                             text="✕",
                             font=('Segoe UI', 12, 'bold'),
                             fg='white',
                             bg='#e74c3c',
                             relief='flat',
                             bd=0,
                             width=3,
                             command=self.close_window)
        close_btn.pack(side=tk.RIGHT, padx=5, pady=8)

        # تأثيرات التفاعل للأزرار
        close_btn.bind('<Enter>', lambda e: close_btn.config(bg='#c0392b'))
        close_btn.bind('<Leave>', lambda e: close_btn.config(bg='#e74c3c'))
        minimize_btn.bind('<Enter>', lambda e: minimize_btn.config(bg='#e67e22'))
        minimize_btn.bind('<Leave>', lambda e: minimize_btn.config(bg='#f39c12'))

        # ربط السحب لتحريك النافذة
        title_bar.bind('<Button-1>', self.start_move)
        title_bar.bind('<B1-Motion>', self.on_move)
        title_label.bind('<Button-1>', self.start_move)
        title_label.bind('<B1-Motion>', self.on_move)
        icon_label.bind('<Button-1>', self.start_move)
        icon_label.bind('<B1-Motion>', self.on_move)

    def create_logo_section(self, parent):
        """إنشاء قسم الشعار"""
        logo_frame = tk.Frame(parent, bg='white')
        logo_frame.pack(fill=tk.X, pady=(25, 15))

        # خلفية دائرية للشعار
        logo_bg = tk.Frame(logo_frame, bg='#ecf0f1', width=100, height=100)
        logo_bg.pack()
        logo_bg.pack_propagate(False)

        # شعار التطبيق
        logo_label = tk.Label(logo_bg,
                             text="⚖️",
                             font=('Segoe UI', 40),
                             fg='#2c3e50',
                             bg='#ecf0f1')
        logo_label.place(relx=0.5, rely=0.5, anchor='center')

        # اسم التطبيق مع تأثير
        title_label = tk.Label(logo_frame,
                              text="الميزان",
                              font=('Segoe UI', 28, 'bold'),
                              fg='#2c3e50',
                              bg='white')
        title_label.pack(pady=(15, 5))

        # وصف التطبيق
        subtitle_label = tk.Label(logo_frame,
                                 text="نظام إدارة الأعمال التجارية",
                                 font=('Segoe UI', 13),
                                 fg='#7f8c8d',
                                 bg='white')
        subtitle_label.pack()

        # خط فاصل أنيق
        separator = tk.Frame(logo_frame, bg='#bdc3c7', height=2)
        separator.pack(fill=tk.X, padx=80, pady=(15, 0))
    
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = tk.Frame(parent, bg='white')
        form_frame.pack(fill=tk.X, padx=40, pady=(20, 15))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill=tk.X, pady=15)

        # حقل اسم المستخدم مع تحسينات
        username_frame = tk.Frame(content_frame, bg='white')
        username_frame.pack(fill=tk.X, pady=(0, 20))

        # تسمية حقل اسم المستخدم
        username_label = tk.Label(username_frame,
                                 text="👤 اسم المستخدم",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg='#2c3e50',
                                 bg='white')
        username_label.pack(anchor='e', pady=(0, 8))

        # إطار حقل اسم المستخدم مع حدود ملونة
        username_border = tk.Frame(username_frame, bg='#3498db', height=2)
        username_border.pack(fill=tk.X)

        self.username_entry = tk.Entry(username_frame,
                                      textvariable=self.username_var,
                                      font=('Segoe UI', 14),
                                      relief='flat',
                                      bd=0,
                                      bg='#f8f9fa',
                                      fg='#2c3e50',
                                      insertbackground='#3498db')
        self.username_entry.pack(fill=tk.X, ipady=12, pady=(0, 2))
        self.username_entry.focus()

        # حقل كلمة المرور مع تحسينات
        password_frame = tk.Frame(content_frame, bg='white')
        password_frame.pack(fill=tk.X, pady=(0, 25))

        # تسمية حقل كلمة المرور
        password_label = tk.Label(password_frame,
                                 text="🔒 كلمة المرور",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg='#2c3e50',
                                 bg='white')
        password_label.pack(anchor='e', pady=(0, 8))

        # إطار حقل كلمة المرور مع حدود ملونة
        password_border = tk.Frame(password_frame, bg='#3498db', height=2)
        password_border.pack(fill=tk.X)

        self.password_entry = tk.Entry(password_frame,
                                      textvariable=self.password_var,
                                      font=('Segoe UI', 14),
                                      show='●',
                                      relief='flat',
                                      bd=0,
                                      bg='#f8f9fa',
                                      fg='#2c3e50',
                                      insertbackground='#3498db')
        self.password_entry.pack(fill=tk.X, ipady=12, pady=(0, 2))

        # تأثيرات التفاعل للحقول
        self.username_entry.bind('<FocusIn>', lambda e: username_border.config(bg='#2ecc71'))
        self.username_entry.bind('<FocusOut>', lambda e: username_border.config(bg='#3498db'))
        self.password_entry.bind('<FocusIn>', lambda e: password_border.config(bg='#2ecc71'))
        self.password_entry.bind('<FocusOut>', lambda e: password_border.config(bg='#3498db'))

        # ربط Enter بتسجيل الدخول
        self.password_entry.bind('<Return>', lambda e: self.login() if self.login_btn['state'] == 'normal' else None)
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())

        # ربط تغيير النص لتفعيل/إلغاء تفعيل الزر
        self.username_var.trace('w', self.check_login_button_state)
        self.password_var.trace('w', self.check_login_button_state)

        # زر تسجيل الدخول المحسن
        self.login_btn = tk.Button(content_frame,
                                  text="🚀 تسجيل الدخول",
                                  font=('Segoe UI', 16, 'bold'),
                                  fg='white',
                                  bg='#95a5a6',  # رمادي في البداية
                                  relief='flat',
                                  bd=0,
                                  padx=30,
                                  pady=15,
                                  state='disabled',  # معطل في البداية
                                  command=self.login,
                                  cursor='hand2')
        self.login_btn.pack(fill=tk.X, pady=(5, 0))

        # تأثيرات التفاعل لزر تسجيل الدخول
        self.login_btn.bind('<Enter>', self.on_login_btn_enter)
        self.login_btn.bind('<Leave>', self.on_login_btn_leave)
    
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg='white')
        info_frame.pack(fill=tk.X, padx=40, pady=(0, 15))

        # إطار معلومات المستخدم الافتراضي مع خلفية ملونة
        info_bg = tk.Frame(info_frame, bg='#ecf0f1', relief='flat', bd=0)
        info_bg.pack(fill=tk.X, pady=(0, 15))

        # أيقونة المعلومات
        info_icon = tk.Label(info_bg,
                            text="💡",
                            font=('Segoe UI', 16),
                            fg='#f39c12',
                            bg='#ecf0f1')
        info_icon.pack(pady=(10, 5))

        # نص المعلومات
        info_text = "بيانات تسجيل الدخول الافتراضية"
        info_label = tk.Label(info_bg,
                             text=info_text,
                             font=('Segoe UI', 11, 'bold'),
                             fg='#2c3e50',
                             bg='#ecf0f1')
        info_label.pack()

        # بيانات الدخول
        credentials_text = "اسم المستخدم: admin\nكلمة المرور: admin123"
        credentials_label = tk.Label(info_bg,
                                   text=credentials_text,
                                   font=('Segoe UI', 10),
                                   fg='#7f8c8d',
                                   bg='#ecf0f1',
                                   justify=tk.CENTER)
        credentials_label.pack(pady=(5, 15))

        # رابط نسيان كلمة المرور
        forgot_btn = tk.Button(info_frame,
                              text="نسيت كلمة المرور؟",
                              font=('Segoe UI', 11, 'underline'),
                              fg='#3498db',
                              bg='white',
                              relief='flat',
                              bd=0,
                              cursor='hand2',
                              command=self.forgot_password)
        forgot_btn.pack(pady=(0, 15))

        # تأثيرات التفاعل لرابط نسيان كلمة المرور
        forgot_btn.bind('<Enter>', lambda e: forgot_btn.config(fg='#2980b9'))
        forgot_btn.bind('<Leave>', lambda e: forgot_btn.config(fg='#3498db'))
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        # التحقق من البيانات
        if not username or not password:
            self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تعطيل زر تسجيل الدخول أثناء المعالجة
        self.login_btn.config(state='disabled', text="⏳ جاري تسجيل الدخول...", bg='#f39c12')
        self.window.update()

        try:
            # محاولة تسجيل الدخول
            user = self.user_manager.authenticate(username, password)

            if user:
                # تسجيل الدخول ناجح
                self.login_btn.config(text="✅ تم بنجاح!", bg='#27ae60')
                self.window.update()

                # انتظار قصير لإظهار الرسالة
                self.window.after(1000, lambda: self._complete_login(user))
            else:
                self.show_error_message("اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_var.set("")
                self._reset_login_button()

        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            self.show_error_message("حدث خطأ أثناء تسجيل الدخول")
            self._reset_login_button()

    def _complete_login(self, user):
        """إكمال عملية تسجيل الدخول"""
        # تأثير الاختفاء التدريجي
        self._fade_out_and_close(user)

    def _fade_out_and_close(self, user):
        """تأثير الاختفاء التدريجي وإغلاق النافذة"""
        try:
            self.window.attributes('-alpha', 0.8)
            self.window.after(50, lambda: self._fade_out_step(0.8, user))
        except:
            self._close_and_callback(user)

    def _fade_out_step(self, alpha, user):
        """خطوة في تأثير الاختفاء التدريجي"""
        try:
            alpha -= 0.1
            if alpha <= 0.0:
                self._close_and_callback(user)
                return
            self.window.attributes('-alpha', alpha)
            self.window.after(30, lambda: self._fade_out_step(alpha, user))
        except:
            self._close_and_callback(user)

    def _close_and_callback(self, user):
        """إغلاق النافذة واستدعاء دالة النجاح"""
        self.window.destroy()
        self.on_login_success(user)

    def _reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.login_btn.config(state='normal', text="🚀 تسجيل الدخول", bg='#27ae60')

    def show_error_message(self, message):
        """عرض رسالة خطأ مع تأثير بصري"""
        # تأثير اهتزاز للنافذة
        self.shake_window()

        # عرض رسالة الخطأ
        messagebox.showerror("خطأ", message)

    def shake_window(self):
        """تأثير اهتزاز للنافذة"""
        try:
            original_x = self.window.winfo_x()
            original_y = self.window.winfo_y()

            for i in range(6):
                if i % 2 == 0:
                    self.window.geometry(f"+{original_x + 3}+{original_y}")
                else:
                    self.window.geometry(f"+{original_x - 3}+{original_y}")
                self.window.update()
                self.window.after(50)

            # العودة للموضع الأصلي
            self.window.geometry(f"+{original_x}+{original_y}")
        except:
            pass
    
    def check_login_button_state(self, *args):
        """فحص حالة زر تسجيل الدخول وتفعيله/إلغاء تفعيله"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if username and password:
            # تفعيل الزر
            self.login_btn.config(state='normal', bg='#27ae60', cursor='hand2')
        else:
            # إلغاء تفعيل الزر
            self.login_btn.config(state='disabled', bg='#95a5a6', cursor='arrow')

    def on_login_btn_enter(self, event):
        """تأثير عند تمرير الماوس على زر تسجيل الدخول"""
        if self.login_btn['state'] == 'normal':
            self.login_btn.config(bg='#2ecc71')

    def on_login_btn_leave(self, event):
        """تأثير عند إزالة الماوس من زر تسجيل الدخول"""
        if self.login_btn['state'] == 'normal':
            self.login_btn.config(bg='#27ae60')

    def minimize_window(self):
        """تصغير النافذة"""
        self.window.iconify()

    def start_move(self, event):
        """بداية سحب النافذة"""
        self.x = event.x
        self.y = event.y

    def on_move(self, event):
        """تحريك النافذة"""
        deltax = event.x - self.x
        deltay = event.y - self.y
        x = self.window.winfo_x() + deltax
        y = self.window.winfo_y() + deltay
        self.window.geometry(f"+{x}+{y}")

    def close_window(self):
        """إغلاق النافذة"""
        self.window.quit()
        self.window.destroy()

    def fade_in_effect(self):
        """تأثير الظهور التدريجي للنافذة"""
        try:
            # بدء النافذة بشفافية 0
            self.window.attributes('-alpha', 0.0)
            self.window.after(50, self._fade_in_step, 0.0)
        except:
            # في حالة عدم دعم الشفافية
            self.window.attributes('-alpha', 1.0)

    def _fade_in_step(self, alpha):
        """خطوة في تأثير الظهور التدريجي"""
        try:
            alpha += 0.05
            if alpha >= 1.0:
                alpha = 1.0
            self.window.attributes('-alpha', alpha)
            if alpha < 1.0:
                self.window.after(30, self._fade_in_step, alpha)
        except:
            self.window.attributes('-alpha', 1.0)

    def forgot_password(self):
        """نسيان كلمة المرور"""
        messagebox.showinfo("نسيان كلمة المرور",
                           "يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()
    
    def destroy(self):
        """إغلاق النافذة"""
        self.window.destroy()
