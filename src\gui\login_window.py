# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable

from ..utils.colors import AppColors
from ..models.user import UserManager, User

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, db_manager, on_login_success: Callable[[User], None]):
        """تهيئة نافذة تسجيل الدخول"""
        self.db = db_manager
        self.on_login_success = on_login_success
        self.user_manager = UserManager(db_manager)
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.create_window()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Tk()
        self.window.title("الميزان - تسجيل الدخول")
        self.window.geometry("450x550")
        self.window.resizable(False, False)

        # إزالة شريط العنوان والحدود
        self.window.overrideredirect(True)

        # تعيين لون الخلفية
        self.window.configure(bg='#f0f0f0')

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.attributes('-topmost', True)
        self.window.lift()
        self.window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء الحاوي الرئيسي مع حدود وظل
        main_container = tk.Frame(self.window,
                                 bg='white',
                                 relief='solid',
                                 bd=1)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # شريط العنوان مع زر الإغلاق
        self.create_title_bar(main_container)

        # شعار التطبيق
        self.create_logo_section(main_container)

        # نموذج تسجيل الدخول
        self.create_login_form(main_container)

        # معلومات إضافية
        self.create_info_section(main_container)
    
    def create_title_bar(self, parent):
        """إنشاء شريط العنوان مع زر الإغلاق"""
        title_bar = tk.Frame(parent, bg='#3498db', height=40)
        title_bar.pack(fill=tk.X)
        title_bar.pack_propagate(False)

        # عنوان النافذة
        title_label = tk.Label(title_bar,
                              text="تسجيل الدخول - الميزان",
                              font=('Segoe UI', 12, 'bold'),
                              fg='white',
                              bg='#3498db')
        title_label.pack(side=tk.LEFT, padx=15, pady=10)

        # زر الإغلاق
        close_btn = tk.Button(title_bar,
                             text="✕",
                             font=('Segoe UI', 14, 'bold'),
                             fg='white',
                             bg='#e74c3c',
                             relief='flat',
                             bd=0,
                             width=3,
                             command=self.close_window)
        close_btn.pack(side=tk.RIGHT, padx=5, pady=5)

        # ربط السحب لتحريك النافذة
        title_bar.bind('<Button-1>', self.start_move)
        title_bar.bind('<B1-Motion>', self.on_move)
        title_label.bind('<Button-1>', self.start_move)
        title_label.bind('<B1-Motion>', self.on_move)

    def create_logo_section(self, parent):
        """إنشاء قسم الشعار"""
        logo_frame = tk.Frame(parent, bg='white')
        logo_frame.pack(fill=tk.X, pady=(30, 20))

        # شعار التطبيق
        logo_label = tk.Label(logo_frame,
                             text="⚖️",
                             font=('Segoe UI', 48),
                             fg='#3498db',
                             bg='white')
        logo_label.pack()

        # اسم التطبيق
        title_label = tk.Label(logo_frame,
                              text="الميزان",
                              font=('Segoe UI', 24, 'bold'),
                              fg='#2c3e50',
                              bg='white')
        title_label.pack()

        # وصف التطبيق
        subtitle_label = tk.Label(logo_frame,
                                 text="نظام إدارة الأعمال التجارية",
                                 font=('Segoe UI', 12),
                                 fg='#7f8c8d',
                                 bg='white')
        subtitle_label.pack()
    
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = tk.Frame(parent, bg='white')
        form_frame.pack(fill=tk.X, padx=40, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill=tk.X, pady=20)

        # حقل اسم المستخدم
        username_frame = tk.Frame(content_frame, bg='white')
        username_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(username_frame,
                text="👤 اسم المستخدم:",
                font=('Segoe UI', 12, 'bold'),
                fg='#2c3e50',
                bg='white').pack(anchor='e', pady=(0, 5))

        self.username_entry = tk.Entry(username_frame,
                                      textvariable=self.username_var,
                                      font=('Segoe UI', 14),
                                      relief='solid',
                                      bd=1,
                                      bg='#f8f9fa',
                                      fg='#2c3e50')
        self.username_entry.pack(fill=tk.X, ipady=8)
        self.username_entry.focus()

        # حقل كلمة المرور
        password_frame = tk.Frame(content_frame, bg='white')
        password_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(password_frame,
                text="🔒 كلمة المرور:",
                font=('Segoe UI', 12, 'bold'),
                fg='#2c3e50',
                bg='white').pack(anchor='e', pady=(0, 5))

        self.password_entry = tk.Entry(password_frame,
                                      textvariable=self.password_var,
                                      font=('Segoe UI', 14),
                                      show='*',
                                      relief='solid',
                                      bd=1,
                                      bg='#f8f9fa',
                                      fg='#2c3e50')
        self.password_entry.pack(fill=tk.X, ipady=8)

        # ربط Enter بتسجيل الدخول
        self.password_entry.bind('<Return>', lambda e: self.login())
        self.username_entry.bind('<Return>', lambda e: self.password_entry.focus())

        # ربط تغيير النص لتفعيل/إلغاء تفعيل الزر
        self.username_var.trace('w', self.check_login_button_state)
        self.password_var.trace('w', self.check_login_button_state)

        # زر تسجيل الدخول
        self.login_btn = tk.Button(content_frame,
                                  text="🚀 تسجيل الدخول",
                                  font=('Segoe UI', 14, 'bold'),
                                  fg='white',
                                  bg='#95a5a6',  # رمادي في البداية
                                  relief='flat',
                                  padx=30,
                                  pady=12,
                                  state='disabled',  # معطل في البداية
                                  command=self.login)
        self.login_btn.pack(fill=tk.X, pady=(10, 0))
    
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg='white')
        info_frame.pack(fill=tk.X, padx=40)

        # معلومات المستخدم الافتراضي
        info_text = "💡 بيانات تسجيل الدخول الافتراضية:\nاسم المستخدم: admin | كلمة المرور: admin123"

        info_label = tk.Label(info_frame,
                             text=info_text,
                             font=('Segoe UI', 10),
                             fg='#7f8c8d',
                             bg='white',
                             justify=tk.CENTER)
        info_label.pack(pady=(0, 20))

        # رابط نسيان كلمة المرور
        forgot_btn = tk.Button(info_frame,
                              text="نسيت كلمة المرور؟",
                              font=('Segoe UI', 10, 'underline'),
                              fg='#3498db',
                              bg='white',
                              relief='flat',
                              bd=0,
                              command=self.forgot_password)
        forgot_btn.pack(pady=(0, 20))
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        # التحقق من البيانات
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            return
        
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            return
        
        # محاولة تسجيل الدخول
        user = self.user_manager.authenticate(username, password)
        
        if user:
            # نجح تسجيل الدخول
            messagebox.showinfo("نجح", f"مرحباً {user.full_name}")
            
            # إخفاء نافذة تسجيل الدخول
            self.window.withdraw()
            
            # استدعاء دالة النجاح
            self.on_login_success(user)
            
            # إغلاق نافذة تسجيل الدخول
            self.window.destroy()
        else:
            # فشل تسجيل الدخول
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_var.set("")
    
    def check_login_button_state(self, *args):
        """فحص حالة زر تسجيل الدخول وتفعيله/إلغاء تفعيله"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if username and password:
            # تفعيل الزر
            self.login_btn.config(state='normal', bg='#3498db')
        else:
            # إلغاء تفعيل الزر
            self.login_btn.config(state='disabled', bg='#95a5a6')

    def start_move(self, event):
        """بداية سحب النافذة"""
        self.x = event.x
        self.y = event.y

    def on_move(self, event):
        """تحريك النافذة"""
        deltax = event.x - self.x
        deltay = event.y - self.y
        x = self.window.winfo_x() + deltax
        y = self.window.winfo_y() + deltay
        self.window.geometry(f"+{x}+{y}")

    def close_window(self):
        """إغلاق النافذة"""
        self.window.quit()
        self.window.destroy()

    def forgot_password(self):
        """نسيان كلمة المرور"""
        messagebox.showinfo("نسيان كلمة المرور",
                           "يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()
    
    def destroy(self):
        """إغلاق النافذة"""
        self.window.destroy()
