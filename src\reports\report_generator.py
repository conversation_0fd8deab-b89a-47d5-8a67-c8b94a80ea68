# -*- coding: utf-8 -*-
"""
مولد التقارير
Report Generator
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import json

class ReportGenerator:
    """مولد التقارير"""
    
    def __init__(self, db_manager):
        """
        تهيئة مولد التقارير
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def sales_report(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """
        تقرير المبيعات
        
        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            بيانات تقرير المبيعات
        """
        try:
            # إجمالي المبيعات
            total_sales_query = """
                SELECT 
                    COUNT(*) as invoice_count,
                    SUM(total_amount) as total_sales,
                    SUM(paid_amount) as total_paid,
                    SUM(remaining_amount) as total_remaining
                FROM invoices 
                WHERE invoice_type = 'sale' 
                AND invoice_date BETWEEN ? AND ?
                AND is_cancelled = 0
            """
            
            total_sales = self.db.fetch_one(total_sales_query, (start_date, end_date))
            
            # المبيعات اليومية
            daily_sales_query = """
                SELECT 
                    invoice_date,
                    COUNT(*) as invoice_count,
                    SUM(total_amount) as daily_total
                FROM invoices 
                WHERE invoice_type = 'sale' 
                AND invoice_date BETWEEN ? AND ?
                AND is_cancelled = 0
                GROUP BY invoice_date
                ORDER BY invoice_date
            """
            
            daily_sales = self.db.fetch_all(daily_sales_query, (start_date, end_date))
            
            # أفضل العملاء
            top_customers_query = """
                SELECT 
                    c.name as customer_name,
                    COUNT(i.id) as invoice_count,
                    SUM(i.total_amount) as total_amount
                FROM invoices i
                JOIN customers c ON i.customer_id = c.id
                WHERE i.invoice_type = 'sale' 
                AND i.invoice_date BETWEEN ? AND ?
                AND i.is_cancelled = 0
                GROUP BY i.customer_id, c.name
                ORDER BY total_amount DESC
                LIMIT 10
            """
            
            top_customers = self.db.fetch_all(top_customers_query, (start_date, end_date))
            
            # أفضل المنتجات
            top_products_query = """
                SELECT 
                    p.name as product_name,
                    SUM(ii.quantity) as total_quantity,
                    SUM(ii.total_price) as total_amount
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                JOIN invoices i ON ii.invoice_id = i.id
                WHERE i.invoice_type = 'sale' 
                AND i.invoice_date BETWEEN ? AND ?
                AND i.is_cancelled = 0
                GROUP BY ii.product_id, p.name
                ORDER BY total_amount DESC
                LIMIT 10
            """
            
            top_products = self.db.fetch_all(top_products_query, (start_date, end_date))
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'summary': {
                    'invoice_count': total_sales['invoice_count'] or 0,
                    'total_sales': total_sales['total_sales'] or 0,
                    'total_paid': total_sales['total_paid'] or 0,
                    'total_remaining': total_sales['total_remaining'] or 0
                },
                'daily_sales': [dict(row) for row in daily_sales],
                'top_customers': [dict(row) for row in top_customers],
                'top_products': [dict(row) for row in top_products]
            }
            
        except Exception as e:
            print(f"خطأ في تقرير المبيعات: {e}")
            raise
    
    def inventory_report(self) -> Dict[str, Any]:
        """
        تقرير المخزون
        
        Returns:
            بيانات تقرير المخزون
        """
        try:
            # إجمالي المخزون
            total_inventory_query = """
                SELECT 
                    COUNT(*) as product_count,
                    SUM(stock_quantity) as total_quantity,
                    SUM(stock_quantity * cost_price) as total_cost_value,
                    SUM(stock_quantity * selling_price) as total_selling_value
                FROM products 
                WHERE is_active = 1
            """
            
            total_inventory = self.db.fetch_one(total_inventory_query)
            
            # المنتجات منخفضة المخزون
            low_stock_query = """
                SELECT 
                    code, name, stock_quantity, min_stock_level,
                    (stock_quantity * selling_price) as value
                FROM products 
                WHERE is_active = 1 
                AND stock_quantity <= min_stock_level
                ORDER BY stock_quantity ASC
            """
            
            low_stock = self.db.fetch_all(low_stock_query)
            
            # المنتجات نافدة المخزون
            out_of_stock_query = """
                SELECT 
                    code, name, stock_quantity, min_stock_level
                FROM products 
                WHERE is_active = 1 
                AND stock_quantity <= 0
                ORDER BY name
            """
            
            out_of_stock = self.db.fetch_all(out_of_stock_query)
            
            # المنتجات عالية القيمة
            high_value_query = """
                SELECT 
                    code, name, stock_quantity, selling_price,
                    (stock_quantity * selling_price) as total_value
                FROM products 
                WHERE is_active = 1 
                AND stock_quantity > 0
                ORDER BY total_value DESC
                LIMIT 20
            """
            
            high_value = self.db.fetch_all(high_value_query)
            
            return {
                'summary': {
                    'product_count': total_inventory['product_count'] or 0,
                    'total_quantity': total_inventory['total_quantity'] or 0,
                    'total_cost_value': total_inventory['total_cost_value'] or 0,
                    'total_selling_value': total_inventory['total_selling_value'] or 0
                },
                'low_stock': [dict(row) for row in low_stock],
                'out_of_stock': [dict(row) for row in out_of_stock],
                'high_value': [dict(row) for row in high_value]
            }
            
        except Exception as e:
            print(f"خطأ في تقرير المخزون: {e}")
            raise
    
    def profit_loss_report(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """
        تقرير الأرباح والخسائر
        
        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            بيانات تقرير الأرباح والخسائر
        """
        try:
            # إيرادات المبيعات
            sales_revenue_query = """
                SELECT 
                    SUM(total_amount) as total_revenue,
                    SUM(tax_amount) as total_tax
                FROM invoices 
                WHERE invoice_type = 'sale' 
                AND invoice_date BETWEEN ? AND ?
                AND is_cancelled = 0
            """
            
            sales_revenue = self.db.fetch_one(sales_revenue_query, (start_date, end_date))
            
            # تكلفة البضاعة المباعة
            cogs_query = """
                SELECT 
                    SUM(ii.quantity * p.cost_price) as total_cogs
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                JOIN invoices i ON ii.invoice_id = i.id
                WHERE i.invoice_type = 'sale' 
                AND i.invoice_date BETWEEN ? AND ?
                AND i.is_cancelled = 0
            """
            
            cogs = self.db.fetch_one(cogs_query, (start_date, end_date))
            
            # مصروفات الشراء
            purchase_expenses_query = """
                SELECT 
                    SUM(total_amount) as total_purchases
                FROM invoices 
                WHERE invoice_type = 'purchase' 
                AND invoice_date BETWEEN ? AND ?
                AND is_cancelled = 0
            """
            
            purchase_expenses = self.db.fetch_one(purchase_expenses_query, (start_date, end_date))
            
            # حساب الأرباح
            revenue = sales_revenue['total_revenue'] or 0
            cost_of_goods = cogs['total_cogs'] or 0
            purchases = purchase_expenses['total_purchases'] or 0
            tax = sales_revenue['total_tax'] or 0
            
            gross_profit = revenue - cost_of_goods
            net_profit = gross_profit - tax
            profit_margin = (gross_profit / revenue * 100) if revenue > 0 else 0
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'revenue': {
                    'total_sales': revenue,
                    'tax_amount': tax,
                    'net_sales': revenue - tax
                },
                'expenses': {
                    'cost_of_goods_sold': cost_of_goods,
                    'total_purchases': purchases
                },
                'profit': {
                    'gross_profit': gross_profit,
                    'net_profit': net_profit,
                    'profit_margin': profit_margin
                }
            }
            
        except Exception as e:
            print(f"خطأ في تقرير الأرباح والخسائر: {e}")
            raise
    
    def customer_report(self) -> Dict[str, Any]:
        """
        تقرير العملاء
        
        Returns:
            بيانات تقرير العملاء
        """
        try:
            # إجمالي العملاء
            total_customers_query = """
                SELECT 
                    COUNT(*) as total_customers,
                    SUM(current_balance) as total_balance,
                    SUM(credit_limit) as total_credit_limit
                FROM customers 
                WHERE is_active = 1
            """
            
            total_customers = self.db.fetch_one(total_customers_query)
            
            # العملاء المدينون
            debtors_query = """
                SELECT 
                    code, name, phone, current_balance, credit_limit,
                    (credit_limit - current_balance) as available_credit
                FROM customers 
                WHERE is_active = 1 
                AND current_balance > 0
                ORDER BY current_balance DESC
            """
            
            debtors = self.db.fetch_all(debtors_query)
            
            # العملاء الذين تجاوزوا حد الائتمان
            over_limit_query = """
                SELECT 
                    code, name, phone, current_balance, credit_limit,
                    (current_balance - credit_limit) as over_amount
                FROM customers 
                WHERE is_active = 1 
                AND current_balance > credit_limit
                ORDER BY over_amount DESC
            """
            
            over_limit = self.db.fetch_all(over_limit_query)
            
            # أفضل العملاء (حسب إجمالي المشتريات)
            top_customers_query = """
                SELECT 
                    c.code, c.name, c.phone,
                    COUNT(i.id) as total_invoices,
                    SUM(i.total_amount) as total_purchases
                FROM customers c
                LEFT JOIN invoices i ON c.id = i.customer_id 
                    AND i.invoice_type = 'sale' 
                    AND i.is_cancelled = 0
                WHERE c.is_active = 1
                GROUP BY c.id, c.code, c.name, c.phone
                HAVING total_purchases > 0
                ORDER BY total_purchases DESC
                LIMIT 20
            """
            
            top_customers = self.db.fetch_all(top_customers_query)
            
            return {
                'summary': {
                    'total_customers': total_customers['total_customers'] or 0,
                    'total_balance': total_customers['total_balance'] or 0,
                    'total_credit_limit': total_customers['total_credit_limit'] or 0
                },
                'debtors': [dict(row) for row in debtors],
                'over_limit': [dict(row) for row in over_limit],
                'top_customers': [dict(row) for row in top_customers]
            }
            
        except Exception as e:
            print(f"خطأ في تقرير العملاء: {e}")
            raise
    
    def dashboard_summary(self) -> Dict[str, Any]:
        """
        ملخص لوحة التحكم
        
        Returns:
            بيانات ملخص لوحة التحكم
        """
        try:
            today = date.today()
            
            # مبيعات اليوم
            today_sales_query = """
                SELECT 
                    COUNT(*) as invoice_count,
                    SUM(total_amount) as total_sales
                FROM invoices 
                WHERE invoice_type = 'sale' 
                AND invoice_date = ?
                AND is_cancelled = 0
            """
            
            today_sales = self.db.fetch_one(today_sales_query, (today,))
            
            # مبيعات الشهر
            month_start = today.replace(day=1)
            month_sales_query = """
                SELECT 
                    COUNT(*) as invoice_count,
                    SUM(total_amount) as total_sales
                FROM invoices 
                WHERE invoice_type = 'sale' 
                AND invoice_date >= ?
                AND is_cancelled = 0
            """
            
            month_sales = self.db.fetch_one(month_sales_query, (month_start,))
            
            # إجمالي العملاء
            customers_count_query = "SELECT COUNT(*) as count FROM customers WHERE is_active = 1"
            customers_count = self.db.fetch_one(customers_count_query)
            
            # إجمالي المنتجات
            products_count_query = "SELECT COUNT(*) as count FROM products WHERE is_active = 1"
            products_count = self.db.fetch_one(products_count_query)
            
            # المنتجات منخفضة المخزون
            low_stock_count_query = """
                SELECT COUNT(*) as count 
                FROM products 
                WHERE is_active = 1 
                AND stock_quantity <= min_stock_level
            """
            low_stock_count = self.db.fetch_one(low_stock_count_query)
            
            # الفواتير غير المدفوعة
            unpaid_invoices_query = """
                SELECT 
                    COUNT(*) as count,
                    SUM(remaining_amount) as total_amount
                FROM invoices 
                WHERE invoice_type = 'sale' 
                AND payment_status != 'paid'
                AND is_cancelled = 0
            """
            unpaid_invoices = self.db.fetch_one(unpaid_invoices_query)
            
            return {
                'today_sales': {
                    'count': today_sales['invoice_count'] or 0,
                    'amount': today_sales['total_sales'] or 0
                },
                'month_sales': {
                    'count': month_sales['invoice_count'] or 0,
                    'amount': month_sales['total_sales'] or 0
                },
                'customers_count': customers_count['count'] or 0,
                'products_count': products_count['count'] or 0,
                'low_stock_count': low_stock_count['count'] or 0,
                'unpaid_invoices': {
                    'count': unpaid_invoices['count'] or 0,
                    'amount': unpaid_invoices['total_amount'] or 0
                }
            }
            
        except Exception as e:
            print(f"خطأ في ملخص لوحة التحكم: {e}")
            raise
