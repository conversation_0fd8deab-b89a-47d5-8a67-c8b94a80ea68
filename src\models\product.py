# -*- coding: utf-8 -*-
"""
نموذج المنتج
Product Model
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

@dataclass
class Product:
    """نموذج بيانات المنتج"""
    
    id: Optional[int] = None
    code: str = ""
    name: str = ""
    description: str = ""
    category_id: Optional[int] = None
    category_name: str = ""
    unit: str = "قطعة"
    cost_price: float = 0.0
    selling_price: float = 0.0
    stock_quantity: int = 0
    min_stock_level: int = 0
    max_stock_level: int = 1000
    barcode: str = ""
    image_path: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """معالجة ما بعد التهيئة"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    @property
    def profit_margin(self) -> float:
        """حساب هامش الربح"""
        if self.cost_price > 0:
            return ((self.selling_price - self.cost_price) / self.cost_price) * 100
        return 0.0
    
    @property
    def profit_amount(self) -> float:
        """حساب مبلغ الربح"""
        return self.selling_price - self.cost_price
    
    @property
    def is_low_stock(self) -> bool:
        """التحقق من انخفاض المخزون"""
        return self.stock_quantity <= self.min_stock_level
    
    @property
    def stock_status(self) -> str:
        """حالة المخزون"""
        if self.stock_quantity <= 0:
            return "نفد المخزون"
        elif self.is_low_stock:
            return "مخزون منخفض"
        elif self.stock_quantity >= self.max_stock_level:
            return "مخزون مرتفع"
        else:
            return "متوفر"
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'category_id': self.category_id,
            'unit': self.unit,
            'cost_price': self.cost_price,
            'selling_price': self.selling_price,
            'stock_quantity': self.stock_quantity,
            'min_stock_level': self.min_stock_level,
            'max_stock_level': self.max_stock_level,
            'barcode': self.barcode,
            'image_path': self.image_path,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Product':
        """إنشاء من قاموس"""
        product = cls()
        for key, value in data.items():
            if hasattr(product, key):
                if key in ['created_at', 'updated_at'] and value:
                    if isinstance(value, str):
                        setattr(product, key, datetime.fromisoformat(value))
                    else:
                        setattr(product, key, value)
                else:
                    setattr(product, key, value)
        return product
    
    def validate(self) -> List[str]:
        """التحقق من صحة البيانات"""
        errors = []
        
        if not self.code.strip():
            errors.append("كود المنتج مطلوب")
        
        if not self.name.strip():
            errors.append("اسم المنتج مطلوب")
        
        if self.selling_price < 0:
            errors.append("سعر البيع لا يمكن أن يكون سالباً")
        
        if self.cost_price < 0:
            errors.append("سعر التكلفة لا يمكن أن يكون سالباً")
        
        if self.stock_quantity < 0:
            errors.append("كمية المخزون لا يمكن أن تكون سالبة")
        
        if self.min_stock_level < 0:
            errors.append("الحد الأدنى للمخزون لا يمكن أن يكون سالباً")
        
        if self.max_stock_level <= self.min_stock_level:
            errors.append("الحد الأقصى للمخزون يجب أن يكون أكبر من الحد الأدنى")
        
        return errors
    
    def update_stock(self, quantity: int, operation: str = 'add'):
        """
        تحديث المخزون
        
        Args:
            quantity: الكمية
            operation: العملية ('add', 'subtract', 'set')
        """
        if operation == 'add':
            self.stock_quantity += quantity
        elif operation == 'subtract':
            self.stock_quantity -= quantity
        elif operation == 'set':
            self.stock_quantity = quantity
        
        # التأكد من عدم كون المخزون سالباً
        if self.stock_quantity < 0:
            self.stock_quantity = 0
        
        self.updated_at = datetime.now()

class ProductManager:
    """مدير المنتجات"""
    
    def __init__(self, db_manager):
        """
        تهيئة مدير المنتجات
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def create_product(self, product: Product) -> Optional[int]:
        """
        إنشاء منتج جديد
        
        Args:
            product: بيانات المنتج
            
        Returns:
            معرف المنتج الجديد أو None في حالة الفشل
        """
        try:
            # التحقق من صحة البيانات
            errors = product.validate()
            if errors:
                raise ValueError(f"أخطاء في البيانات: {', '.join(errors)}")
            
            # التحقق من عدم تكرار الكود
            existing = self.get_product_by_code(product.code)
            if existing:
                raise ValueError("كود المنتج موجود مسبقاً")
            
            # إدراج المنتج
            query = """
                INSERT INTO products (
                    code, name, description, category_id, unit, cost_price,
                    selling_price, stock_quantity, min_stock_level, max_stock_level,
                    barcode, image_path, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor = self.db.execute_query(query, (
                product.code, product.name, product.description, product.category_id,
                product.unit, product.cost_price, product.selling_price,
                product.stock_quantity, product.min_stock_level, product.max_stock_level,
                product.barcode, product.image_path, product.is_active
            ))
            
            return cursor.lastrowid
            
        except Exception as e:
            print(f"خطأ في إنشاء المنتج: {e}")
            raise
    
    def get_product_by_id(self, product_id: int) -> Optional[Product]:
        """
        الحصول على منتج بالمعرف
        
        Args:
            product_id: معرف المنتج
            
        Returns:
            المنتج أو None
        """
        query = """
            SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.id = ?
        """
        
        row = self.db.fetch_one(query, (product_id,))
        if row:
            return Product.from_dict(dict(row))
        return None
    
    def get_product_by_code(self, code: str) -> Optional[Product]:
        """
        الحصول على منتج بالكود
        
        Args:
            code: كود المنتج
            
        Returns:
            المنتج أو None
        """
        query = """
            SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.code = ?
        """
        
        row = self.db.fetch_one(query, (code,))
        if row:
            return Product.from_dict(dict(row))
        return None
