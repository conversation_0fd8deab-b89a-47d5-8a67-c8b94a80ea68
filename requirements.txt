# متطلبات برنامج الميزان
# Al-Mezan Business Management System Requirements

# Note: Most packages are built into Python and don't need installation
# ملاحظة: معظم الحزم مدمجة في Python ولا تحتاج تثبيت

# Built-in Python packages (no installation needed):
# حزم Python المدمجة (لا تحتاج تثبيت):
# - tkinter (GUI framework)
# - sqlite3 (database)
# - datetime (date/time handling)
# - os (operating system interface)
# - json (JSON handling)
# - hashlib (password hashing)

# Optional packages for enhanced functionality:
# حزم اختيارية لوظائف محسنة:

# For PDF generation (optional)
# لإنشاء ملفات PDF (اختياري)
# reportlab==4.0.4

# For Excel export (optional)
# لتصدير Excel (اختياري)
# openpyxl==3.1.2

# For image handling (optional)
# للتعامل مع الصور (اختياري)
# pillow==10.0.0

# For QR code generation (optional)
# لإنشاء رموز QR (اختياري)
# qrcode==7.4.2

# Uncomment the packages above if you want to install them
# قم بإلغاء التعليق على الحزم أعلاه إذا كنت تريد تثبيتها
