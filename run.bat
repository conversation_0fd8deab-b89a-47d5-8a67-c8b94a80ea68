@echo off
REM ملف تشغيل برنامج الميزان
REM Al-Mezan Business Management System Launcher

echo ========================================
echo    برنامج الميزان لإدارة المحال التجارية
echo    Al-Mezan Business Management System
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed on the system
    echo.
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo Please install Python 3.8 or newer from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo تم العثور على Python...
echo Python found...
echo.

REM التحقق من وجود الملف الرئيسي
if not exist "main.py" (
    echo خطأ: الملف الرئيسي main.py غير موجود
    echo Error: main.py file not found
    echo.
    echo تأكد من وجود الملف في نفس مجلد هذا الملف
    echo Make sure the file exists in the same folder as this file
    echo.
    pause
    exit /b 1
)

echo بدء تشغيل برنامج الميزان...
echo Starting Al-Mezan...
echo.

REM تشغيل البرنامج
python main.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo An error occurred while running the program
    echo.
    echo تحقق من:
    echo Check:
    echo 1. تثبيت Python بشكل صحيح
    echo    Python is installed correctly
    echo 2. وجود جميع الملفات المطلوبة
    echo    All required files exist
    echo 3. صلاحيات الكتابة في المجلد
    echo    Write permissions in the folder
    echo.
)

pause
