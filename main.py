#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج الميزان - نظام إدارة المحال التجارية
Al-Mezan - Business Management System

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025
الإصدار: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# إضافة مجلد المشروع إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.gui.modern_main_window import ModernMainWindow
from src.gui.login_window import LoginWindow
from src.utils.config import Config
from src.models.user import UserManager
from src.utils.backup_manager import BackupManager

class AlMezanApp:
    """الفئة الرئيسية لتطبيق الميزان"""

    def __init__(self):
        self.config = Config()
        self.db_manager = DatabaseManager()
        self.user_manager = UserManager(self.db_manager)
        self.current_user = None
        self.main_window = None
        
        # إنشاء قاعدة البيانات
        self.initialize_database()

        # بدء تسجيل الدخول
        self.start_login()
    
    def start_login(self):
        """بدء عملية تسجيل الدخول"""
        try:
            # إنشاء نافذة تسجيل الدخول
            login_window = LoginWindow(self.db_manager, self.on_login_success)
            login_window.run()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تسجيل الدخول: {str(e)}")

    def on_login_success(self, user):
        """عند نجاح تسجيل الدخول"""
        self.current_user = user

        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.setup_main_window()

        # إنشاء الواجهة الرئيسية الحديثة
        self.main_window = ModernMainWindow(self.root, self.db_manager, self.config, self.current_user)

        # بدء النسخ الاحتياطي التلقائي
        self.start_automatic_backup()

        # تشغيل النافذة الرئيسية
        self.root.mainloop()

    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("الميزان - نظام إدارة المحال التجارية")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # تعيين الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass

        # تعيين الخط العربي
        self.root.option_add('*Font', 'Arial 10')

        # تمركز النافذة في الشاشة
        self.center_window()

    def center_window(self):
        """تمركز النافذة في وسط الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def start_automatic_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        try:
            backup_manager = BackupManager(self.db_manager, self.config)
            backup_manager.start_automatic_backup()
            print("تم بدء النسخ الاحتياطي التلقائي")
        except Exception as e:
            print(f"فشل في بدء النسخ الاحتياطي التلقائي: {e}")

    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.db_manager.create_tables()
            print("تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء قاعدة البيانات: {str(e)}")
            sys.exit(1)
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # لا نحتاج لتشغيل mainloop هنا لأنه يتم في نافذة تسجيل الدخول
            pass
        except KeyboardInterrupt:
            self.close_app()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def close_app(self):
        """إغلاق التطبيق"""
        if messagebox.askokcancel("إغلاق", "هل تريد إغلاق البرنامج؟"):
            self.db_manager.close()
            self.root.destroy()

def main():
    """الدالة الرئيسية"""
    try:
        app = AlMezanApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق: {str(e)}")

if __name__ == "__main__":
    main()
