#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج الميزان - نظام إدارة المحال التجارية
Al-Mezan - Business Management System

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025
الإصدار: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# إضافة مجلد المشروع إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.gui.main_window import MainWindow
from src.utils.config import Config

class AlMezanApp:
    """الفئة الرئيسية لتطبيق الميزان"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.config = Config()
        self.db_manager = DatabaseManager()
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # إنشاء قاعدة البيانات
        self.initialize_database()
        
        # إنشاء الواجهة الرئيسية
        self.main_window = MainWindow(self.root, self.db_manager, self.config)
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("الميزان - نظام إدارة المحال التجارية")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # تعيين الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
        
        # تعيين الخط العربي
        self.root.option_add('*Font', 'Arial 10')
        
        # تمركز النافذة في الشاشة
        self.center_window()
    
    def center_window(self):
        """تمركز النافذة في وسط الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.db_manager.create_tables()
            print("تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء قاعدة البيانات: {str(e)}")
            sys.exit(1)
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.close_app()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def close_app(self):
        """إغلاق التطبيق"""
        if messagebox.askokcancel("إغلاق", "هل تريد إغلاق البرنامج؟"):
            self.db_manager.close()
            self.root.destroy()

def main():
    """الدالة الرئيسية"""
    try:
        app = AlMezanApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق: {str(e)}")

if __name__ == "__main__":
    main()
