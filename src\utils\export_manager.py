# -*- coding: utf-8 -*-
"""
مدير تصدير البيانات
Data Export Manager
"""

import csv
import json
import os
from datetime import datetime
from typing import List, Dict, Optional
import tkinter as tk
from tkinter import filedialog, messagebox

class ExportManager:
    """مدير تصدير البيانات"""
    
    def __init__(self, db_manager):
        """تهيئة مدير التصدير"""
        self.db = db_manager
        self.export_formats = ['CSV', 'JSON', 'Excel']
    
    def export_products(self, format_type: str = 'CSV', file_path: str = None) -> Dict:
        """تصدير بيانات المنتجات"""
        try:
            # جلب بيانات المنتجات
            query = """
            SELECT p.*, c.name as category_name 
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY p.name
            """
            products = self.db.fetch_all(query)
            
            if not products:
                return {"success": False, "message": "لا توجد منتجات للتصدير"}
            
            # تحديد مسار الملف إذا لم يتم تحديده
            if not file_path:
                file_path = self._get_export_path("products", format_type)
                if not file_path:
                    return {"success": False, "message": "تم إلغاء العملية"}
            
            # تصدير البيانات حسب النوع
            if format_type.upper() == 'CSV':
                return self._export_to_csv(products, file_path, "المنتجات")
            elif format_type.upper() == 'JSON':
                return self._export_to_json(products, file_path, "المنتجات")
            elif format_type.upper() == 'EXCEL':
                return self._export_to_excel(products, file_path, "المنتجات")
            else:
                return {"success": False, "message": "نوع التصدير غير مدعوم"}
                
        except Exception as e:
            return {"success": False, "message": f"فشل في تصدير المنتجات: {str(e)}"}
    
    def export_customers(self, format_type: str = 'CSV', file_path: str = None) -> Dict:
        """تصدير بيانات العملاء"""
        try:
            query = "SELECT * FROM customers ORDER BY name"
            customers = self.db.fetch_all(query)
            
            if not customers:
                return {"success": False, "message": "لا توجد عملاء للتصدير"}
            
            if not file_path:
                file_path = self._get_export_path("customers", format_type)
                if not file_path:
                    return {"success": False, "message": "تم إلغاء العملية"}
            
            if format_type.upper() == 'CSV':
                return self._export_to_csv(customers, file_path, "العملاء")
            elif format_type.upper() == 'JSON':
                return self._export_to_json(customers, file_path, "العملاء")
            elif format_type.upper() == 'EXCEL':
                return self._export_to_excel(customers, file_path, "العملاء")
            else:
                return {"success": False, "message": "نوع التصدير غير مدعوم"}
                
        except Exception as e:
            return {"success": False, "message": f"فشل في تصدير العملاء: {str(e)}"}
    
    def export_suppliers(self, format_type: str = 'CSV', file_path: str = None) -> Dict:
        """تصدير بيانات الموردين"""
        try:
            query = "SELECT * FROM suppliers ORDER BY name"
            suppliers = self.db.fetch_all(query)
            
            if not suppliers:
                return {"success": False, "message": "لا توجد موردين للتصدير"}
            
            if not file_path:
                file_path = self._get_export_path("suppliers", format_type)
                if not file_path:
                    return {"success": False, "message": "تم إلغاء العملية"}
            
            if format_type.upper() == 'CSV':
                return self._export_to_csv(suppliers, file_path, "الموردين")
            elif format_type.upper() == 'JSON':
                return self._export_to_json(suppliers, file_path, "الموردين")
            elif format_type.upper() == 'EXCEL':
                return self._export_to_excel(suppliers, file_path, "الموردين")
            else:
                return {"success": False, "message": "نوع التصدير غير مدعوم"}
                
        except Exception as e:
            return {"success": False, "message": f"فشل في تصدير الموردين: {str(e)}"}
    
    def export_invoices(self, format_type: str = 'CSV', file_path: str = None, 
                       start_date: str = None, end_date: str = None) -> Dict:
        """تصدير بيانات الفواتير"""
        try:
            query = """
            SELECT i.*, c.name as customer_name, s.name as supplier_name, u.full_name as user_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            LEFT JOIN suppliers s ON i.supplier_id = s.id
            LEFT JOIN users u ON i.user_id = u.id
            WHERE 1=1
            """
            params = []
            
            if start_date:
                query += " AND i.invoice_date >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND i.invoice_date <= ?"
                params.append(end_date)
            
            query += " ORDER BY i.invoice_date DESC"
            
            invoices = self.db.fetch_all(query, params)
            
            if not invoices:
                return {"success": False, "message": "لا توجد فواتير للتصدير"}
            
            if not file_path:
                file_path = self._get_export_path("invoices", format_type)
                if not file_path:
                    return {"success": False, "message": "تم إلغاء العملية"}
            
            if format_type.upper() == 'CSV':
                return self._export_to_csv(invoices, file_path, "الفواتير")
            elif format_type.upper() == 'JSON':
                return self._export_to_json(invoices, file_path, "الفواتير")
            elif format_type.upper() == 'EXCEL':
                return self._export_to_excel(invoices, file_path, "الفواتير")
            else:
                return {"success": False, "message": "نوع التصدير غير مدعوم"}
                
        except Exception as e:
            return {"success": False, "message": f"فشل في تصدير الفواتير: {str(e)}"}
    
    def export_all_data(self, format_type: str = 'JSON', export_dir: str = None) -> Dict:
        """تصدير جميع البيانات"""
        try:
            if not export_dir:
                export_dir = filedialog.askdirectory(title="اختر مجلد التصدير")
                if not export_dir:
                    return {"success": False, "message": "تم إلغاء العملية"}
            
            # إنشاء مجلد فرعي بالتاريخ
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_folder = os.path.join(export_dir, f"almezan_export_{timestamp}")
            os.makedirs(export_folder, exist_ok=True)
            
            results = []
            
            # تصدير المنتجات
            products_path = os.path.join(export_folder, f"products.{format_type.lower()}")
            result = self.export_products(format_type, products_path)
            results.append(("المنتجات", result))
            
            # تصدير العملاء
            customers_path = os.path.join(export_folder, f"customers.{format_type.lower()}")
            result = self.export_customers(format_type, customers_path)
            results.append(("العملاء", result))
            
            # تصدير الموردين
            suppliers_path = os.path.join(export_folder, f"suppliers.{format_type.lower()}")
            result = self.export_suppliers(format_type, suppliers_path)
            results.append(("الموردين", result))
            
            # تصدير الفواتير
            invoices_path = os.path.join(export_folder, f"invoices.{format_type.lower()}")
            result = self.export_invoices(format_type, invoices_path)
            results.append(("الفواتير", result))
            
            # تصدير الفئات
            categories_path = os.path.join(export_folder, f"categories.{format_type.lower()}")
            categories = self.db.fetch_all("SELECT * FROM categories ORDER BY name")
            if categories:
                if format_type.upper() == 'CSV':
                    result = self._export_to_csv(categories, categories_path, "الفئات")
                elif format_type.upper() == 'JSON':
                    result = self._export_to_json(categories, categories_path, "الفئات")
                elif format_type.upper() == 'EXCEL':
                    result = self._export_to_excel(categories, categories_path, "الفئات")
                results.append(("الفئات", result))
            
            # إنشاء ملف معلومات التصدير
            export_info = {
                "export_date": datetime.now().isoformat(),
                "export_format": format_type,
                "database_stats": self.db.get_database_stats(),
                "results": [{"table": name, "success": res["success"], "message": res.get("message", "")} 
                           for name, res in results]
            }
            
            info_path = os.path.join(export_folder, "export_info.json")
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(export_info, f, ensure_ascii=False, indent=2)
            
            # حساب النتائج
            successful = sum(1 for _, result in results if result["success"])
            total = len(results)
            
            return {
                "success": True,
                "message": f"تم تصدير {successful} من {total} جداول بنجاح",
                "export_folder": export_folder,
                "results": results
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في تصدير البيانات: {str(e)}"}
    
    def _get_export_path(self, data_type: str, format_type: str) -> Optional[str]:
        """الحصول على مسار ملف التصدير"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_name = f"{data_type}_{timestamp}.{format_type.lower()}"
        
        filetypes = []
        if format_type.upper() == 'CSV':
            filetypes = [("ملفات CSV", "*.csv"), ("جميع الملفات", "*.*")]
        elif format_type.upper() == 'JSON':
            filetypes = [("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        elif format_type.upper() == 'EXCEL':
            filetypes = [("ملفات Excel", "*.xlsx"), ("جميع الملفات", "*.*")]
        
        return filedialog.asksaveasfilename(
            title=f"حفظ ملف {data_type}",
            defaultextension=f".{format_type.lower()}",
            filetypes=filetypes,
            initialvalue=default_name
        )
    
    def _export_to_csv(self, data: List[Dict], file_path: str, data_name: str) -> Dict:
        """تصدير البيانات إلى CSV"""
        try:
            if not data:
                return {"success": False, "message": f"لا توجد بيانات {data_name} للتصدير"}
            
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # كتابة العناوين
                writer.writeheader()
                
                # كتابة البيانات
                for row in data:
                    # تحويل القيم None إلى نص فارغ
                    clean_row = {k: (v if v is not None else '') for k, v in row.items()}
                    writer.writerow(clean_row)
            
            return {
                "success": True,
                "message": f"تم تصدير {data_name} إلى CSV بنجاح",
                "file_path": file_path,
                "records_count": len(data)
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في تصدير {data_name} إلى CSV: {str(e)}"}
    
    def _export_to_json(self, data: List[Dict], file_path: str, data_name: str) -> Dict:
        """تصدير البيانات إلى JSON"""
        try:
            if not data:
                return {"success": False, "message": f"لا توجد بيانات {data_name} للتصدير"}
            
            export_data = {
                "export_info": {
                    "data_type": data_name,
                    "export_date": datetime.now().isoformat(),
                    "records_count": len(data)
                },
                "data": data
            }
            
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2, default=str)
            
            return {
                "success": True,
                "message": f"تم تصدير {data_name} إلى JSON بنجاح",
                "file_path": file_path,
                "records_count": len(data)
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في تصدير {data_name} إلى JSON: {str(e)}"}
    
    def _export_to_excel(self, data: List[Dict], file_path: str, data_name: str) -> Dict:
        """تصدير البيانات إلى Excel"""
        try:
            # محاولة استيراد openpyxl
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill, Alignment
            except ImportError:
                return {
                    "success": False, 
                    "message": "مكتبة openpyxl غير مثبتة. يرجى تثبيتها أولاً: pip install openpyxl"
                }
            
            if not data:
                return {"success": False, "message": f"لا توجد بيانات {data_name} للتصدير"}
            
            # إنشاء ملف Excel
            wb = Workbook()
            ws = wb.active
            ws.title = data_name
            
            # إعداد الأنماط
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # كتابة العناوين
            headers = list(data[0].keys())
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # كتابة البيانات
            for row_idx, row_data in enumerate(data, 2):
                for col_idx, header in enumerate(headers, 1):
                    value = row_data.get(header)
                    if value is None:
                        value = ""
                    ws.cell(row=row_idx, column=col_idx, value=value)
            
            # تعديل عرض الأعمدة
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # حفظ الملف
            wb.save(file_path)
            
            return {
                "success": True,
                "message": f"تم تصدير {data_name} إلى Excel بنجاح",
                "file_path": file_path,
                "records_count": len(data)
            }
            
        except Exception as e:
            return {"success": False, "message": f"فشل في تصدير {data_name} إلى Excel: {str(e)}"}
    
    def get_export_formats(self) -> List[str]:
        """الحصول على أنواع التصدير المدعومة"""
        return self.export_formats.copy()
    
    def validate_export_data(self, data: List[Dict]) -> bool:
        """التحقق من صحة البيانات للتصدير"""
        if not data:
            return False
        
        if not isinstance(data, list):
            return False
        
        if not all(isinstance(item, dict) for item in data):
            return False
        
        return True
