# -*- coding: utf-8 -*-
"""
نافذة إدارة العملاء
Customers Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from ..models.customer import Customer, CustomerManager

class CustomersWindow:
    """نافذة إدارة العملاء"""
    
    def __init__(self, parent, db_manager, config):
        """
        تهيئة نافذة العملاء
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.customer_manager = CustomerManager(db_manager)
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة العملاء")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        # متغيرات
        self.customers = []
        self.selected_customer = None
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        # إعداد الواجهة
        self.setup_ui()
        self.load_customers()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إطار البحث والأزرار
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # البحث
        search_frame = ttk.Frame(top_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT, padx=(0, 5))
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(top_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="إضافة عميل", command=self.add_customer).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="تعديل", command=self.edit_customer).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="حذف", command=self.delete_customer).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="تحديث", command=self.load_customers).pack(side=tk.LEFT, padx=2)
        
        # جدول العملاء
        self.create_customers_table(main_frame)
        
        # إطار التفاصيل
        details_frame = ttk.LabelFrame(main_frame, text="تفاصيل العميل", padding=10)
        details_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.create_customer_details(details_frame)
    
    def create_customers_table(self, parent):
        """إنشاء جدول العملاء"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # أعمدة الجدول
        columns = ('code', 'name', 'phone', 'email', 'city', 'credit_limit', 'balance', 'status')
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        self.tree.heading('code', text='الكود')
        self.tree.heading('name', text='اسم العميل')
        self.tree.heading('phone', text='الهاتف')
        self.tree.heading('email', text='البريد الإلكتروني')
        self.tree.heading('city', text='المدينة')
        self.tree.heading('credit_limit', text='حد الائتمان')
        self.tree.heading('balance', text='الرصيد الحالي')
        self.tree.heading('status', text='حالة الائتمان')
        
        # تعيين عرض الأعمدة
        self.tree.column('code', width=80)
        self.tree.column('name', width=150)
        self.tree.column('phone', width=100)
        self.tree.column('email', width=150)
        self.tree.column('city', width=100)
        self.tree.column('credit_limit', width=100)
        self.tree.column('balance', width=100)
        self.tree.column('status', width=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tree.bind('<<TreeviewSelect>>', self.on_customer_select)
        self.tree.bind('<Double-1>', self.edit_customer)
    
    def create_customer_details(self, parent):
        """إنشاء قسم تفاصيل العميل"""
        # إطار المعلومات الأساسية
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X)
        
        # الصف الأول
        row1 = ttk.Frame(info_frame)
        row1.pack(fill=tk.X, pady=2)
        
        ttk.Label(row1, text="الكود:", width=15).pack(side=tk.LEFT)
        self.code_label = ttk.Label(row1, text="-", foreground="blue")
        self.code_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row1, text="الاسم:", width=15).pack(side=tk.LEFT)
        self.name_label = ttk.Label(row1, text="-")
        self.name_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # الصف الثاني
        row2 = ttk.Frame(info_frame)
        row2.pack(fill=tk.X, pady=2)
        
        ttk.Label(row2, text="الهاتف:", width=15).pack(side=tk.LEFT)
        self.phone_label = ttk.Label(row2, text="-")
        self.phone_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row2, text="البريد الإلكتروني:", width=15).pack(side=tk.LEFT)
        self.email_label = ttk.Label(row2, text="-")
        self.email_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # الصف الثالث
        row3 = ttk.Frame(info_frame)
        row3.pack(fill=tk.X, pady=2)
        
        ttk.Label(row3, text="العنوان:", width=15).pack(side=tk.LEFT)
        self.address_label = ttk.Label(row3, text="-")
        self.address_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # الصف الرابع
        row4 = ttk.Frame(info_frame)
        row4.pack(fill=tk.X, pady=2)
        
        ttk.Label(row4, text="حد الائتمان:", width=15).pack(side=tk.LEFT)
        self.credit_label = ttk.Label(row4, text="-")
        self.credit_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row4, text="الرصيد الحالي:", width=15).pack(side=tk.LEFT)
        self.balance_label = ttk.Label(row4, text="-")
        self.balance_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row4, text="الائتمان المتاح:", width=15).pack(side=tk.LEFT)
        self.available_label = ttk.Label(row4, text="-")
        self.available_label.pack(side=tk.LEFT, padx=(0, 20))
    
    def load_customers(self):
        """تحميل العملاء"""
        try:
            # مسح الجدول
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # جلب العملاء من قاعدة البيانات
            self.customers = self.customer_manager.get_all_customers()
            
            # إضافة العملاء للجدول
            for customer in self.customers:
                self.tree.insert('', tk.END, values=(
                    customer.code,
                    customer.name,
                    customer.phone or '-',
                    customer.email or '-',
                    customer.city or '-',
                    f"{customer.credit_limit:.2f}",
                    f"{customer.current_balance:.2f}",
                    customer.credit_status
                ))
            
            # تحديث شريط الحالة
            self.update_status(f"تم تحميل {len(self.customers)} عميل")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العملاء: {str(e)}")
    
    def on_search_change(self, *args):
        """البحث في العملاء"""
        search_term = self.search_var.get().lower()
        
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # تصفية العملاء
        filtered_customers = []
        for customer in self.customers:
            if (search_term in customer.name.lower() or 
                search_term in customer.code.lower() or
                search_term in (customer.phone or '').lower() or
                search_term in (customer.email or '').lower()):
                filtered_customers.append(customer)
        
        # إضافة العملاء المفلترين
        for customer in filtered_customers:
            self.tree.insert('', tk.END, values=(
                customer.code,
                customer.name,
                customer.phone or '-',
                customer.email or '-',
                customer.city or '-',
                f"{customer.credit_limit:.2f}",
                f"{customer.current_balance:.2f}",
                customer.credit_status
            ))
    
    def on_customer_select(self, event):
        """عند اختيار عميل"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            customer_code = item['values'][0]
            
            # البحث عن العميل
            self.selected_customer = None
            for customer in self.customers:
                if customer.code == customer_code:
                    self.selected_customer = customer
                    break
            
            # تحديث التفاصيل
            self.update_customer_details()
    
    def update_customer_details(self):
        """تحديث تفاصيل العميل"""
        if self.selected_customer:
            self.code_label.config(text=self.selected_customer.code)
            self.name_label.config(text=self.selected_customer.name)
            self.phone_label.config(text=self.selected_customer.phone or '-')
            self.email_label.config(text=self.selected_customer.email or '-')
            self.address_label.config(text=self.selected_customer.address or '-')
            self.credit_label.config(text=f"{self.selected_customer.credit_limit:.2f} ج.م")
            self.balance_label.config(text=f"{self.selected_customer.current_balance:.2f} ج.م")
            self.available_label.config(text=f"{self.selected_customer.available_credit:.2f} ج.م")
        else:
            # مسح التفاصيل
            for label in [self.code_label, self.name_label, self.phone_label, 
                         self.email_label, self.address_label, self.credit_label, 
                         self.balance_label, self.available_label]:
                label.config(text="-")
    
    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self.window, self.db, self.config)
        if dialog.result:
            self.load_customers()
    
    def edit_customer(self):
        """تعديل عميل"""
        if not self.selected_customer:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return
        
        dialog = CustomerDialog(self.window, self.db, self.config, self.selected_customer)
        if dialog.result:
            self.load_customers()
    
    def delete_customer(self):
        """حذف عميل"""
        if not self.selected_customer:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", 
                             f"هل تريد حذف العميل '{self.selected_customer.name}'؟"):
            try:
                # حذف العميل (تعطيل بدلاً من الحذف الفعلي)
                query = "UPDATE customers SET is_active = 0 WHERE id = ?"
                self.db.execute_query(query, (self.selected_customer.id,))
                
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.load_customers()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف العميل: {str(e)}")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        # يمكن إضافة شريط حالة هنا
        pass

class CustomerDialog:
    """حوار إضافة/تعديل عميل"""
    
    def __init__(self, parent, db_manager, config, customer=None):
        """
        تهيئة حوار العميل
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
            customer: العميل للتعديل (None للإضافة)
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.customer = customer
        self.result = None
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إضافة عميل" if customer is None else "تعديل عميل")
        self.window.geometry("500x500")
        self.window.transient(parent)
        self.window.grab_set()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات إذا كان تعديل
        if self.customer:
            self.load_customer_data()
        
        # تشغيل النافذة
        self.window.wait_window()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.code_var = tk.StringVar()
        self.name_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.city_var = tk.StringVar()
        self.credit_limit_var = tk.DoubleVar()
        self.current_balance_var = tk.DoubleVar()
        self.notes_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar(value=True)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إطار النموذج
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.BOTH, expand=True)
        
        # الحقول
        row = 0
        
        # كود العميل
        ttk.Label(form_frame, text="كود العميل *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.code_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # اسم العميل
        ttk.Label(form_frame, text="اسم العميل *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.name_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الهاتف
        ttk.Label(form_frame, text="الهاتف:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.phone_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # البريد الإلكتروني
        ttk.Label(form_frame, text="البريد الإلكتروني:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.email_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # العنوان
        ttk.Label(form_frame, text="العنوان:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.address_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # المدينة
        ttk.Label(form_frame, text="المدينة:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.city_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # حد الائتمان
        ttk.Label(form_frame, text="حد الائتمان:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.credit_limit_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الرصيد الحالي
        ttk.Label(form_frame, text="الرصيد الحالي:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.current_balance_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الملاحظات
        ttk.Label(form_frame, text="الملاحظات:").grid(row=row, column=0, sticky=tk.W, pady=5)
        notes_text = tk.Text(form_frame, width=30, height=3)
        notes_text.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # نشط
        ttk.Checkbutton(form_frame, text="عميل نشط", variable=self.is_active_var).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_customer).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.RIGHT)
        
        # حفظ مرجع للنص
        self.notes_text = notes_text
    
    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        if self.customer:
            self.code_var.set(self.customer.code)
            self.name_var.set(self.customer.name)
            self.phone_var.set(self.customer.phone)
            self.email_var.set(self.customer.email)
            self.address_var.set(self.customer.address)
            self.city_var.set(self.customer.city)
            self.credit_limit_var.set(self.customer.credit_limit)
            self.current_balance_var.set(self.customer.current_balance)
            self.notes_text.insert('1.0', self.customer.notes)
            self.is_active_var.set(self.customer.is_active)
    
    def save_customer(self):
        """حفظ العميل"""
        try:
            # إنشاء كائن العميل
            customer = Customer(
                id=self.customer.id if self.customer else None,
                code=self.code_var.get().strip(),
                name=self.name_var.get().strip(),
                phone=self.phone_var.get().strip(),
                email=self.email_var.get().strip(),
                address=self.address_var.get().strip(),
                city=self.city_var.get().strip(),
                credit_limit=self.credit_limit_var.get(),
                current_balance=self.current_balance_var.get(),
                notes=self.notes_text.get('1.0', tk.END).strip(),
                is_active=self.is_active_var.get()
            )
            
            # التحقق من صحة البيانات
            errors = customer.validate()
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return
            
            # حفظ في قاعدة البيانات
            customer_manager = CustomerManager(self.db)
            
            if self.customer:  # تعديل
                query = """
                    UPDATE customers SET 
                        code = ?, name = ?, phone = ?, email = ?, address = ?,
                        city = ?, credit_limit = ?, current_balance = ?, notes = ?,
                        is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """
                self.db.execute_query(query, (
                    customer.code, customer.name, customer.phone, customer.email,
                    customer.address, customer.city, customer.credit_limit,
                    customer.current_balance, customer.notes, customer.is_active,
                    self.customer.id
                ))
                messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
            else:  # إضافة
                customer_id = customer_manager.create_customer(customer)
                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ العميل: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.window.destroy()
