# -*- coding: utf-8 -*-
"""
نافذة إدارة العملاء
Customers Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from ..models.customer import Customer, CustomerManager
from ..utils.colors import AppColors

class CustomersWindow:
    """نافذة إدارة العملاء"""
    
    def __init__(self, parent, db_manager, config):
        """
        تهيئة نافذة العملاء
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.customer_manager = CustomerManager(db_manager)
        
        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("🧑‍🤝‍🧑 إدارة العملاء - الميزان")
        self.window.geometry("1200x800")
        self.window.configure(bg=self.colors['background'])
        self.window.transient(parent)
        self.window.grab_set()
        
        # تحسين مظهر النافذة
        self.window.resizable(True, True)
        self.window.minsize(1000, 600)
        
        # متغيرات
        self.customers = []
        self.selected_customer = None
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        # إعداد الواجهة
        self.setup_ui()
        self.load_customers()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي مع خلفية محسنة
        main_frame = tk.Frame(self.window, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إنشاء شريط العنوان
        self.create_header(main_frame)
        
        # إطار البحث والأزرار مع تصميم محسن
        top_frame = tk.Frame(main_frame, bg=self.colors['background'])
        top_frame.pack(fill=tk.X, pady=(20, 15))
        
        # البحث مع تصميم محسن
        search_frame = tk.Frame(top_frame, bg=self.colors['background'])
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        search_label = tk.Label(search_frame, text="🔍 البحث:", 
                               font=('Segoe UI', 12, 'bold'), 
                               fg=self.colors['text'], bg=self.colors['background'])
        search_label.pack(side=tk.LEFT, padx=(0, 10))
        
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=('Segoe UI', 12), width=25,
                               relief='flat', bd=0, bg=self.colors['surface'],
                               fg=self.colors['text'], insertbackground=self.colors['text'])
        search_entry.pack(side=tk.LEFT, padx=(0, 10), ipady=8)
        
        # أزرار العمليات مع تصميم محسن
        buttons_frame = tk.Frame(top_frame, bg=self.colors['background'])
        buttons_frame.pack(side=tk.RIGHT)
        
        # زر إضافة عميل
        add_btn = tk.Button(buttons_frame, text="➕ إضافة عميل", 
                           font=('Segoe UI', 11, 'bold'),
                           bg=self.colors['success'], fg=self.colors['text_white'], relief='flat',
                           bd=0, cursor='hand2', command=self.add_customer)
        add_btn.pack(side=tk.LEFT, padx=5, ipady=8, ipadx=15)
        
        # زر تعديل
        edit_btn = tk.Button(buttons_frame, text="✏️ تعديل", 
                            font=('Segoe UI', 11, 'bold'),
                            bg=self.colors['info'], fg=self.colors['text_white'], relief='flat',
                            bd=0, cursor='hand2', command=self.edit_customer)
        edit_btn.pack(side=tk.LEFT, padx=5, ipady=8, ipadx=15)
        
        # زر حذف
        delete_btn = tk.Button(buttons_frame, text="🗑️ حذف", 
                              font=('Segoe UI', 11, 'bold'),
                              bg=self.colors['danger'], fg=self.colors['text_white'], relief='flat',
                              bd=0, cursor='hand2', command=self.delete_customer)
        delete_btn.pack(side=tk.LEFT, padx=5, ipady=8, ipadx=15)
        
        # زر تحديث
        refresh_btn = tk.Button(buttons_frame, text="🔄 تحديث", 
                               font=('Segoe UI', 11, 'bold'),
                               bg=self.colors['secondary'], fg=self.colors['text_white'], relief='flat',
                               bd=0, cursor='hand2', command=self.load_customers)
        refresh_btn.pack(side=tk.LEFT, padx=5, ipady=8, ipadx=15)
        
        # تأثيرات التفاعل للأزرار
        add_btn.bind('<Enter>', lambda e: add_btn.config(bg=self.colors['hover_success']))
        add_btn.bind('<Leave>', lambda e: add_btn.config(bg=self.colors['success']))
        edit_btn.bind('<Enter>', lambda e: edit_btn.config(bg=self.colors['hover_info']))
        edit_btn.bind('<Leave>', lambda e: edit_btn.config(bg=self.colors['info']))
        delete_btn.bind('<Enter>', lambda e: delete_btn.config(bg=self.colors['hover_danger']))
        delete_btn.bind('<Leave>', lambda e: delete_btn.config(bg=self.colors['danger']))
        
        # جدول العملاء مع تصميم محسن
        self.create_customers_table(main_frame)
        
        # إطار التفاصيل مع تصميم محسن
        details_frame = tk.LabelFrame(main_frame, text="📋 تفاصيل العميل المحدد", 
                                     font=('Segoe UI', 12, 'bold'),
                                     fg=self.colors['text'], bg=self.colors['card'],
                                     relief='flat', bd=2)
        details_frame.pack(fill=tk.X, pady=(15, 0), padx=5)
        
        # إنشاء تفاصيل العميل
        self.create_customer_details(details_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(parent, bg='#2c3e50', height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # أيقونة ونص العنوان
        title_frame = tk.Frame(header_frame, bg='#2c3e50')
        title_frame.pack(expand=True, fill=tk.BOTH)
        
        icon_label = tk.Label(title_frame, text="🧑‍🤝‍🧑", 
                             font=('Segoe UI', 24), 
                             fg='white', bg='#2c3e50')
        icon_label.pack(side=tk.LEFT, padx=(20, 10), pady=20)
        
        title_label = tk.Label(title_frame, text="إدارة العملاء", 
                              font=('Segoe UI', 18, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(side=tk.LEFT, pady=20)
        
        subtitle_label = tk.Label(title_frame, text="إضافة وتعديل وإدارة بيانات العملاء", 
                                 font=('Segoe UI', 11), 
                                 fg='#bdc3c7', bg='#2c3e50')
        subtitle_label.pack(side=tk.LEFT, padx=(20, 0), pady=20)
    
    def create_customers_table(self, parent):
        """إنشاء جدول العملاء مع تصميم محسن"""
        # إطار الجدول مع خلفية
        table_container = tk.Frame(parent, bg='#f8f9fa')
        table_container.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # عنوان الجدول
        table_header = tk.Frame(table_container, bg='#34495e', height=40)
        table_header.pack(fill=tk.X)
        table_header.pack_propagate(False)
        
        header_label = tk.Label(table_header, text="📊 قائمة العملاء", 
                               font=('Segoe UI', 14, 'bold'),
                               fg='white', bg='#34495e')
        header_label.pack(side=tk.LEFT, padx=15, pady=10)
        
        # إطار الجدول الفعلي
        table_frame = tk.Frame(table_container, bg='white', relief='flat', bd=1)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # أعمدة الجدول
        columns = ('code', 'name', 'phone', 'email', 'city', 'credit_limit', 'balance', 'status')
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        # تحسين مظهر الجدول
        style = ttk.Style()
        style.configure("Treeview", 
                       background="white",
                       foreground="#2c3e50",
                       fieldbackground="white",
                       font=('Segoe UI', 10))
        style.configure("Treeview.Heading", 
                       background="#ecf0f1",
                       foreground="#2c3e50",
                       font=('Segoe UI', 11, 'bold'))
        
        # تعيين عناوين الأعمدة مع رموز
        self.tree.heading('code', text='🆔 الكود')
        self.tree.heading('name', text='👤 اسم العميل')
        self.tree.heading('phone', text='📞 الهاتف')
        self.tree.heading('email', text='📧 البريد الإلكتروني')
        self.tree.heading('city', text='🏙️ المدينة')
        self.tree.heading('credit_limit', text='💳 حد الائتمان')
        self.tree.heading('balance', text='💰 الرصيد الحالي')
        self.tree.heading('status', text='📊 حالة الائتمان')
        
        # تعيين عرض الأعمدة المحسن
        self.tree.column('code', width=100, anchor='center')
        self.tree.column('name', width=180, anchor='w')
        self.tree.column('phone', width=120, anchor='center')
        self.tree.column('email', width=200, anchor='w')
        self.tree.column('city', width=120, anchor='center')
        self.tree.column('credit_limit', width=120, anchor='e')
        self.tree.column('balance', width=120, anchor='e')
        self.tree.column('status', width=140, anchor='center')
        
        # شريط التمرير المحسن
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.tree.bind('<<TreeviewSelect>>', self.on_customer_select)
        self.tree.bind('<Double-1>', self.edit_customer)
    
    def create_customer_details(self, parent):
        """إنشاء قسم تفاصيل العميل مع تصميم محسن"""
        # إطار المعلومات الأساسية مع خلفية
        info_frame = tk.Frame(parent, bg='white', relief='flat', bd=1)
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # الصف الأول - المعلومات الأساسية
        row1 = tk.Frame(info_frame, bg='white')
        row1.pack(fill=tk.X, pady=8, padx=15)
        
        # الكود
        code_frame = tk.Frame(row1, bg='white')
        code_frame.pack(side=tk.LEFT, padx=(0, 30))
        tk.Label(code_frame, text="🆔 الكود:", font=('Segoe UI', 11, 'bold'), 
                fg='#2c3e50', bg='white').pack(anchor='w')
        self.code_label = tk.Label(code_frame, text="-", font=('Segoe UI', 12), 
                                  fg='#3498db', bg='white')
        self.code_label.pack(anchor='w')
        
        # الاسم
        name_frame = tk.Frame(row1, bg='white')
        name_frame.pack(side=tk.LEFT, padx=(0, 30))
        tk.Label(name_frame, text="👤 الاسم:", font=('Segoe UI', 11, 'bold'), 
                fg='#2c3e50', bg='white').pack(anchor='w')
        self.name_label = tk.Label(name_frame, text="-", font=('Segoe UI', 12), 
                                  fg='#2c3e50', bg='white')
        self.name_label.pack(anchor='w')
        
        # الصف الثاني - معلومات الاتصال
        row2 = tk.Frame(info_frame, bg='white')
        row2.pack(fill=tk.X, pady=8, padx=15)
        
        # الهاتف
        phone_frame = tk.Frame(row2, bg='white')
        phone_frame.pack(side=tk.LEFT, padx=(0, 30))
        tk.Label(phone_frame, text="📞 الهاتف:", font=('Segoe UI', 11, 'bold'), 
                fg='#2c3e50', bg='white').pack(anchor='w')
        self.phone_label = tk.Label(phone_frame, text="-", font=('Segoe UI', 12), 
                                   fg='#2c3e50', bg='white')
        self.phone_label.pack(anchor='w')
        
        # البريد الإلكتروني
        email_frame = tk.Frame(row2, bg='white')
        email_frame.pack(side=tk.LEFT, padx=(0, 30))
        tk.Label(email_frame, text="📧 البريد الإلكتروني:", font=('Segoe UI', 11, 'bold'), 
                fg='#2c3e50', bg='white').pack(anchor='w')
        self.email_label = tk.Label(email_frame, text="-", font=('Segoe UI', 12), 
                                   fg='#2c3e50', bg='white')
        self.email_label.pack(anchor='w')
        
        # الصف الثالث - العنوان
        row3 = tk.Frame(info_frame, bg='white')
        row3.pack(fill=tk.X, pady=8, padx=15)
        
        # العنوان
        address_frame = tk.Frame(row3, bg='white')
        address_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Label(address_frame, text="🏠 العنوان:", font=('Segoe UI', 11, 'bold'), 
                fg='#2c3e50', bg='white').pack(anchor='w')
        self.address_label = tk.Label(address_frame, text="-", font=('Segoe UI', 12), 
                                     fg='#2c3e50', bg='white')
        self.address_label.pack(anchor='w')
        
        # خط فاصل
        separator = tk.Frame(info_frame, bg='#ecf0f1', height=1)
        separator.pack(fill=tk.X, padx=15, pady=10)
        
        # الصف الرابع - المعلومات المالية
        row4 = tk.Frame(info_frame, bg='white')
        row4.pack(fill=tk.X, pady=8, padx=15)
        
        # حد الائتمان
        credit_frame = tk.Frame(row4, bg='white')
        credit_frame.pack(side=tk.LEFT, padx=(0, 30))
        tk.Label(credit_frame, text="💳 حد الائتمان:", font=('Segoe UI', 11, 'bold'), 
                fg='#2c3e50', bg='white').pack(anchor='w')
        self.credit_label = tk.Label(credit_frame, text="-", font=('Segoe UI', 12), 
                                    fg='#27ae60', bg='white')
        self.credit_label.pack(anchor='w')
        
        # الرصيد الحالي
        balance_frame = tk.Frame(row4, bg='white')
        balance_frame.pack(side=tk.LEFT, padx=(0, 30))
        tk.Label(balance_frame, text="💰 الرصيد الحالي:", font=('Segoe UI', 11, 'bold'), 
                fg='#2c3e50', bg='white').pack(anchor='w')
        self.balance_label = tk.Label(balance_frame, text="-", font=('Segoe UI', 12), 
                                     fg='#e74c3c', bg='white')
        self.balance_label.pack(anchor='w')
        
        # الائتمان المتاح
        available_frame = tk.Frame(row4, bg='white')
        available_frame.pack(side=tk.LEFT, padx=(0, 30))
        tk.Label(available_frame, text="💎 الائتمان المتاح:", font=('Segoe UI', 11, 'bold'), 
                fg='#2c3e50', bg='white').pack(anchor='w')
        self.available_label = tk.Label(available_frame, text="-", font=('Segoe UI', 12), 
                                       fg='#3498db', bg='white')
        self.available_label.pack(anchor='w')
    
    def load_customers(self):
        """تحميل العملاء"""
        try:
            # مسح الجدول
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # جلب العملاء من قاعدة البيانات
            self.customers = self.customer_manager.get_all_customers()
            
            # إضافة العملاء للجدول
            for customer in self.customers:
                self.tree.insert('', tk.END, values=(
                    customer.code,
                    customer.name,
                    customer.phone or '-',
                    customer.email or '-',
                    customer.city or '-',
                    f"{customer.credit_limit:.2f}",
                    f"{customer.current_balance:.2f}",
                    customer.credit_status
                ))
            
            # تحديث شريط الحالة
            self.update_status(f"تم تحميل {len(self.customers)} عميل")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العملاء: {str(e)}")
    
    def on_search_change(self, *args):
        """البحث في العملاء"""
        search_term = self.search_var.get().lower()
        
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # تصفية العملاء
        filtered_customers = []
        for customer in self.customers:
            if (search_term in customer.name.lower() or 
                search_term in customer.code.lower() or
                search_term in (customer.phone or '').lower() or
                search_term in (customer.email or '').lower()):
                filtered_customers.append(customer)
        
        # إضافة العملاء المفلترين
        for customer in filtered_customers:
            self.tree.insert('', tk.END, values=(
                customer.code,
                customer.name,
                customer.phone or '-',
                customer.email or '-',
                customer.city or '-',
                f"{customer.credit_limit:.2f}",
                f"{customer.current_balance:.2f}",
                customer.credit_status
            ))
    
    def on_customer_select(self, event):
        """عند اختيار عميل"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            customer_code = item['values'][0]
            
            # البحث عن العميل
            self.selected_customer = None
            for customer in self.customers:
                if customer.code == customer_code:
                    self.selected_customer = customer
                    break
            
            # تحديث التفاصيل
            self.update_customer_details()
    
    def update_customer_details(self):
        """تحديث تفاصيل العميل"""
        if self.selected_customer:
            self.code_label.config(text=self.selected_customer.code)
            self.name_label.config(text=self.selected_customer.name)
            self.phone_label.config(text=self.selected_customer.phone or '-')
            self.email_label.config(text=self.selected_customer.email or '-')
            self.address_label.config(text=self.selected_customer.address or '-')
            self.credit_label.config(text=f"{self.selected_customer.credit_limit:.2f} ج.م")
            self.balance_label.config(text=f"{self.selected_customer.current_balance:.2f} ج.م")
            self.available_label.config(text=f"{self.selected_customer.available_credit:.2f} ج.م")
        else:
            # مسح التفاصيل
            for label in [self.code_label, self.name_label, self.phone_label, 
                         self.email_label, self.address_label, self.credit_label, 
                         self.balance_label, self.available_label]:
                label.config(text="-")
    
    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self.window, self.db, self.config)
        if dialog.result:
            self.load_customers()
    
    def edit_customer(self):
        """تعديل عميل"""
        if not self.selected_customer:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return
        
        dialog = CustomerDialog(self.window, self.db, self.config, self.selected_customer)
        if dialog.result:
            self.load_customers()
    
    def delete_customer(self):
        """حذف عميل"""
        if not self.selected_customer:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", 
                             f"هل تريد حذف العميل '{self.selected_customer.name}'؟"):
            try:
                # حذف العميل (تعطيل بدلاً من الحذف الفعلي)
                query = "UPDATE customers SET is_active = 0 WHERE id = ?"
                self.db.execute_query(query, (self.selected_customer.id,))
                
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.load_customers()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف العميل: {str(e)}")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        # يمكن إضافة شريط حالة هنا
        pass

class CustomerDialog:
    """حوار إضافة/تعديل عميل"""
    
    def __init__(self, parent, db_manager, config, customer=None):
        """
        تهيئة حوار العميل
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
            config: إعدادات التطبيق
            customer: العميل للتعديل (None للإضافة)
        """
        self.parent = parent
        self.db = db_manager
        self.config = config
        self.customer = customer
        self.result = None
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إضافة عميل" if customer is None else "تعديل عميل")
        self.window.geometry("500x500")
        self.window.transient(parent)
        self.window.grab_set()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات إذا كان تعديل
        if self.customer:
            self.load_customer_data()
        
        # تشغيل النافذة
        self.window.wait_window()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.code_var = tk.StringVar()
        self.name_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.city_var = tk.StringVar()
        self.credit_limit_var = tk.DoubleVar()
        self.current_balance_var = tk.DoubleVar()
        self.notes_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar(value=True)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إطار النموذج
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.BOTH, expand=True)
        
        # الحقول
        row = 0
        
        # كود العميل
        ttk.Label(form_frame, text="كود العميل *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.code_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # اسم العميل
        ttk.Label(form_frame, text="اسم العميل *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.name_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الهاتف
        ttk.Label(form_frame, text="الهاتف:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.phone_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # البريد الإلكتروني
        ttk.Label(form_frame, text="البريد الإلكتروني:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.email_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # العنوان
        ttk.Label(form_frame, text="العنوان:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.address_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # المدينة
        ttk.Label(form_frame, text="المدينة:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.city_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # حد الائتمان
        ttk.Label(form_frame, text="حد الائتمان:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.credit_limit_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الرصيد الحالي
        ttk.Label(form_frame, text="الرصيد الحالي:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.current_balance_var, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # الملاحظات
        ttk.Label(form_frame, text="الملاحظات:").grid(row=row, column=0, sticky=tk.W, pady=5)
        notes_text = tk.Text(form_frame, width=30, height=3)
        notes_text.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # نشط
        ttk.Checkbutton(form_frame, text="عميل نشط", variable=self.is_active_var).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_customer).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.RIGHT)
        
        # حفظ مرجع للنص
        self.notes_text = notes_text
    
    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        if self.customer:
            self.code_var.set(self.customer.code)
            self.name_var.set(self.customer.name)
            self.phone_var.set(self.customer.phone)
            self.email_var.set(self.customer.email)
            self.address_var.set(self.customer.address)
            self.city_var.set(self.customer.city)
            self.credit_limit_var.set(self.customer.credit_limit)
            self.current_balance_var.set(self.customer.current_balance)
            self.notes_text.insert('1.0', self.customer.notes)
            self.is_active_var.set(self.customer.is_active)
    
    def save_customer(self):
        """حفظ العميل"""
        try:
            # إنشاء كائن العميل
            customer = Customer(
                id=self.customer.id if self.customer else None,
                code=self.code_var.get().strip(),
                name=self.name_var.get().strip(),
                phone=self.phone_var.get().strip(),
                email=self.email_var.get().strip(),
                address=self.address_var.get().strip(),
                city=self.city_var.get().strip(),
                credit_limit=self.credit_limit_var.get(),
                current_balance=self.current_balance_var.get(),
                notes=self.notes_text.get('1.0', tk.END).strip(),
                is_active=self.is_active_var.get()
            )
            
            # التحقق من صحة البيانات
            errors = customer.validate()
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return
            
            # حفظ في قاعدة البيانات
            customer_manager = CustomerManager(self.db)
            
            if self.customer:  # تعديل
                query = """
                    UPDATE customers SET 
                        code = ?, name = ?, phone = ?, email = ?, address = ?,
                        city = ?, credit_limit = ?, current_balance = ?, notes = ?,
                        is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """
                self.db.execute_query(query, (
                    customer.code, customer.name, customer.phone, customer.email,
                    customer.address, customer.city, customer.credit_limit,
                    customer.current_balance, customer.notes, customer.is_active,
                    self.customer.id
                ))
                messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
            else:  # إضافة
                customer_id = customer_manager.create_customer(customer)
                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ العميل: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.window.destroy()
