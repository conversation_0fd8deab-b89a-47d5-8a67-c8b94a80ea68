# برنامج الميزان - نظام إدارة المحال التجارية

## نظرة عامة

برنامج الميزان هو نظام شامل لإدارة المحال التجارية الصغيرة والمتوسطة، مصمم خصيصاً للتجار العرب. يوفر البرنامج جميع الأدوات اللازمة لإدارة المبيعات والمشتريات والمخزون والعملاء والموردين والتقارير المالية.

## المميزات الرئيسية

### 🏪 إدارة المبيعات والفواتير
- إنشاء فواتير بيع (نقدي / آجل)
- حساب الضرائب والخصومات تلقائياً
- طباعة الفواتير وإرسالها
- متابعة حالة الدفع
- إدارة المرتجعات

### 📦 إدارة المخزون
- إضافة وتعديل المنتجات
- تتبع كميات المخزون
- تنبيهات المخزون المنخفض
- إدارة فئات المنتجات
- دعم الباركود
- تقارير حركة المخزون

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء والموردين
- إدارة حدود الائتمان
- تتبع المديونيات
- سجل التعاملات التجارية
- تقارير العملاء والموردين

### 📊 التقارير والتحليلات
- تقارير المبيعات اليومية/الشهرية
- تقرير الأرباح والخسائر
- تقارير المخزون والنواقص
- تحليل أداء المنتجات
- تقارير العملاء والمديونيات

### ⚙️ المميزات الإضافية
- واجهة عربية أنيقة وسهلة الاستخدام
- نظام صلاحيات متعدد المستخدمين
- النسخ الاحتياطي التلقائي
- تصدير البيانات إلى Excel
- دعم العملات المتعددة (الجنيه المصري افتراضياً)

## متطلبات النظام

- نظام التشغيل: Windows 10 أو أحدث
- Python 3.8 أو أحدث
- ذاكرة: 4 جيجابايت RAM كحد أدنى
- مساحة القرص: 500 ميجابايت

## التثبيت والتشغيل

### 1. تحميل المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج

```bash
python main.py
```

### 3. تسجيل الدخول الأولي

- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## هيكل المشروع

```
Al-mezan/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المتطلبات
├── README.md              # التوثيق
├── src/                   # الكود المصدري
│   ├── database/          # إدارة قاعدة البيانات
│   ├── gui/              # واجهات المستخدم
│   ├── models/           # نماذج البيانات
│   ├── reports/          # نظام التقارير
│   └── utils/            # الأدوات المساعدة
├── data/                 # ملفات البيانات
├── assets/              # الصور والأيقونات
└── docs/               # التوثيق التفصيلي
```

## دليل الاستخدام السريع

### إضافة منتج جديد
1. اذهب إلى قائمة "المخزون" → "المنتجات"
2. اضغط على "إضافة منتج"
3. أدخل بيانات المنتج (الكود، الاسم، السعر، إلخ)
4. اضغط "حفظ"

### إنشاء فاتورة بيع
1. اذهب إلى قائمة "المبيعات" → "فاتورة بيع جديدة"
2. اختر العميل
3. أضف المنتجات والكميات
4. راجع الإجماليات
5. اضغط "حفظ"

### عرض التقارير
1. اذهب إلى قائمة "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اضغط "إنشاء التقرير"

## الإعدادات

يمكن تخصيص البرنامج من خلال:
- قائمة "الإعدادات" → "إعدادات عامة"
- تغيير معلومات المحل التجاري
- إعدادات الفواتير والضرائب
- إدارة المستخدمين والصلاحيات

## النسخ الاحتياطي

البرنامج يقوم بإنشاء نسخ احتياطية تلقائية في مجلد `data/backups/`

لإنشاء نسخة احتياطية يدوية:
1. اذهب إلى "الإعدادات" → "النسخ الاحتياطي"
2. اضغط "إنشاء نسخة احتياطية"

## الدعم الفني

للحصول على المساعدة:
- راجع دليل المستخدم في مجلد `docs/`
- تواصل مع فريق الدعم

## الترخيص

هذا البرنامج مطور بواسطة مساعد الذكي الاصطناعي لأغراض تعليمية وتجارية.

## تحديثات مستقبلية

### الإصدار القادم (v1.1)
- [ ] دعم الفواتير الإلكترونية
- [ ] تطبيق موبايل مصاحب
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير متقدمة مع رسوم بيانية
- [ ] نظام إشعارات متطور

### الإصدار v1.2
- [ ] دعم المتاجر متعددة الفروع
- [ ] نظام إدارة الموظفين
- [ ] تكامل مع منصات التجارة الإلكترونية
- [ ] ذكاء اصطناعي لتوقع المبيعات

## المساهمة في التطوير

نرحب بالمساهمات لتحسين البرنامج:
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التوثيق
4. اختبر التغييرات
5. أرسل Pull Request

## شكر وتقدير

شكر خاص لجميع المطورين والمختبرين الذين ساهموا في تطوير هذا البرنامج.

---

**برنامج الميزان - حلول ذكية لإدارة أعمالك التجارية** 🏪✨
