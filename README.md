# Al-Mezan Business Management System

A comprehensive business management system designed for small and medium retail businesses in the Arab region, with full Arabic language support.

## Key Features

### Inventory & Product Management
- Add, edit, and delete products
- Product categorization
- Stock level tracking
- Low stock alerts
- Purchase and sale price management
- Barcode & QR Code system

### Customer & Supplier Management
- Comprehensive customer database
- Supplier information management
- Transaction history tracking
- Account and debt management

### Advanced Invoice System
- Create sales and purchase invoices
- Automatic tax and discount calculation
- Print invoices and receipts
- Payment status tracking

### Reports & Analytics
- Daily and monthly sales reports
- Profit and loss analysis
- Inventory and movement reports
- Comprehensive performance statistics

### User Management & Permissions
- Secure login system
- User and role management
- Detailed user permissions
- Operations and audit log

### Additional Features
- **Automatic Backup**: Data protection with automatic scheduling
- **Data Export**: Export in CSV, JSON, Excel formats
- **Dark Mode**: Multiple color themes (light, dark, blue)
- **Barcode System**: Generate and read barcodes & QR codes
- **Arabic Interface**: Full Arabic language support

## Technologies Used

- **Python 3.8+**: Main programming language
- **Tkinter**: Graphical user interface
- **SQLite**: Local database
- **PBKDF2**: Password encryption
- **Schedule**: Task scheduling

### Optional Libraries for Advanced Features:
```bash
# For Barcode & QR Code
pip install python-barcode[images] qrcode[pil] pyzbar

# For Excel Export
pip install openpyxl

# For Scheduled Backups
pip install schedule
```

## Installation & Running

### Requirements
- Python 3.8 or newer
- Operating System: Windows, macOS, Linux

### Installation Steps

1. **Run the program**:
```bash
python main.py
```

### First Run
- Database will be created automatically
- Default user: `admin`
- Default password: `admin123`
- **Important**: Change password immediately after first login

## 🎨 Enhanced Login Window

The login window has been completely redesigned with modern, professional features:

### **Visual Design:**
- ✅ **Modern Design** without default title bar
- ✅ **Custom Title Bar** with app icon and control buttons
- ✅ **Consistent Color Scheme** with professional gradients
- ✅ **Window Shadow** for visual depth
- ✅ **Clear Typography** using Segoe UI font

### **Interactive Effects:**
- ✅ **Fade-in Effect** when window opens
- ✅ **Fade-out Effect** on successful login
- ✅ **Shake Animation** for incorrect credentials
- ✅ **Field Color Changes** on focus (blue → green)
- ✅ **Button Hover Effects** for all interactive elements

### **Smart Features:**
- ✅ **Auto-enable Login Button** when credentials are entered
- ✅ **Dynamic Button Messages** ("Login" → "Logging in..." → "Success!")
- ✅ **Keyboard Shortcuts** (Enter to navigate/login)
- ✅ **Window Dragging** for repositioning
- ✅ **Minimize and Close Buttons** with hover effects

### **User Experience:**
- ✅ **Perfect Centering** on screen
- ✅ **Clear Default Credentials** display
- ✅ **Informative Error Messages** with visual feedback
- ✅ **Instant Visual Feedback** for all interactions

## Project Structure

```
al-mezan/
├── main.py                 # Entry point
├── requirements.txt        # Dependencies
├── README.md              # This file
├── src/                   # Source code
│   ├── database/          # Database management
│   ├── models/            # Data models
│   ├── gui/               # User interfaces
│   └── utils/             # Utilities
├── data/                  # Database files
├── backups/               # Backup files
├── exports/               # Export files
├── barcodes/              # Barcode images
└── qrcodes/               # QR Code images
```

## Security & Protection

- **Password Encryption**: Using PBKDF2 with Salt
- **Secure Sessions**: User session management
- **Detailed Permissions**: Precise access control
- **Operations Log**: Track all activities
- **Backup System**: Data protection

## Quick Usage Guide

### Login
1. Run the program
2. Enter username and password
3. Click "Login"

### Product Management
1. Choose "Products" from main menu
2. Click "Add New Product"
3. Fill required data
4. Save product

### Create Invoice
1. Choose "Invoices" from menu
2. Click "New Invoice"
3. Select customer and products
4. Save and print invoice

## Theme Customization

The system supports three color themes:
- **Light Mode**: Default design
- **Dark Mode**: Dark colors for eye comfort
- **Blue Mode**: Elegant blue gradients

To change theme:
1. Go to "Settings"
2. "System" tab
3. Select desired theme

## Data Export

Export data in the following formats:
- **CSV**: For Excel use
- **JSON**: For other applications
- **Excel**: .xlsx files with formatting

### Exportable Data Types:
- Products and inventory
- Customers and suppliers
- Invoices and sales
- Financial reports

## Troubleshooting

### Common Issues and Solutions:

**1. Database Error**:
```bash
# Delete database file and restart
rm data/almezan.db
python main.py
```

**2. Barcode Issues**:
```bash
# Install required libraries
pip install python-barcode[images] qrcode[pil]
```

**3. Export Issues**:
```bash
# For Excel support
pip install openpyxl
```

## Contributing

We welcome contributions! Please:
1. Fork the project
2. Create feature branch
3. Add improvements
4. Send Pull Request

## License

This project is licensed under the MIT License.

## Support & Contact

For technical support and inquiries, please contact us.

## Acknowledgments

Special thanks to all contributors and developers who helped develop this system.

---

**Al-Mezan** - Comprehensive Business Management System in Arabic
© 2024 All Rights Reserved
